import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { message } from "antd";

interface BulkNotificationPayload {
  title: string;
  message: string;
  type?: "info" | "success" | "warning" | "error";
  userIds?: number[];
  roleFilter?: string;
}

interface BulkNotificationResponse {
  success: boolean;
  message: string;
  sent: number;
  total: number;
  errors?: Array<{ userId: number; error: string }>;
}

export const useSendBulkNotification = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (payload: BulkNotificationPayload): Promise<BulkNotificationResponse> => {
      const { data } = await Http.post<BulkNotificationResponse>(
        "/api/notifications/send-bulk",
        payload
      );
      return data;
    },
    onSuccess: (data) => {
      // Invalidar las queries de notificaciones para refrescar la lista
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      
      // Mostrar mensaje de éxito
      if (data.errors && data.errors.length > 0) {
        message.warning(
          `${data.message}. ${data.errors.length} errores encontrados.`
        );
      } else {
        message.success(data.message);
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.error?.message || 
                          error?.message || 
                          "Error al enviar notificación masiva";
      message.error(errorMessage);
    },
  });

  return {
    sendBulkNotification: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
  };
};
