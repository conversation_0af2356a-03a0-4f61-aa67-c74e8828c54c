module.exports = {
    // Eliminamos el método me() porque está siendo manejado por la extensión del plugin
    // Mantenemos solo el método de actualización de usuario
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const { body } = ctx.request;
            console.log("[DEBUG] Updating user with ID:", id);
            console.log("[DEBUG] Update data:", JSON.stringify(body, null, 2));
            // Validate input
            if (!id) {
                return ctx.badRequest("Missing user ID");
            }
            // Update user with entityService
            const updatedUser = await strapi.entityService.update("plugin::users-permissions.user", id, {
                data: body,
                populate: ["role"],
            });
            // Remove sensitive fields
            delete updatedUser.password;
            delete updatedUser.resetPasswordToken;
            delete updatedUser.confirmationToken;
            console.log("[DEBUG] Updated user successfully");
            return ctx.send(updatedUser);
        }
        catch (error) {
            console.error("[ERROR] Error updating user:", error);
            return ctx.badRequest(null, error);
        }
    },
};
