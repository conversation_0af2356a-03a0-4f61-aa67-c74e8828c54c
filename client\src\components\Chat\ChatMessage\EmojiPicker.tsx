/**
 * Componente selector de emojis para reacciones
 */

import React from "react";
import type { EmojiPickerProps } from "@app/types/chat";
import * as S from "./ChatMessage.styles";

// Lista de emojis más comunes para reacciones
const COMMON_EMOJIS = [
  "👍",
  "👎",
  "❤️",
  "😂",
  "😮",
  "😢",
  "😡",
  "🎉",
  "👏",
  "🔥",
  "💯",
  "✅",
  "❌",
  "⭐",
  "💡",
  "🤔",
  "😊",
  "😍",
  "🥳",
  "😎",
  "🤝",
  "🙏",
  "💪",
  "👌",
  "😴",
  "🤯",
  "🥺",
  "😬",
  "🤷",
  "🙄",
  "😇",
  "🤗",
];

export const EmojiPicker: React.FC<EmojiPickerProps> = ({
  onEmojiSelect,
  onVisibleChange,
}) => {
  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    if (onVisibleChange) {
      onVisibleChange(false);
    }
  };

  return (
    <S.EmojiPickerContainer>
      {COMMON_EMOJIS.map((emoji) => (
        <S.EmojiButton
          key={emoji}
          onClick={() => handleEmojiClick(emoji)}
          title={`Reaccionar con ${emoji}`}
        >
          {emoji}
        </S.EmojiButton>
      ))}
    </S.EmojiPickerContainer>
  );
};
