import { Typography } from "antd";

interface DebugInfoProps {
  data: any;
  title?: string;
  show?: boolean;
}

/**
 * Componente para mostrar información de depuración
 * Solo se muestra en entornos de desarrollo (NODE_ENV !== 'production')
 */
const DebugInfo: React.FC<DebugInfoProps> = ({
  data,
  title = "Información de depuración",
  show = process.env.NODE_ENV !== "production",
}) => {
  if (!show) return null;

  return (
    <div
      style={{
        marginBottom: "16px",
        padding: "10px",
        background: "#f0f0f0",
        borderRadius: "4px",
        width: "100%",
        position: "relative",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "8px",
        }}
      >
        <Typography.Text strong>{title}</Typography.Text>
        <Typography.Text type="secondary" style={{ fontSize: "12px" }}>
          (Solo visible en desarrollo)
        </Typography.Text>
      </div>
      <pre style={{ fontSize: "12px", overflowX: "auto", margin: 0 }}>
        {typeof data === "string" ? data : JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
};

export default DebugInfo;
