"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// Función para forzar la salida de logs
const log = (...args) => {
    const message = args
        .map((arg) => typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg))
        .join(" ");
    process.stdout.write(`[${new Date().toISOString()}] ${message}\n`);
};
// Rutas de los archivos de plantilla
const TEMPLATES_DIR = path_1.default.join(process.cwd(), "config", "email-templates");
const RESET_PASSWORD_PATH = path_1.default.join(TEMPLATES_DIR, "reset-password.html");
const EMAIL_CONFIRMATION_PATH = path_1.default.join(TEMPLATES_DIR, "email-confirmation.html");
// Función para leer el contenido de una plantilla HTML
const readTemplateContent = (filePath) => {
    try {
        return fs_1.default.readFileSync(filePath, "utf8");
    }
    catch (error) {
        log(`❌ Error al leer la plantilla ${filePath}:`, error);
        return "";
    }
};
// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent) => {
    // Extraer solo el contenido relevante para el mensaje
    // En este caso, vamos a extraer todo lo que está dentro del body
    const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
        // Simplificar el contenido para la interfaz de Strapi
        let content = bodyMatch[1].trim();
        // Reemplazar las variables de plantilla al formato que espera Strapi
        content = content.replace(/<%=\s*user\.username\s*%>/g, "<%= user.username %>");
        content = content.replace(/<%=\s*URL\s*%>/g, "<%= URL %>");
        content = content.replace(/<%=\s*TOKEN\s*%>/g, "<%= TOKEN %>");
        content = content.replace(/<%=\s*CODE\s*%>/g, "<%= CODE %>");
        return content;
    }
    return "";
};
// Función principal para actualizar las plantillas en la interfaz de Strapi
const updateEmailTemplatesUI = async ({ strapi }) => {
    try {
        log("🔄 INICIANDO: Actualización de plantillas en la interfaz de Strapi");
        // Verificar que las plantillas existan
        if (!fs_1.default.existsSync(RESET_PASSWORD_PATH) || !fs_1.default.existsSync(EMAIL_CONFIRMATION_PATH)) {
            log("❌ Error: Las plantillas HTML no existen en el directorio config/email-templates/");
            log("📋 Ejecuta primero el script setup-email-templates.js para crear las plantillas");
            return;
        }
        // Leer las plantillas
        const resetPasswordHtml = readTemplateContent(RESET_PASSWORD_PATH);
        const emailConfirmationHtml = readTemplateContent(EMAIL_CONFIRMATION_PATH);
        // Extraer el contenido del mensaje
        const resetPasswordMessage = extractMessageBody(resetPasswordHtml);
        const emailConfirmationMessage = extractMessageBody(emailConfirmationHtml);
        // Actualizar las plantillas en la base de datos de Strapi
        log("📝 Actualizando plantilla de restablecimiento de contraseña en Strapi...");
        try {
            // En Strapi v5, usamos el servicio de email directamente
            await strapi.plugin("email").service("email").updateTemplate("reset-password", {
                subject: "Restablecer contraseña",
                message: resetPasswordMessage,
            });
            log("✅ Plantilla de restablecimiento de contraseña actualizada correctamente");
        }
        catch (error) {
            log("❌ Error al actualizar plantilla de restablecimiento de contraseña:", error.message);
        }
        log("📝 Actualizando plantilla de confirmación de correo en Strapi...");
        try {
            // En Strapi v5, usamos el servicio de email directamente
            await strapi.plugin("email").service("email").updateTemplate("email-confirmation", {
                subject: "Confirmación de cuenta",
                message: emailConfirmationMessage,
            });
            log("✅ Plantilla de confirmación de correo actualizada correctamente");
        }
        catch (error) {
            log("❌ Error al actualizar plantilla de confirmación de correo:", error.message);
        }
        log("✅ Plantillas actualizadas correctamente en la interfaz de Strapi");
        log("🔍 Ahora deberías poder ver las plantillas actualizadas en la interfaz de administración");
        log("📋 Nota: Es posible que necesites refrescar la página de administración para ver los cambios");
    }
    catch (error) {
        log("❌ ERROR: Fallo en la actualización de plantillas");
        log("🔧 Detalles del error:", error.message);
        log("📌 Stack:", error.stack);
    }
};
// Exportar la función para ser usada por Strapi
exports.default = async ({ strapi }) => {
    // Ejecutar la actualización
    await updateEmailTemplatesUI({ strapi });
};
