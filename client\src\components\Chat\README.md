# Sistema de Chat Comunitario

Un sistema completo de chat en tiempo real para la comunicación entre residentes del conjunto residencial.

## 🚀 Características

### ✅ Funcionalidades Implementadas

- **Mensajes en tiempo real** vía WebSocket
- **Canales temáticos** (General, Avisos, Compra/Venta, Mascotas, Eventos)
- **Reacciones con emojis** en mensajes
- **Menciones de usuarios** (@username)
- **Respuestas a mensajes** (threading)
- **Edición y eliminación** de mensajes propios
- **Búsqueda de mensajes** en canales
- **Indicadores de escritura** en tiempo real
- **Presencia de usuarios** (online/offline)
- **Sistema de permisos** por roles
- **Moderación de contenido** para administradores
- **Interfaz responsive** para móviles y desktop

### 🔐 Sistema de Permisos

- **Residentes/Arrendatarios**: <PERSON><PERSON><PERSON> leer y escribir en canales públicos
- **Vigilantes**: Solo lectura para estar informados
- **Consejeros/Admins**: Moderación completa + canal exclusivo de avisos

## 📁 Estructura del Proyecto

```
client/src/components/Chat/
├── ChatContainer/           # Componente principal
├── ChatChannelList/         # Lista de canales
├── ChatMessageList/         # Lista de mensajes
├── ChatMessage/             # Mensaje individual
├── ChatInput/               # Input para escribir
├── ChatHeader/              # Header del canal
├── index.ts                 # Exportaciones
├── theme.ts                 # Configuración de tema
└── README.md               # Esta documentación

client/src/hooks/
├── useChat.ts              # Hook principal del chat
└── useWebSocketChat.ts     # Hook para WebSocket

client/src/services/
└── chat.service.ts         # Servicio API

client/src/types/
└── chat.ts                 # Tipos TypeScript

client/src/pages/Chat/
└── ChatPage.tsx            # Página principal
```

## 🔧 Uso

### Importar el componente principal

```tsx
import { ChatContainer } from "@app/components/Chat";

function App() {
  return <ChatContainer />;
}
```

### Usar el hook de chat

```tsx
import { useChat } from '@app/hooks/useChat';

function MyComponent() {
  const {
    channels,
    selectedChannel,
    messages,
    sendMessage,
    selectChannel,
  } = useChat();

  return (
    // Tu componente
  );
}
```

### Usar el servicio API

```tsx
import { chatService } from "@app/services/chat.service";

// Obtener canales
const channels = await chatService.getChannels();

// Enviar mensaje
await chatService.sendMessage({
  channelId: 1,
  content: "Hola mundo!",
  messageType: "text",
});
```

## 🎨 Personalización

### Tema

El sistema usa un tema personalizable definido en `theme.ts`:

```tsx
import { chatTheme } from "@app/components/Chat/theme";

// Colores principales
chatTheme.colors.primary = "#1890ff";
chatTheme.colors.success = "#52c41a";

// Espaciado
chatTheme.spacing.md = "12px";

// Bordes
chatTheme.borderRadius.md = "6px";
```

### Estilos

Cada componente tiene sus propios estilos en archivos `.styles.ts` usando styled-components:

```tsx
// Ejemplo de personalización
const CustomChatContainer = styled(ChatContainer)`
  background-color: #f0f0f0;
  border-radius: 8px;
`;
```

## 🔌 WebSocket

El sistema usa WebSocket para comunicación en tiempo real:

### Eventos que maneja

- `chat_message` - Nuevo mensaje
- `chat_reaction_updated` - Reacción agregada/quitada
- `chat_typing` - Usuario escribiendo
- `chat_user_presence` - Usuario conectado/desconectado
- `chat_message_updated` - Mensaje editado
- `chat_message_deleted` - Mensaje eliminado
- `chat_message_moderated` - Mensaje moderado

### Configuración

```tsx
import { useWebSocketChat } from "@app/hooks/useWebSocketChat";

const { isConnected, sendTypingIndicator } = useWebSocketChat({
  onMessageReceived: (message) => {
    console.log("Nuevo mensaje:", message);
  },
  onTypingUpdate: (typing) => {
    console.log("Usuario escribiendo:", typing);
  },
});
```

## 📱 Responsive

El sistema es completamente responsive:

- **Desktop**: Layout de 2 columnas (canales + chat)
- **Tablet**: Layout adaptativo con sidebar colapsable
- **Mobile**: Vista de una sola columna con navegación por pestañas

## 🧪 Testing

Para probar el sistema:

1. **Backend**: Asegúrate de que el servidor Strapi esté corriendo
2. **WebSocket**: Verifica que el WebSocket esté configurado correctamente
3. **Permisos**: Prueba con diferentes roles de usuario
4. **Tiempo real**: Abre múltiples pestañas para probar la sincronización

## 🐛 Troubleshooting

### Problemas comunes

1. **WebSocket no conecta**

   - Verifica la URL del WebSocket en la configuración
   - Asegúrate de que el token de autenticación sea válido

2. **Mensajes no aparecen**

   - Revisa los permisos del usuario en el canal
   - Verifica que el canal esté activo

3. **Errores de API**
   - Confirma que el backend esté corriendo
   - Revisa los logs del servidor para errores

### Logs útiles

```tsx
// Habilitar logs de debug
localStorage.setItem("chat-debug", "true");

// Ver estado del WebSocket
console.log("WebSocket state:", wsRef.current?.readyState);

// Ver mensajes recibidos
console.log("Messages:", messages);
```

## 🚀 Próximas Funcionalidades

- [ ] Archivos adjuntos (imágenes, documentos)
- [ ] Mensajes privados entre usuarios
- [ ] Notificaciones push
- [ ] Historial de mensajes con paginación infinita
- [ ] Temas personalizables
- [ ] Comandos de chat (/help, /clear, etc.)
- [ ] Integración con sistema de notificaciones existente
- [ ] Chat de voz/video
- [ ] Bots automatizados

## 📄 Licencia

Este sistema es parte del proyecto CC Palmas de Canarias 1 y está sujeto a las mismas condiciones de licencia del proyecto principal.
