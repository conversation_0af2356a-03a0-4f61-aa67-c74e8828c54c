{"kind": "collectionType", "collectionName": "up_users", "info": {"singularName": "user", "pluralName": "users", "displayName": "User", "description": "Modelo de usuario que almacena información personal y de autenticación"}, "options": {"draftAndPublish": false}, "pluginOptions": {"users-permissions": {"subject": true}}, "attributes": {"push_subscription": {"type": "relation", "relation": "oneToOne", "target": "api::push-subscription.push-subscription", "inversedBy": "user"}, "username": {"type": "string", "configurable": false, "minLength": 3, "required": true, "unique": true}, "email": {"type": "email", "configurable": false, "minLength": 6, "required": true, "unique": true}, "password": {"type": "password", "configurable": false, "minLength": 6, "private": true, "searchable": false}, "provider": {"type": "string", "configurable": false}, "confirmed": {"type": "boolean", "configurable": false, "default": false}, "blocked": {"type": "boolean", "configurable": false, "default": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "firstName": {"type": "string", "required": true}, "lastName": {"type": "string", "required": true}, "address": {"type": "string"}, "profession": {"type": "string"}, "phone": {"type": "json"}, "imgUrl": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "signature": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "coefficient": {"type": "decimal"}, "status": {"type": "boolean", "default": true, "description": "true = al día, false = moroso"}, "isPublic": {"type": "boolean", "default": false, "description": "true = perfil público visible en directorio, false = perfil privado"}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "dependents": {"type": "relation", "relation": "oneToMany", "target": "api::dependent.dependent", "mappedBy": "users_permissions_user"}, "pets": {"type": "relation", "relation": "oneToMany", "target": "api::pet.pet", "mappedBy": "owner"}, "vehicles": {"type": "relation", "relation": "oneToMany", "target": "api::vehicle.vehicle", "mappedBy": "owner"}, "payments": {"type": "relation", "relation": "oneToMany", "target": "api::payment.payment", "mappedBy": "owner"}, "sanctions": {"type": "relation", "relation": "oneToMany", "target": "api::sanction.sanction", "mappedBy": "sanctionsUser"}, "rentals": {"type": "relation", "relation": "oneToMany", "target": "api::rental.rental", "mappedBy": "user"}, "visits": {"type": "relation", "relation": "oneToMany", "target": "api::visit.visit", "mappedBy": "user"}, "reservations": {"type": "relation", "relation": "oneToMany", "target": "api::reservation.reservation", "mappedBy": "owner"}, "pool_accesses": {"type": "relation", "relation": "oneToMany", "target": "api::pool-access.pool-access", "mappedBy": "user"}, "pool_sanctions": {"type": "relation", "relation": "oneToMany", "target": "api::pool-sanction.pool-sanction", "mappedBy": "user"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "author"}, "comments": {"type": "relation", "relation": "oneToMany", "target": "api::comment.comment", "mappedBy": "author"}, "likedComments": {"type": "relation", "relation": "manyToMany", "target": "api::comment.comment", "mappedBy": "likes"}, "dislikedComments": {"type": "relation", "relation": "manyToMany", "target": "api::comment.comment", "mappedBy": "dislikes"}, "ledger": {"type": "relation", "relation": "oneToMany", "target": "api::ledger.ledger", "mappedBy": "owner"}, "complaints": {"type": "relation", "relation": "oneToMany", "target": "api::complaint.complaint", "mappedBy": "user"}, "warnings": {"type": "relation", "relation": "oneToMany", "target": "api::warning.warning", "mappedBy": "resident"}, "frequent_visitors": {"type": "relation", "relation": "oneToMany", "target": "api::frequent-visitor.frequent-visitor", "mappedBy": "user"}, "legal_assistants": {"type": "relation", "relation": "oneToMany", "target": "api::legal-assistant.legal-assistant", "mappedBy": "user"}, "usuarioPrincipal": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "arrenda<PERSON><PERSON>"}, "arrendatarios": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "usuarioPrincipal"}, "activityLogs": {"type": "relation", "relation": "oneToMany", "target": "api::activity-log.activity-log", "mappedBy": "user"}}}