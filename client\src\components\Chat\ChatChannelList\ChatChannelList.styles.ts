/**
 * Estilos para el componente ChatChannelList
 */

import styled, { css } from 'styled-components';

interface ChannelItemProps {
  $isSelected: boolean;
  $hasUnread: boolean;
}

interface ChannelNameProps {
  $hasUnread: boolean;
}

export const ChannelListContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#fafafa' : '#141414'};
  border-right: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
`;

export const ChannelListHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.background};
  
  .ant-typography {
    margin: 0;
    color: ${({ theme }) => theme.textMain};
  }
`;

export const ChannelList = styled.div`
  flex: 1;
  overflow-y: auto;
  
  /* Estilos para el scrollbar */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
    border-radius: 2px;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#bfbfbf' : '#595959'};
    }
  }
`;

export const ChannelItem = styled.div<ChannelItemProps>`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  ${({ $isSelected, theme }) => $isSelected && css`
    background-color: ${theme.primary}15;
    border-right: 3px solid ${theme.primary};
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: ${theme.primary};
    }
  `}
  
  ${({ $hasUnread, theme }) => $hasUnread && css`
    background-color: ${theme.background === '#ffffff' ? '#f0f8ff' : '#0a1929'};
  `}

  &:hover {
    background-color: ${({ $isSelected, theme }) => 
      $isSelected 
        ? theme.primary + '20'
        : theme.background === '#ffffff' ? '#f5f5f5' : '#1f1f1f'
    };
  }

  &:active {
    transform: scale(0.98);
  }
`;

export const ChannelIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const ChannelInfo = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

export const ChannelHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
`;

export const ChannelName = styled.span<ChannelNameProps>`
  font-weight: ${({ $hasUnread }) => $hasUnread ? '600' : '500'};
  color: ${({ theme }) => theme.textMain};
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
`;

export const ChannelTime = styled.span`
  font-size: 11px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  flex-shrink: 0;
`;

export const ChannelPreview = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
  
  .ant-typography {
    margin: 0;
    font-size: inherit;
    color: inherit;
  }
`;

export const ChannelIndicators = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
`;

export const UnreadBadge = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  height: 16px;
  padding: 0 4px;
`;

export const ReadOnlyIndicator = styled.span`
  font-size: 12px;
  opacity: 0.6;
`;

export const EmptyContainer = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
`;

export const ChannelSkeleton = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  
  .channel-icon-skeleton {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  .channel-info-skeleton {
    flex: 1;
    
    .channel-name-skeleton {
      height: 14px;
      width: 80%;
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
      border-radius: 4px;
      margin-bottom: 4px;
      animation: pulse 1.5s ease-in-out infinite;
    }
    
    .channel-preview-skeleton {
      height: 12px;
      width: 60%;
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
      border-radius: 4px;
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
`;

export const ChannelActions = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  ${ChannelItem}:hover & {
    opacity: 1;
  }
  
  .ant-btn {
    border: none;
    box-shadow: none;
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#ffffff' : '#1f1f1f'};
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
    }
  }
`;

export const ChannelDivider = styled.div`
  height: 1px;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  margin: 8px 16px;
`;

export const ChannelCategory = styled.div`
  padding: 8px 16px 4px 16px;
  font-size: 11px;
  font-weight: 600;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const OnlineIndicator = styled.div<{ $isOnline: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${({ $isOnline }) => $isOnline ? '#52c41a' : '#d9d9d9'};
  position: absolute;
  bottom: 0;
  right: 0;
  border: 2px solid ${({ theme }) => theme.background};
`;
