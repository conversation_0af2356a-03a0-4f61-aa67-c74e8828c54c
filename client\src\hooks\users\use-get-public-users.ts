import Http from "@app/config/http/http";
import { useQuery } from "@tanstack/react-query";

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  address: string | null;
  phone: string | null;
  documentId: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
  coefficient: number | null;
  status: string | null;
  profession: string | null;
  isPublic?: boolean;
  role?: {
    id: number;
    name: string;
    type: string;
    description: string;
  };
  imgUrl?: {
    url: string;
    formats?: {
      thumbnail?: {
        url: string;
      };
      small?: {
        url: string;
      };
    };
  };
}

interface UseGetPublicUsersProps {
  enabled?: boolean;
}

export const useGetPublicUsers = ({
  enabled = true,
}: UseGetPublicUsersProps = {}) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["public-users"],
    queryFn: async () => {
      const { data } = await Http.get<User[]>("/api/users/public");
      return data;
    },
    enabled,
  });

  return {
    users: data || [],
    isLoading,
    error,
  };
};
