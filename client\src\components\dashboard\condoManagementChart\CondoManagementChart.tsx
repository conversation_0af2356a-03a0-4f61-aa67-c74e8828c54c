import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { useTheme } from "styled-components";
import {
  BaseChart,
  BaseChartProps,
  getDefaultTooltipStyles,
} from "@app/components/common/charts/BaseChart";
import { useGetAllStatusUsers, type User } from "./use-get-all-status-users";

interface PaymentStatusData {
  alDia: number;
  enMora: number;
}

export const GraficoGestionCondominio = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { users, loading: isLoading, error } = useGetAllStatusUsers();

  // Estadísticas de pago
  const paymentStats = useMemo<PaymentStatusData>(() => {
    if (!users || users.length === 0) return { alDia: 0, enMora: 0 };

    let alDia = 0;
    let enMora = 0;
    users.forEach((user: User) => {
      if (user.status === true) alDia++;
      else if (user.status === false) enMora++;
    });

    return { alDia, enMora };
  }, [users]);

  // Opciones del gráfico
  const chartOptions: BaseChartProps["option"] = useMemo(
    () => ({
      backgroundColor: theme.colors?.background?.default || "#fff",
      tooltip: {
        ...getDefaultTooltipStyles(theme),
        trigger: "item",
        formatter: (params: any) => {
          const { name, value, percent } = params;
          return `
            <div style="font-weight: bold; margin-bottom: 5px">${name}</div>
            <div>Usuarios: ${value}</div>
            <div>Porcentaje: ${percent}%</div>
          `;
        },
      },
      legend: {
        orient: "horizontal",
        bottom: 0,
        data: ["Al día", "En mora"],
        textStyle: {
          color: theme.colors?.text?.primary || "#333",
        },
      },
      title: {
        text: "Estado de pagos del condominio",
        left: "center",
        textStyle: {
          color: theme.colors?.text?.primary || "#333",
          fontSize: 16,
          fontWeight: "bold",
        },
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "70%"],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: theme.colors?.background?.default || "#fff",
            borderWidth: 2,
          },
          label: {
            show: false,
            position: "center",
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 18,
              fontWeight: "bold",
              formatter: "{b}: {c} ({d}%)",
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: paymentStats.alDia,
              name: "Al día",
              itemStyle: {
                color: theme.colors?.success?.main || "#52c41a",
              },
            },
            {
              value: paymentStats.enMora,
              name: "En mora",
              itemStyle: {
                color: theme.colors?.error?.main || "#ff4d4f",
              },
            },
          ],
        },
      ],
    }),
    [theme, paymentStats],
  );

  if (error) {
    return (
      <div
        style={{
          height: "400px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: theme.colors?.error?.main || "#ff4d4f",
        }}
      >
        Error al cargar datos
      </div>
    );
  }

  return (
    <div style={{ height: "400px", position: "relative" }}>
      <BaseChart
        option={chartOptions}
        style={{ height: "100%" }}
        loading={isLoading}
      />
    </div>
  );
};
