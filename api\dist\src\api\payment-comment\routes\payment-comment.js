/**
 * payment-comment router
 */
module.exports = {
    routes: [
        {
            method: "GET",
            path: "/payment-comments",
            handler: "payment-comment.find",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        {
            method: "GET",
            path: "/payment-comments/:id",
            handler: "payment-comment.findOne",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        {
            method: "POST",
            path: "/payment-comments",
            handler: "payment-comment.create",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        {
            method: "PUT",
            path: "/payment-comments/:id",
            handler: "payment-comment.update",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        {
            method: "DELETE",
            path: "/payment-comments/:id",
            handler: "payment-comment.delete",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
    ],
};
