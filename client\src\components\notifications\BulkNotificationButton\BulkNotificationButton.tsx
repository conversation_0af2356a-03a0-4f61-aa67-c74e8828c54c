import { useState } from "react";
import { But<PERSON>, Tooltip, message } from "antd";
import { NotificationOutlined } from "@ant-design/icons";
import { useAppSelector } from "@app/hooks/reduxHooks";
import { BulkNotificationModal } from "../BulkNotificationModal/BulkNotificationModal";

interface BulkNotificationButtonProps {
  size?: "small" | "middle" | "large";
  type?: "default" | "primary" | "dashed" | "link" | "text";
}

export const BulkNotificationButton = ({
  size = "small",
  type = "default",
}: BulkNotificationButtonProps) => {
  const [modalOpen, setModalOpen] = useState(false);
  const user = useAppSelector((state) => state.user.user);

  // Verificar si el usuario tiene permisos para enviar notificaciones masivas
  const hasPermission = () => {
    if (!user?.role?.type) return false;

    const allowedRoles = ["admin", "consejero", "vigilante"];
    return allowedRoles.includes(user.role.type.toLowerCase());
  };

  // Si el usuario no tiene permisos, no mostrar el botón
  if (!hasPermission()) {
    return null;
  }

  return (
    <>
      <Tooltip title="Enviar notificación a múltiples usuarios">
        <Button
          size={size}
          type={type}
          icon={<NotificationOutlined />}
          onClick={() => setModalOpen(true)}
        >
          Notificación Masiva
        </Button>
      </Tooltip>

      <BulkNotificationModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
      />
    </>
  );
};
