/**
 * Tipos TypeScript para el sistema de chat - Frontend
 */

export interface ChatChannel {
  id: number;
  documentId?: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  isActive: boolean;
  isPublic: boolean;
  allowedRoles: string[];
  moderatorRoles: string[];
  order: number;
  maxMessages: number;
  lastActivity?: string;
  createdAt: string;
  updatedAt: string;
  messageCount?: number;
  lastMessage?: ChatMessage | null;
  canModerate?: boolean;
  canWrite?: boolean;
}

export interface ChatMessage {
  id: number;
  documentId?: string;
  content: string;
  messageType: "text" | "image" | "file" | "system";
  isEdited: boolean;
  editedAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  reactions: Record<string, number[]>; // emoji -> array of user IDs
  mentions: ChatMention[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  channel?: {
    id: number;
    name: string;
    icon: string;
  };
  author: {
    id: number;
    username: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
  };
  replyTo?: {
    id: number;
    content: string;
    author: {
      username: string;
      firstName?: string;
      lastName?: string;
    };
  };
  attachments?: ChatAttachment[];
}

export interface ChatAttachment {
  id: number;
  name: string;
  url: string;
  size: number;
  mimeType: string;
  type: "image" | "document" | "video" | "audio";
}

export interface ChatMention {
  userId: number;
  username: string;
  displayName: string;
  position: number;
}

export interface ChatTypingIndicator {
  channelId: number;
  userId: number;
  username: string;
  isTyping: boolean;
}

export interface ChatUserPresence {
  userId: number;
  username: string;
  isOnline: boolean;
  lastSeen?: string;
}

// WebSocket message types
export interface ChatWebSocketMessage {
  type:
    | "chat_message"
    | "chat_reaction_updated"
    | "chat_typing"
    | "chat_user_presence"
    | "chat_message_updated"
    | "chat_message_deleted"
    | "chat_message_moderated";
  data: any;
  timestamp: string;
}

// API Request types
export interface CreateChatMessageRequest {
  channelId: number;
  content: string;
  messageType?: "text" | "image" | "file";
  replyTo?: number;
  mentions?: ChatMention[];
  attachments?: File[];
}

export interface UpdateChatMessageRequest {
  content: string;
}

export interface AddReactionRequest {
  emoji: string;
}

// API Response types
export interface ChatMessagesResponse {
  data: ChatMessage[];
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
    };
    channelId: number;
    channelName: string;
  };
}

export interface ChatChannelsResponse {
  data: ChatChannel[];
  meta: {
    userRole: string;
    totalChannels: number;
  };
}

export interface ChatChannelResponse {
  data: ChatChannel & {
    canModerate: boolean;
    canWrite: boolean;
    messages: ChatMessage[];
  };
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
    };
  };
}

// UI State types
export interface ChatUIState {
  selectedChannelId: number | null;
  isLoading: boolean;
  error: string | null;
  typingUsers: Record<number, ChatTypingIndicator[]>; // channelId -> typing users
  onlineUsers: Record<number, boolean>; // userId -> isOnline
  replyingTo: ChatMessage | null;
  editingMessage: ChatMessage | null;
  searchQuery: string;
  searchResults: ChatMessage[];
}

// Hook return types
export interface UseChatReturn {
  // State
  channels: ChatChannel[];
  selectedChannel: ChatChannel | null;
  messages: ChatMessage[];
  uiState: ChatUIState;

  // Actions
  selectChannel: (channelId: number) => void;
  sendMessage: (content: string, replyTo?: number) => Promise<void>;
  editMessage: (messageId: number, content: string) => Promise<void>;
  deleteMessage: (messageId: number) => Promise<void>;
  addReaction: (messageId: number, emoji: string) => Promise<void>;
  searchMessages: (query: string) => Promise<void>;
  setReplyingTo: (message: ChatMessage | null) => void;
  setEditingMessage: (message: ChatMessage | null) => void;

  // Loading states
  isLoadingChannels: boolean;
  isLoadingMessages: boolean;
  isSendingMessage: boolean;
}

export interface UseWebSocketChatReturn {
  isConnected: boolean;
  connectionError: string | null;
  sendTypingIndicator: (channelId: number, isTyping: boolean) => void;
}

// Component Props types
export interface ChatContainerProps {
  className?: string;
  style?: React.CSSProperties;
}

export interface ChatChannelListProps {
  channels: ChatChannel[];
  selectedChannelId: number | null;
  onChannelSelect: (channelId: number) => void;
  loading?: boolean;
}

export interface ChatMessageListProps {
  messages: ChatMessage[];
  loading?: boolean;
  onReply: (message: ChatMessage) => void;
  onEdit: (message: ChatMessage) => void;
  onDelete: (messageId: number) => void;
  onReaction: (messageId: number, emoji: string) => void;
  currentUserId: number;
}

export interface ChatInputProps {
  onSendMessage: (content: string, attachments?: File[]) => void;
  replyingTo?: ChatMessage | null;
  onCancelReply?: () => void;
  disabled?: boolean;
  placeholder?: string;
  channelId: number;
}

export interface ChatMessageProps {
  message: ChatMessage;
  currentUserId: number;
  onReply: (message: ChatMessage) => void;
  onEdit: (message: ChatMessage) => void;
  onDelete: (messageId: number) => void;
  onReaction: (messageId: number, emoji: string) => void;
  showAvatar?: boolean;
  compact?: boolean;
}

export interface ChatHeaderProps {
  channel: ChatChannel | null;
  onlineCount?: number;
  typingUsers?: ChatTypingIndicator[];
  isConnected?: boolean;
  lastUpdated?: Date;
}

// Error types
export interface ChatError {
  message: string;
  code?: string;
  details?: any;
}

// Emoji picker types
export interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

// Search types
export interface ChatSearchProps {
  onSearch: (query: string) => void;
  results: ChatMessage[];
  loading?: boolean;
  onResultClick: (message: ChatMessage) => void;
}

// Typing indicator types
export interface TypingIndicatorProps {
  typingUsers: ChatTypingIndicator[];
  maxDisplay?: number;
}

// User presence types
export interface UserPresenceProps {
  userId: number;
  isOnline: boolean;
  size?: "small" | "default" | "large";
  showText?: boolean;
}

// Message reactions types
export interface MessageReactionsProps {
  reactions: Record<string, number[]>;
  currentUserId: number;
  onReactionClick: (emoji: string) => void;
  onReactionAdd: () => void;
}

// File upload types
export interface ChatFileUploadProps {
  onFileSelect: (files: File[]) => void;
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
}

// Mention types
export interface MentionSuggestion {
  id: number;
  username: string;
  displayName: string;
  avatar?: string;
}

export interface MentionInputProps {
  value: string;
  onChange: (value: string, mentions: ChatMention[]) => void;
  placeholder?: string;
  onSuggestionsFetch: (query: string) => Promise<MentionSuggestion[]>;
  disabled?: boolean;
}
