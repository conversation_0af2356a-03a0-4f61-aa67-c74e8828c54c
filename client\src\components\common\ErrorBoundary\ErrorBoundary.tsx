import { Component, ErrorInfo, ReactNode } from "react";
import { Result, Button, Typography, message } from "antd";
import { useTranslation } from "react-i18next";

const { Paragraph, Text } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  t: any;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: string | null;
}

export const ErrorBoundaryWithTranslation = (props: Omit<Props, "t">) => {
  const { t } = useTranslation();
  return <ErrorBoundaryComponent {...props} t={t} />;
};

class ErrorBoundaryComponent extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
    this.setState({ errorInfo: errorInfo.componentStack || null });
  }

  private handleCopyError = () => {
    const { error, errorInfo } = this.state;
    if (error) {
      const errorText = `Error: ${error.message}\n\nStack trace:\n${
        errorInfo || "No stack trace available"
      }`;
      navigator.clipboard.writeText(errorText).then(() => {
        message.success("Detalles del error copiados al portapapeles");
      });
    }
  };

  public render() {
    const { hasError, error, errorInfo } = this.state;
    const { t, fallback, children } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <Result
          status="error"
          title={t("common.error", "Ocurrió un error")}
          subTitle={t("common.errorMessage", "Lo sentimos, algo salió mal.")}
          extra={[
            error && (
              <Paragraph key="error-message">
                <Text strong>
                  {t("common.errorDetails", "Detalles del error")}:
                </Text>
                <Text type="danger">{error.message}</Text>
              </Paragraph>
            ),
            errorInfo && (
              <Paragraph key="error-info" style={{ whiteSpace: "pre-wrap" }}>
                <Text code>{errorInfo}</Text>
              </Paragraph>
            ),
            <Button type="primary" onClick={this.handleCopyError} key="copy">
              {t("common.copyError", "Copiar error")}
            </Button>,
            <Button
              type="primary"
              onClick={() => window.location.reload()}
              key="reload"
            >
              {t("common.reload", "Recargar página")}
            </Button>,
          ].filter(Boolean)}
        />
      );
    }

    return children;
  }
}

export const ErrorBoundary = ErrorBoundaryWithTranslation;
