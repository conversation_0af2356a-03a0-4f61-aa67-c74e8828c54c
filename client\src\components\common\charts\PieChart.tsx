import { EChartsOption } from "echarts-for-react";
import { useTheme } from "styled-components";
import {
  BaseChart,
  BaseChartProps,
} from "@app/components/common/charts/BaseChart";

interface PieChartProps extends BaseChartProps {
  option?: EChartsOption;

  data?: any;
  name?: string;
  showLegend?: boolean;
}

export const PieChart: React.FC<PieChartProps> = ({
  option,
  data,
  name,
  showLegend,
  ...props
}) => {
  const theme = useTheme();

  const defaultPieOption = {
    toolbox: {
      show: true,
      feature: {
        dataView: { readOnly: false },
        restore: {},
        saveAsImage: {},
      },
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      show: showLegend,
      top: "0%",
      left: 16,
      textStyle: {
        color: theme.textMain,
      },
    },
    series: [
      {
        name,
        type: "pie",
        top: showLegend ? "25%" : "10%",
        bottom: "5%",
        radius: ["55%", "100%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: theme.white,
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "40",
            fontWeight: "bold",
            color: theme.textMain,
          },
        },
        labelLine: {
          show: false,
        },
        data,
      },
    ],
  };
  return <BaseChart {...props} option={{ ...defaultPieOption, ...option }} />;
};
