import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import { BaseForm } from "@app/components/common/forms/BaseForm/BaseForm";
import * as Auth from "@app/components/layouts/AuthLayout/AuthLayout.styles";
import { useAppDispatch } from "@app/hooks/reduxHooks";
import { useFeedback } from "@app/hooks/useFeedback";
import { doLogin } from "@app/store/slices/authSlice";

import * as S from "./LoginForm.styles";

interface LoginFormData {
  identifier: string;
  password: string;
}

export const initValues: LoginFormData = {
  identifier: "",
  password: "",
};

export const LoginForm = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { notification } = useFeedback();

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (values: LoginFormData) => {
    setIsLoading(true);
    dispatch(doLogin(values))
      .unwrap()
      .then(() => navigate("/dashboard"))
      .catch((err) => {
        notification.error({ message: err.message });
        setIsLoading(false);
      });
  };

  return (
    <Auth.FormWrapper>
      <BaseForm
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark="optional"
        initialValues={initValues}
        autoComplete="off"
      >
        <Auth.FormTitle>{t("common.login")}</Auth.FormTitle>
        <S.LoginDescription>{t("login.loginInfo")}</S.LoginDescription>
        <Auth.FormItem
          name="identifier"
          label={t("common.email")}
          rules={[
            { required: true, message: t("common.requiredField") },
            {
              type: "email",
              message: t("common.notValidEmail"),
            },
          ]}
        >
          <Auth.FormInput placeholder={t("common.email")} />
        </Auth.FormItem>
        <Auth.FormItem
          label={t("common.password")}
          name="password"
          rules={[{ required: true, message: t("common.requiredField") }]}
        >
          <Auth.FormInputPassword placeholder={t("common.password")} />
        </Auth.FormItem>
        <Auth.ActionsWrapper>
          <BaseForm.Item name="rememberMe" valuePropName="checked" noStyle>
            <Auth.FormCheckbox>
              <S.RememberMeText>{t("login.rememberMe")}</S.RememberMeText>
            </Auth.FormCheckbox>
          </BaseForm.Item>
          <Link to="/auth/forgot-password">
            <S.ForgotPasswordText>
              {t("common.forgotPass")}
            </S.ForgotPasswordText>
          </Link>
        </Auth.ActionsWrapper>
        <BaseForm.Item noStyle>
          <Auth.SubmitButton
            type="primary"
            htmlType="submit"
            loading={isLoading}
          >
            {t("common.login")}
          </Auth.SubmitButton>
        </BaseForm.Item>
        <Auth.FooterWrapper>
          <Auth.Text>
            ¿No tienes una cuenta? Crea una
            <Link to="/auth/sign-up">
              <Auth.LinkText> aquí</Auth.LinkText>
            </Link>
          </Auth.Text>
        </Auth.FooterWrapper>
      </BaseForm>
    </Auth.FormWrapper>
  );
};
