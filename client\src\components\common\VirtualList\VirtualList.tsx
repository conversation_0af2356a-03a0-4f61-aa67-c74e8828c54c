import { useRef, useEffect } from "react";
import { List } from "antd";
import VirtualList from "rc-virtual-list";

interface VirtualizedListProps {
  data: any[];
  itemHeight: number;
  height: number;
  children: React.ReactNode;
}

export const VirtualizedList = ({
  data,
  itemHeight,
  height,
  children,
}: VirtualizedListProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, [data]);

  return (
    <List>
      <VirtualList
        data={data}
        height={height}
        itemHeight={itemHeight}
        itemKey="id"
      >
        {() => children}
      </VirtualList>
    </List>
  );
};
