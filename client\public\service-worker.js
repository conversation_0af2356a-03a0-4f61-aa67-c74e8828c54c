/* eslint-disable no-restricted-globals */

// Evento de instalación del Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker instalado');
  self.skipWaiting();
});

// Evento de activación del Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker activado');
  event.waitUntil(clients.claim());
});

// Manejar notificaciones push
self.addEventListener('push', (event) => {
  if (!event.data) {
    console.log('Push recibido sin datos');
    return;
  }

  try {
    const data = event.data.json();
    const options = {
      body: data.message || 'Nueva notificación',
      icon: '/logo192.png',
      badge: '/badge-icon.png',
      vibrate: [100, 50, 100],
      data: {
        url: data.url || '/',
        ...data.data
      },
      actions: data.actions || [],
      requireInteraction: true
    };

    event.waitUntil(
      self.registration.showNotification(data.title || 'Nueva notificación', options)
    );
  } catch (error) {
    console.error('Error al procesar la notificación push:', error);
  }
});

// Manejar clics en las notificaciones
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then((clientList) => {
        // Buscar si ya hay una ventana abierta con la URL
        const hadWindowToFocus = clientList.some((client) => {
          if (client.url === urlToOpen) {
            client.focus();
            return true;
          }
          return false;
        });

        // Si no hay ventana abierta, abrir una nueva
        if (!hadWindowToFocus && clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

// Manejar el cierre de notificaciones
self.addEventListener('notificationclose', (event) => {
  console.log('Notificación cerrada', event.notification);
});
