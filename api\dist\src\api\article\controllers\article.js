"use strict";
/**
 *  article controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::article.article", ({ strapi }) => ({
    async find(ctx) {
        // Obtener parámetros de paginación
        const { page = "1", pageSize = "10", tags, title, search, } = ctx.query;
        // Construir los filtros
        const filters = {};
        // Filtrar por tags si se especifican
        if (tags) {
            const tagArray = Array.isArray(tags) ? tags : tags.split(",");
            // Convertir los tags a minúsculas para la comparación
            const normalizedTags = tagArray.map((tag) => tag.toLowerCase().trim());
            filters.tags = {
                $contains: normalizedTags
            };
        }
        // Filtrar por título si se especifica
        if (title) {
            filters.title = {
                $containsi: title,
            };
        }
        // Filtrar por texto si se especifica
        if (search) {
            filters.$or = [
                { title: { $containsi: search } },
                { text: { $containsi: search } },
            ];
        }
        // Obtener los artículos con sus relaciones
        const entries = await strapi.entityService.findMany("api::article.article", {
            sort: [{ date: 'desc' }],
            filters,
            populate: {
                author: {
                    populate: ["imgUrl"],
                    fields: ["username", "firstName", "lastName"]
                },
                img: true
            },
            page: parseInt(page, 10),
            pageSize: parseInt(pageSize, 10)
        });
        const data = entries;
        const meta = {
            pagination: {
                page: parseInt(page, 10),
                pageSize: parseInt(pageSize, 10),
                pageCount: Math.ceil(entries.length / parseInt(pageSize, 10)),
                total: entries.length
            }
        };
        // Transformar la respuesta
        const transformedData = data.map((article) => {
            var _a, _b, _c;
            // Obtener el nombre del autor
            const author = article.author
                ? `${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() || article.author.username
                : "Anónimo";
            // Obtener el avatar del autor
            const avatarUrl = ((_b = (_a = article.author) === null || _a === void 0 ? void 0 : _a.imgUrl) === null || _b === void 0 ? void 0 : _b.url) || null;
            // Obtener la imagen del artículo
            const img = ((_c = article.img) === null || _c === void 0 ? void 0 : _c.url) || null;
            // Asegurarnos de que tags sea siempre un array de strings
            const tags = Array.isArray(article.tags) ? article.tags : [];
            return {
                id: article.id,
                title: article.title,
                text: article.text,
                date: article.date,
                author,
                avatarUrl,
                img,
                tags,
            };
        });
        return { data: transformedData, meta };
    },
    async create(ctx) {
        var _a, _b, _c, _d;
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("No estás autorizado. Por favor inicia sesión.");
            }
            let data;
            try {
                data = JSON.parse(ctx.request.body.data || "{}");
                // Asegurarnos de que los tags sean un array
                if (data.tags && !Array.isArray(data.tags)) {
                    data.tags = [data.tags];
                }
            }
            catch (e) {
                return ctx.badRequest("Error al procesar los datos");
            }
            const imgFile = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a["files.img"];
            // Asignar el autor actual al artículo
            data.author = ctx.state.user.id;
            data.date = new Date();
            // Crear el artículo primero
            const article = await strapi.entityService.create("api::article.article", {
                data,
                populate: ["author", "img"],
            });
            // Si hay una imagen, subirla y actualizar el artículo
            if (imgFile) {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::article.article",
                        refId: article.id,
                        field: "img",
                    },
                    files: imgFile,
                });
                // Actualizar el artículo con la imagen
                await strapi.entityService.update("api::article.article", article.id, {
                    data: {
                        img: uploadedFiles[0].id,
                    },
                });
            }
            // Obtener el artículo actualizado con su autor y imagen
            const updatedArticle = await strapi.entityService.findOne("api::article.article", article.id, {
                populate: ["author", "author.imgUrl", "img"],
            });
            if (!updatedArticle) {
                return ctx.notFound("No se pudo encontrar el artículo");
            }
            // Transformar la respuesta
            const populatedArticle = updatedArticle;
            return {
                data: {
                    id: populatedArticle.id,
                    title: populatedArticle.title,
                    text: populatedArticle.text,
                    date: populatedArticle.date,
                    author: populatedArticle.author
                        ? `${populatedArticle.author.firstName || ''} ${populatedArticle.author.lastName || ''}`.trim() || populatedArticle.author.username || "Anónimo"
                        : "Anónimo",
                    avatarUrl: ((_c = (_b = populatedArticle.author) === null || _b === void 0 ? void 0 : _b.imgUrl) === null || _c === void 0 ? void 0 : _c.url) || null,
                    img: ((_d = populatedArticle.img) === null || _d === void 0 ? void 0 : _d.url) || null,
                    tags: Array.isArray(populatedArticle.tags)
                        ? populatedArticle.tags
                        : [populatedArticle.tags].filter(Boolean),
                },
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    async update(ctx) {
        var _a;
        const { id } = ctx.params;
        const article = (await strapi.db.query("api::article.article").findOne({
            where: { id },
            populate: { author: true },
        }));
        // Verificar si el usuario actual es el autor
        if (((_a = article === null || article === void 0 ? void 0 : article.author) === null || _a === void 0 ? void 0 : _a.id) !== ctx.state.user.id) {
            return ctx.forbidden("Solo el autor puede editar este artículo");
        }
        const response = await super.update(ctx);
        return response;
    },
    async delete(ctx) {
        var _a, _b;
        try {
            const { id } = ctx.params;
            // Verificar si el artículo existe
            const article = await strapi.db.query("api::article.article").findOne({
                where: { id },
                populate: { author: true },
            });
            if (!article) {
                return ctx.notFound("Artículo no encontrado");
            }
            // Verificar si el usuario actual es el autor o es administrador
            const isAdmin = ((_a = ctx.state.user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin";
            const isAuthor = ((_b = article.author) === null || _b === void 0 ? void 0 : _b.id) === ctx.state.user.id;
            if (!isAdmin && !isAuthor) {
                return ctx.forbidden("No tienes permiso para eliminar este artículo");
            }
            // Eliminar directamente usando el entityService
            await strapi.entityService.delete('api::article.article', id);
            // Enviar respuesta de éxito
            ctx.response.status = 200;
            return {
                data: { id },
                meta: {
                    message: "Artículo eliminado exitosamente"
                }
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
}));
