"use strict";
/**
 * pool-cleaning router
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: "GET",
            path: "/pool-cleanings",
            handler: "pool-cleaning.findAll",
            config: {
                policies: [],
                middlewares: [],
                auth: false,
            },
        },
        {
            method: "POST",
            path: "/pool-cleanings",
            handler: "pool-cleaning.create",
            config: {
                policies: [],
                middlewares: [],
                auth: false,
            },
        },
        {
            method: "PUT",
            path: "/pool-cleanings/:id",
            handler: "pool-cleaning.update",
            config: {
                policies: [],
                middlewares: [],
                auth: false,
            },
        },
        {
            method: "DELETE",
            path: "/pool-cleanings/:id",
            handler: "pool-cleaning.delete",
            config: {
                policies: [],
                middlewares: [],
                auth: false,
            },
        },
    ],
};
