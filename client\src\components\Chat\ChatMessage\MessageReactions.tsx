/**
 * Componente para mostrar y manejar reacciones de mensajes
 */

import React from 'react';
import { Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { MessageReactionsProps } from '@app/types/chat';
import * as S from './ChatMessage.styles';

export const MessageReactions: React.FC<MessageReactionsProps> = ({
  reactions,
  currentUserId,
  onReactionClick,
  onReactionAdd,
}) => {
  // Convertir reacciones a formato más manejable
  const reactionEntries = Object.entries(reactions || {}).filter(([_, userIds]) => userIds.length > 0);

  if (reactionEntries.length === 0) {
    return null;
  }

  return (
    <S.ReactionsContainer>
      {reactionEntries.map(([emoji, userIds]) => {
        const count = userIds.length;
        const isActive = userIds.includes(currentUserId);
        
        // Crear tooltip con nombres de usuarios (máximo 5)
        const tooltipContent = count <= 5 
          ? `Reaccionaron: ${userIds.slice(0, 5).map(id => `Usuario ${id}`).join(', ')}`
          : `${count} personas reaccionaron`;

        return (
          <Tooltip key={emoji} title={tooltipContent} placement="top">
            <S.ReactionButton
              $isActive={isActive}
              onClick={() => onReactionClick(emoji)}
            >
              <span className="emoji">{emoji}</span>
              <span className="count">{count}</span>
            </S.ReactionButton>
          </Tooltip>
        );
      })}
      
      {/* Botón para agregar nueva reacción */}
      <Tooltip title="Agregar reacción" placement="top">
        <S.AddReactionButton onClick={onReactionAdd}>
          <PlusOutlined style={{ fontSize: '10px' }} />
        </S.AddReactionButton>
      </Tooltip>
    </S.ReactionsContainer>
  );
};
