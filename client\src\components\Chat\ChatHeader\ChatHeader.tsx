/**
 * Componente header del canal de chat
 */

import React from "react";
import { Typo<PERSON>, Space, Badge, Tooltip, Button, Dropdown } from "antd";
import {
  UserOutlined,
  MoreOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import type { ChatHeaderProps, ChatTypingIndicator } from "@app/types/chat";
import * as S from "./ChatHeader.styles";

const { Title, Text } = Typography;

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  channel,
  onlineCount = 0,
  typingUsers = [],
  isConnected = true,
  lastUpdated,
}) => {
  if (!channel) {
    return null;
  }

  // Formatear usuarios escribiendo
  const formatTypingUsers = (users: ChatTypingIndicator[]) => {
    if (users.length === 0) return null;

    if (users.length === 1) {
      return `${users[0].username} está escribiendo...`;
    } else if (users.length === 2) {
      return `${users[0].username} y ${users[1].username} están escribiendo...`;
    } else {
      return `${users[0].username} y ${users.length - 1} más están escribiendo...`;
    }
  };

  const typingText = formatTypingUsers(typingUsers);

  // Menú de acciones del canal
  const channelActions = [
    {
      key: "info",
      icon: <InfoCircleOutlined />,
      label: "Información del canal",
    },
    {
      key: "search",
      icon: <SearchOutlined />,
      label: "Buscar en este canal",
    },
    ...(channel.canModerate
      ? [
          {
            key: "settings",
            icon: <SettingOutlined />,
            label: "Configurar canal",
          },
        ]
      : []),
  ];

  return (
    <S.HeaderContainer>
      <S.ChannelInfo>
        {/* Icono y nombre del canal */}
        <S.ChannelIcon style={{ backgroundColor: channel.color }}>
          {channel.icon}
        </S.ChannelIcon>

        <S.ChannelDetails>
          <S.ChannelName>
            <Title level={4} style={{ margin: 0 }}>
              {channel.name}
            </Title>
            {!channel.canWrite && (
              <Tooltip title="Solo lectura">
                <S.ReadOnlyBadge>Solo lectura</S.ReadOnlyBadge>
              </Tooltip>
            )}
          </S.ChannelName>

          <S.ChannelStatus>
            {typingText ? (
              <S.TypingIndicator>
                {typingText}
                <S.TypingDots>
                  <span />
                  <span />
                  <span />
                </S.TypingDots>
              </S.TypingIndicator>
            ) : (
              <Space size="small">
                {channel.description && (
                  <Text type="secondary" style={{ fontSize: "12px" }}>
                    {channel.description}
                  </Text>
                )}
                {onlineCount > 0 && (
                  <Space size={4}>
                    <UserOutlined style={{ fontSize: "12px" }} />
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {onlineCount} en línea
                    </Text>
                  </Space>
                )}
                {/* Indicador de estado de conexión */}
                <Space size={4}>
                  <CheckCircleOutlined
                    style={{
                      fontSize: "12px",
                      color: isConnected ? "#52c41a" : "#ff4d4f",
                    }}
                  />
                  <Text type="secondary" style={{ fontSize: "12px" }}>
                    {isConnected ? "Conectado" : "Desconectado"}
                  </Text>
                </Space>
                {/* Última actualización */}
                {lastUpdated && (
                  <Text type="secondary" style={{ fontSize: "11px" }}>
                    Actualizado{" "}
                    {formatDistanceToNow(lastUpdated, {
                      addSuffix: true,
                      locale: es,
                    })}
                  </Text>
                )}
              </Space>
            )}
          </S.ChannelStatus>
        </S.ChannelDetails>
      </S.ChannelInfo>

      {/* Acciones del canal */}
      <S.ChannelActions>
        <Space size="small">
          {/* Contador de usuarios online */}
          {onlineCount > 0 && (
            <Tooltip title={`${onlineCount} usuarios en línea`}>
              <Badge count={onlineCount} size="small">
                <Button type="text" icon={<UserOutlined />} size="small" />
              </Badge>
            </Tooltip>
          )}

          {/* Búsqueda rápida */}
          <Tooltip title="Buscar en este canal">
            <Button type="text" icon={<SearchOutlined />} size="small" />
          </Tooltip>

          {/* Menú de acciones */}
          <Dropdown
            menu={{ items: channelActions }}
            trigger={["click"]}
            placement="bottomRight"
          >
            <Button type="text" icon={<MoreOutlined />} size="small" />
          </Dropdown>
        </Space>
      </S.ChannelActions>
    </S.HeaderContainer>
  );
};
