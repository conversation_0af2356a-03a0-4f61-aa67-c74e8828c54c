import { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { BaseForm } from "@app/components/common/forms/BaseForm/BaseForm";
import { useFeedback } from "@app/hooks/useFeedback";
import { useAppDispatch } from "@app/hooks/reduxHooks";
import { doSetNewPassword } from "@app/store/slices/authSlice";
import * as S from "./NewPasswordForm.styles";
import * as Auth from "@app/components/layouts/AuthLayout/AuthLayout.styles";

interface NewPasswordFormData {
  password: string;
  passwordConfirmation: string;
}

export const NewPasswordForm = () => {
  const { t } = useTranslation();
  const { notification } = useFeedback();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation(); // Obtener el objeto location para extraer el query param
  const [isLoading, setLoading] = useState(false);

  // Extraer el parámetro `code` desde la URL
  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get("code");

  const handleSubmit = (values: NewPasswordFormData) => {
    if (!code) {
      notification.error({
        message: t("newPassword.codeError"),
        description: t("newPassword.codeMissing"),
      });
      return;
    }

    setLoading(true);
    // Incluir el `code` junto con la nueva contraseña
    dispatch(doSetNewPassword({ ...values, code }))
      .unwrap()
      .then(() => {
        setLoading(false);
        navigate("/auth/login");
        notification.success({
          message: t("newPassword.successMessage"),
        });
      })
      .catch((err) => {
        notification.error({ message: err.message });
        setLoading(false);
      });
  };

  return (
    <Auth.FormWrapper>
      <BaseForm
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark="optional"
      >
        <Auth.FormTitle>{t("newPassword.title")}</Auth.FormTitle>
        <S.Description>{t("newPassword.description")}</S.Description>
        <Auth.FormItem
          name="password"
          label={t("common.password")}
          rules={[{ required: true, message: t("common.requiredField") }]}
        >
          <Auth.FormInputPassword placeholder={t("common.password")} />
        </Auth.FormItem>
        <Auth.FormItem
          name="passwordConfirmation"
          label={t("common.confirmPassword")}
          dependencies={["password"]}
          rules={[
            { required: true, message: t("common.requiredField") },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t("common.confirmPasswordError")),
                );
              },
            }),
          ]}
        >
          <Auth.FormInputPassword placeholder={t("common.confirmPassword")} />
        </Auth.FormItem>
        <BaseForm.Item noStyle>
          <S.SubmitButton type="primary" htmlType="submit" loading={isLoading}>
            {t("common.resetPassword")}
          </S.SubmitButton>
        </BaseForm.Item>
      </BaseForm>
    </Auth.FormWrapper>
  );
};
