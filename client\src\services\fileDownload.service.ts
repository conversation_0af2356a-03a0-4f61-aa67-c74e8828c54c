import { message } from 'antd';

export interface DownloadOptions {
  filename?: string;
  showProgress?: boolean;
}

/**
 * Servicio para manejar descargas de archivos
 */
export class FileDownloadService {
  /**
   * Descargar archivo desde URL
   */
  static async downloadFile(url: string, options: DownloadOptions = {}): Promise<void> {
    const { filename, showProgress = true } = options;
    
    try {
      if (showProgress) {
        message.loading('Descargando archivo...', 0);
      }

      // Crear elemento de descarga
      const link = document.createElement('a');
      link.href = url;
      
      // Si se especifica un nombre de archivo, usarlo
      if (filename) {
        link.download = filename;
      } else {
        // Extraer nombre del archivo de la URL
        const urlParts = url.split('/');
        const urlFilename = urlParts[urlParts.length - 1];
        if (urlFilename) {
          link.download = urlFilename;
        }
      }
      
      // Agregar al DOM temporalmente
      document.body.appendChild(link);
      
      // Simular click para iniciar descarga
      link.click();
      
      // Limpiar
      document.body.removeChild(link);
      
      if (showProgress) {
        message.destroy();
        message.success('Archivo descargado correctamente');
      }
      
    } catch (error) {
      console.error('Error descargando archivo:', error);
      
      if (showProgress) {
        message.destroy();
        message.error('Error al descargar el archivo');
      }
      
      throw error;
    }
  }

  /**
   * Descargar archivo con fetch (para archivos que requieren autenticación)
   */
  static async downloadFileWithAuth(url: string, options: DownloadOptions = {}): Promise<void> {
    const { filename, showProgress = true } = options;
    
    try {
      if (showProgress) {
        message.loading('Descargando archivo...', 0);
      }

      // Obtener token de autenticación
      const token = localStorage.getItem('token');
      
      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
      });

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      // Obtener el blob
      const blob = await response.blob();
      
      // Crear URL temporal
      const blobUrl = window.URL.createObjectURL(blob);
      
      // Crear elemento de descarga
      const link = document.createElement('a');
      link.href = blobUrl;
      
      // Determinar nombre del archivo
      let downloadFilename = filename;
      if (!downloadFilename) {
        // Intentar obtener del header Content-Disposition
        const contentDisposition = response.headers.get('Content-Disposition');
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            downloadFilename = filenameMatch[1];
          }
        }
        
        // Si no se encuentra, usar nombre de la URL
        if (!downloadFilename) {
          const urlParts = url.split('/');
          downloadFilename = urlParts[urlParts.length - 1] || 'archivo';
        }
      }
      
      link.download = downloadFilename;
      
      // Agregar al DOM temporalmente
      document.body.appendChild(link);
      
      // Simular click para iniciar descarga
      link.click();
      
      // Limpiar
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
      
      if (showProgress) {
        message.destroy();
        message.success('Archivo descargado correctamente');
      }
      
    } catch (error) {
      console.error('Error descargando archivo:', error);
      
      if (showProgress) {
        message.destroy();
        message.error('Error al descargar el archivo');
      }
      
      throw error;
    }
  }

  /**
   * Obtener información de un archivo sin descargarlo
   */
  static async getFileInfo(url: string): Promise<{
    size: number;
    type: string;
    lastModified?: Date;
  }> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      
      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }
      
      const size = parseInt(response.headers.get('Content-Length') || '0');
      const type = response.headers.get('Content-Type') || 'application/octet-stream';
      const lastModifiedHeader = response.headers.get('Last-Modified');
      const lastModified = lastModifiedHeader ? new Date(lastModifiedHeader) : undefined;
      
      return {
        size,
        type,
        lastModified,
      };
      
    } catch (error) {
      console.error('Error obteniendo información del archivo:', error);
      throw error;
    }
  }

  /**
   * Verificar si un archivo es una imagen
   */
  static isImage(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Verificar si un archivo es un video
   */
  static isVideo(mimeType: string): boolean {
    return mimeType.startsWith('video/');
  }

  /**
   * Verificar si un archivo es audio
   */
  static isAudio(mimeType: string): boolean {
    return mimeType.startsWith('audio/');
  }

  /**
   * Obtener extensión de archivo desde nombre
   */
  static getFileExtension(filename: string): string {
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * Formatear tamaño de archivo
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
