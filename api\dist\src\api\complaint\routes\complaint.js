"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: 'GET',
            path: '/complaints',
            handler: 'complaint.find',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        {
            method: 'GET',
            path: '/complaints/user/:id',
            handler: 'complaint.findByUser',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        {
            method: 'POST',
            path: '/complaints',
            handler: 'complaint.create',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        {
            method: 'PUT',
            path: '/complaints/:id',
            handler: 'complaint.update',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        {
            method: 'POST',
            path: '/complaints/:id/respond',
            handler: 'complaint.respond',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
    ],
};
