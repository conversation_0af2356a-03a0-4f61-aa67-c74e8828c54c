"use strict";
/**
 * comment controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
const sanitizeComment = (comment, userId) => {
    var _a, _b, _c, _d;
    if (!comment)
        return null;
    const likes = (comment.likes || []).map((user) => user.id);
    const dislikes = (comment.dislikes || []).map((user) => user.id);
    return {
        id: comment.id,
        content: comment.content,
        createdAt: comment.createdAt,
        isDeleted: comment.isDeleted || false,
        author: comment.author
            ? {
                id: comment.author.id,
                username: comment.author.username || "",
                firstName: comment.author.firstName || "",
                lastName: comment.author.lastName || "",
                imgUrl: ((_c = (_b = (_a = comment.author.imgUrl) === null || _a === void 0 ? void 0 : _a.formats) === null || _b === void 0 ? void 0 : _b.thumbnail) === null || _c === void 0 ? void 0 : _c.url) ||
                    ((_d = comment.author.imgUrl) === null || _d === void 0 ? void 0 : _d.url) ||
                    null,
            }
            : {
                id: 0,
                username: "deleted",
                firstName: "",
                lastName: "",
                imgUrl: null,
            },
        article: comment.article
            ? {
                id: comment.article.id,
                title: comment.article.title,
            }
            : null,
        replies: Array.isArray(comment.replies)
            ? comment.replies.map((c) => sanitizeComment(c, userId)).filter(Boolean)
            : [],
        likes,
        dislikes,
        likeCount: likes.length,
        dislikeCount: dislikes.length,
        userReaction: userId
            ? likes.includes(userId)
                ? "like"
                : dislikes.includes(userId)
                    ? "dislike"
                    : null
            : null,
    };
};
const defaultPopulate = {
    populate: {
        author: {
            populate: ["imgUrl"],
        },
        article: true,
        threadOf: {
            populate: {
                author: {
                    populate: ["imgUrl"],
                },
            },
        },
        replies: {
            populate: {
                author: {
                    populate: ["imgUrl"],
                },
            },
        },
        likes: true,
        dislikes: true,
    },
};
const validateReplyLevel = async (strapi, threadOfId) => {
    if (!threadOfId)
        return true;
    const parentComment = await strapi.entityService.findOne("api::comment.comment", threadOfId, { populate: ["threadOf"] });
    // Si el comentario padre ya es una respuesta, no permitir otra respuesta
    if (parentComment === null || parentComment === void 0 ? void 0 : parentComment.threadOf) {
        throw new Error("No se permiten respuestas a respuestas");
    }
    return true;
};
exports.default = strapi_1.factories.createCoreController("api::comment.comment", ({ strapi }) => ({
    async toggleReaction(ctx) {
        const { id: commentId } = ctx.params;
        const { type } = ctx.request.body;
        const userId = ctx.state.user.id;
        if (!["like", "dislike"].includes(type)) {
            return ctx.badRequest("Invalid reaction type");
        }
        try {
            const comment = (await strapi.entityService.findOne("api::comment.comment", commentId));
            if (!comment) {
                return ctx.notFound("Comment not found");
            }
            const currentLikes = (comment.likes || []);
            const currentDislikes = (comment.dislikes || []);
            // Get current user reactions
            const hasLiked = currentLikes.some((user) => user.id === userId);
            const hasDisliked = currentDislikes.some((user) => user.id === userId);
            let likesToConnect = [];
            let likesToDisconnect = [];
            let dislikesToConnect = [];
            let dislikesToDisconnect = [];
            if (type === "like") {
                if (hasLiked) {
                    // Si ya dio like, quitarlo
                    likesToDisconnect.push(userId);
                }
                else {
                    // Si no ha dado like, agregarlo y quitar dislike si existe
                    likesToConnect.push(userId);
                    if (hasDisliked) {
                        dislikesToDisconnect.push(userId);
                    }
                }
            }
            else if (type === "dislike") {
                if (hasDisliked) {
                    // Si ya dio dislike, quitarlo
                    dislikesToDisconnect.push(userId);
                }
                else {
                    // Si no ha dado dislike, agregarlo y quitar like si existe
                    dislikesToConnect.push(userId);
                    if (hasLiked) {
                        likesToDisconnect.push(userId);
                    }
                }
            }
            // Update the comment using raw queries for relations
            if (type === "like") {
                if (hasLiked) {
                    // Remove like
                    await strapi.db.query("api::comment.comment").update({
                        where: { id: commentId },
                        data: {
                            likes: { disconnect: [{ id: userId }] },
                        },
                    });
                }
                else {
                    // Add like and remove dislike if exists
                    await strapi.db.query("api::comment.comment").update({
                        where: { id: commentId },
                        data: {
                            likes: { connect: [{ id: userId }] },
                            dislikes: hasDisliked
                                ? { disconnect: [{ id: userId }] }
                                : undefined,
                        },
                    });
                }
            }
            else if (type === "dislike") {
                if (hasDisliked) {
                    // Remove dislike
                    await strapi.db.query("api::comment.comment").update({
                        where: { id: commentId },
                        data: {
                            dislikes: { disconnect: [{ id: userId }] },
                        },
                    });
                }
                else {
                    // Add dislike and remove like if exists
                    await strapi.db.query("api::comment.comment").update({
                        where: { id: commentId },
                        data: {
                            dislikes: { connect: [{ id: userId }] },
                            likes: hasLiked
                                ? { disconnect: [{ id: userId }] }
                                : undefined,
                        },
                    });
                }
            }
            // Fetch the updated comment with relations
            const updatedComment = (await strapi.entityService.findOne("api::comment.comment", commentId, defaultPopulate));
            return { data: sanitizeComment(updatedComment, userId) };
        }
        catch (error) {
            console.error("Error updating reaction:", error);
            return ctx.badRequest("Failed to update reaction");
        }
    },
    async find(ctx) {
        var _a, _b;
        const { query } = ctx;
        const userId = (_a = ctx.state.user) === null || _a === void 0 ? void 0 : _a.id;
        try {
            // Asegurarnos de que solo obtenemos comentarios principales (no respuestas)
            const filters = {
                ...((_b = query.filters) !== null && _b !== void 0 ? _b : {}),
                threadOf: { $null: true },
            };
            const comments = (await strapi.entityService.findMany("api::comment.comment", {
                ...defaultPopulate,
                filters,
            }));
            return {
                data: comments.map((comment) => sanitizeComment(comment, userId)),
            };
        }
        catch (error) {
            console.error("Error fetching comments:", error);
            return ctx.badRequest("Failed to fetch comments");
        }
    },
    async findOne(ctx) {
        const { id } = ctx.params;
        const { query } = ctx;
        const comment = await strapi.entityService.findOne("api::comment.comment", id);
        // Sanitizar los datos antes de enviarlos
        const sanitizedComment = sanitizeComment(comment);
        return { data: sanitizedComment };
    },
    async create(ctx) {
        var _a;
        const { data } = ctx.request.body;
        const userId = (_a = ctx.state.user) === null || _a === void 0 ? void 0 : _a.id;
        try {
            // Validar nivel de respuesta si es una respuesta
            if (data.threadOf) {
                await validateReplyLevel(strapi, data.threadOf);
            }
            const comment = await strapi.entityService.create("api::comment.comment", {
                data: {
                    ...data,
                    author: userId,
                },
                populate: defaultPopulate.populate,
            });
            return { data: sanitizeComment(comment) };
        }
        catch (error) {
            console.error("Error creating comment:", error);
            return ctx.badRequest("Failed to create comment");
        }
    },
    async update(ctx) {
        var _a;
        const { id } = ctx.params;
        const { data } = ctx.request.body;
        const comment = await strapi.entityService.findOne("api::comment.comment", id, {
            populate: ["author"],
        });
        if (!comment) {
            return ctx.notFound("Comment not found");
        }
        const { user } = ctx.state;
        // Solo el autor puede editar su comentario
        if (((_a = comment.author) === null || _a === void 0 ? void 0 : _a.id) !== user.id) {
            return ctx.forbidden("You are not allowed to perform this action");
        }
        const updatedComment = await strapi.entityService.update("api::comment.comment", id, {
            ...defaultPopulate,
            data,
        });
        return { data: sanitizeComment(updatedComment) };
    },
    async delete(ctx) {
        var _a;
        const { id } = ctx.params;
        const { user } = ctx.state;
        if (!user) {
            return ctx.unauthorized("You must be logged in to delete a comment");
        }
        const comment = await strapi.entityService.findOne("api::comment.comment", id, {
            populate: ["author", "replies"],
        });
        if (!comment) {
            return ctx.notFound("Comment not found");
        }
        // Solo el autor puede eliminar su comentario
        if (((_a = comment.author) === null || _a === void 0 ? void 0 : _a.id) !== user.id) {
            return ctx.forbidden("You are not allowed to perform this action");
        }
        try {
            // Eliminar el comentario completamente
            const deletedComment = await strapi.entityService.delete("api::comment.comment", id);
            return sanitizeComment(deletedComment, user.id);
        }
        catch (error) {
            return ctx.badRequest("Error al eliminar el comentario");
        }
    },
}));
