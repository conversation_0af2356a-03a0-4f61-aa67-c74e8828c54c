/**
 * Configuración de tema para el sistema de chat
 */

export const chatTheme = {
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff',
    
    // Colores de texto
    textMain: '#262626',
    textSecondary: '#8c8c8c',
    textDisabled: '#bfbfbf',
    
    // Colores de fondo
    backgroundLight: '#ffffff',
    backgroundDark: '#141414',
    backgroundSecondaryLight: '#fafafa',
    backgroundSecondaryDark: '#1f1f1f',
    
    // Colores de borde
    borderLight: '#f0f0f0',
    borderDark: '#2a2a2a',
    
    // Colores específicos del chat
    ownMessageBg: '#e6f7ff',
    otherMessageBg: '#f5f5f5',
    systemMessageBg: '#f0f0f0',
    
    // Estados de conexión
    online: '#52c41a',
    offline: '#d9d9d9',
    away: '#faad14',
    busy: '#ff4d4f',
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '12px',
    lg: '16px',
    xl: '24px',
    xxl: '32px',
  },
  
  borderRadius: {
    sm: '4px',
    md: '6px',
    lg: '8px',
    xl: '12px',
    round: '50%',
  },
  
  shadows: {
    sm: '0 2px 4px rgba(0, 0, 0, 0.1)',
    md: '0 4px 8px rgba(0, 0, 0, 0.15)',
    lg: '0 6px 16px rgba(0, 0, 0, 0.2)',
  },
  
  transitions: {
    fast: '0.1s ease',
    normal: '0.2s ease',
    slow: '0.3s ease',
  },
  
  zIndex: {
    dropdown: 1000,
    modal: 1050,
    tooltip: 1060,
    notification: 1070,
  },
};

export type ChatTheme = typeof chatTheme;
