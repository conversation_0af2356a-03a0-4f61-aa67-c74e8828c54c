"use strict";
/**
 * article router.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        // Obtener todos los artículos
        {
            method: 'GET',
            path: '/articles',
            handler: 'article.find',
            config: {
                auth: false,
            },
        },
        // Obtener un artículo específico
        {
            method: 'GET',
            path: '/articles/:id',
            handler: 'article.findOne',
            config: {
                auth: false,
            },
        },
        // Crear un nuevo artículo
        {
            method: 'POST',
            path: '/articles',
            handler: 'article.create',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        // Actualizar un artículo
        {
            method: 'PUT',
            path: '/articles/:id',
            handler: 'article.update',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        // Eliminar un artículo
        {
            method: 'DELETE',
            path: '/articles/:id',
            handler: 'article.delete',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
    ],
};
