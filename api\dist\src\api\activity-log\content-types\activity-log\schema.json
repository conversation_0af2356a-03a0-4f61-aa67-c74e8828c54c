{"kind": "collectionType", "collectionName": "activity_logs", "info": {"singularName": "activity-log", "pluralName": "activity-logs", "displayName": "ActivityLog", "description": "Registro de actividades de los usuarios, especialmente para monitorear arrendatarios"}, "options": {"draftAndPublish": false}, "attributes": {"action": {"type": "string", "required": true, "description": "Acción realizada por el usuario"}, "method": {"type": "enumeration", "enum": ["GET", "POST", "PUT", "DELETE"], "description": "Método HTTP utilizado"}, "route": {"type": "string", "description": "Ruta accedida"}, "description": {"type": "text", "description": "Descripción detallada de la actividad"}, "ip": {"type": "string", "description": "Dirección IP del usuario"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "activityLogs"}, "metadata": {"type": "json", "description": "Datos adicionales relacionados con la actividad"}}}