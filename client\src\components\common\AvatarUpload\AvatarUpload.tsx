import { useState } from "react";
import { Flex, message, Typography } from "antd";
import type { UploadFile, UploadProps } from "antd";
import { CameraFilled, FilePdfOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { BaseUpload } from "../BaseUpload/BaseUpload";
import { BaseAvatar } from "../BaseAvatar/BaseAvatar";

const { Text } = Typography;

interface AvatarUploadProps {
  value?: UploadFile[];
  onChange?: (fileList: UploadFile[]) => void;
  maxCount?: number;
  imgUrl?: string;
  allowPdf?: boolean;
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  imgUrl,
  value = [],
  onChange,
  maxCount = 1,
  allowPdf = false,
}) => {
  const fileList: UploadFile[] = Array.isArray(value) ? value : [];
  const { t } = useTranslation();
  const [imageUrl, setImageUrl] = useState<string | undefined>(imgUrl);
  const [fileName, setFileName] = useState<string>("");

  const validateFileType = (file: File) => {
    const isImage = file.type.startsWith("image/");
    const isPdf = file.type === "application/pdf";
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!isImage && (!allowPdf || !isPdf)) {
      message.error(t("uploads.invalidFileType"));
      return false;
    }

    if (file.size > maxSize) {
      message.error(t("uploads.fileTooLarge"));
      return false;
    }

    return true;
  };

  const handleChange: UploadProps["onChange"] = async ({
    fileList: newFileList,
  }) => {
    const file = newFileList[0];

    if (file?.originFileObj) {
      if (file.originFileObj.type.startsWith("image/")) {
        const url = URL.createObjectURL(file.originFileObj);
        setImageUrl(url);
        setFileName("");

        const updatedFile = {
          ...file,
          thumbUrl: url,
        };
        onChange?.([updatedFile]);
      } else {
        // Para PDFs, guardamos el nombre del archivo
        setImageUrl("");
        setFileName(file.name || file.originFileObj.name);

        const updatedFile = {
          ...file,
          name: file.name || file.originFileObj.name,
        };
        onChange?.([updatedFile]);
      }
    } else {
      setImageUrl("");
      setFileName("");
      onChange?.(newFileList);
    }
  };

  const uploadProps: UploadProps = {
    beforeUpload: validateFileType,
    listType: "picture-card",
    maxCount,
    fileList,
    onChange: handleChange,
    showUploadList: false,
    accept: allowPdf ? "image/*,.pdf" : "image/*",
  };

  const currentFile = value?.[0];
  const isPdf = currentFile?.type === "application/pdf";
  const currentImageUrl =
    imageUrl || (!isPdf && (currentFile?.thumbUrl || currentFile?.url));
  const displayFileName = fileName || (isPdf && currentFile?.name) || "";

  return (
    <Flex vertical align="center" gap="small">
      <BaseUpload {...uploadProps}>
        <div
          style={{
            width: "140px",
            height: "140px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            padding: "12px",
          }}
        >
          {isPdf ? (
            <>
              <FilePdfOutlined
                style={{ fontSize: "24px", marginBottom: "8px" }}
              />
              {displayFileName && (
                <Text
                  ellipsis={{ tooltip: displayFileName }}
                  style={{
                    maxWidth: "120px",
                    textAlign: "center",
                    fontSize: "12px",
                  }}
                >
                  {displayFileName}
                </Text>
              )}
            </>
          ) : currentImageUrl ? (
            <BaseAvatar
              size={140}
              src={currentImageUrl}
              alt="avatar"
              shape="square"
            />
          ) : (
            <CameraFilled style={{ fontSize: "24px" }} />
          )}
        </div>
      </BaseUpload>
    </Flex>
  );
};
