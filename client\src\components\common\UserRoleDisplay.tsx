
import { useUserRole } from "@app/hooks/useUserRole";
import { Card, Typography, Spin, Tag } from "antd";

const { Title, Text } = Typography;

const UserRoleDisplay = () => {
  const { userRole, loading, error } = useUserRole();

  // Función para determinar el color del tag según el rol
  const getRoleColor = (role: string) => {
    const roleColors: Record<string, string> = {
      Admin: "red",
      Vigilante: "blue",
      Piscinero: "cyan",
      Consejero: "green",
      Convivencia: "purple",
      Authenticated: "orange",
      Public: "default",
    };

    return roleColors[role] || "default";
  };

  return (
    <Card
      title="Información de Usuario"
      style={{ width: "100%", marginBottom: "20px" }}
      extra={<Tag color={getRoleColor(userRole)}>{userRole || "Sin rol"}</Tag>}
    >
      {loading ? (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <Spin size="large" />
          <div style={{ marginTop: "10px" }}>
            Cargando información de rol...
          </div>
        </div>
      ) : error ? (
        <div style={{ color: "red" }}>
          <Text type="danger">Error al cargar el rol: {error}</Text>
        </div>
      ) : (
        <div>
          <Title level={4}>Tu rol actual es: </Title>
          <Tag
            color={getRoleColor(userRole)}
            style={{ fontSize: "16px", padding: "5px 10px" }}
          >
            {userRole || "Sin rol asignado"}
          </Tag>

          <div style={{ marginTop: "20px" }}>
            <Text>
              Este componente está utilizando el hook useUserRole para mostrar
              el rol actual.
            </Text>
          </div>

          {userRole === "Admin" && (
            <div
              style={{
                marginTop: "10px",
                padding: "10px",
                backgroundColor: "#f6ffed",
                borderRadius: "4px",
              }}
            >
              <Text strong>¡Tienes permisos de administrador!</Text>
              <div>Tienes acceso a todas las secciones del sistema.</div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default UserRoleDisplay;
