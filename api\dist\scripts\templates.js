module.exports = [
    /* ───────────── 1. MANTEN<PERSON><PERSON><PERSON><PERSON> ───────────── */
    {
        title: "Solicitud de Mantenimiento",
        category: "MANTENIMIENTO",
        content: "Solicitud de mantenimiento para: {location}\n\n" +
            "Tipo de mantenimiento: {maintenanceType}\n\n" +
            "Descripción del problema: {description}\n\n" +
            "Fecha: {date}\nHora: {time}",
        requiredFields: [
            "maintenanceType",
            "description",
            "location",
            "date",
            "time",
        ],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            maintenanceType: {
                type: "select",
                label: "Tipo de Mantenimiento",
                required: true,
                options: [
                    { label: "Plomería", value: "PLOMERIA" },
                    { label: "Electricidad", value: "ELECTRICIDAD" },
                    { label: "Estructural", value: "ESTRUCTURAL" },
                    { label: "O<PERSON>", value: "OTRO" },
                ],
            },
            description: {
                type: "textarea",
                label: "Descripción del problema",
                required: true,
            },
        },
    },
    /* ───────────── 2. MUDANZA ───────────── */
    {
        title: "Solicitud de Mudanza",
        category: "MUDANZA",
        content: "Solicitud de mudanza\n\nDetalles: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            description: {
                type: "textarea",
                label: "Detalles de la mudanza",
                required: true,
            },
        },
    },
    /* ───────────── 3. RUIDO ───────────── */
    {
        title: "Queja por Ruido",
        category: "CONVIVENCIA",
        subcategory: "RUIDO",
        content: "Queja por ruido\n\n" +
            "Tipo: {noiseType} | Intensidad: {intensity} | Frecuencia: {frequency}\n\n" +
            "Ubicación: {location}\n\nDescripción: {description}\n\n" +
            "Fecha: {date}\nHora: {time}",
        requiredFields: [
            "noiseType",
            "intensity",
            "frequency",
            "description",
            "location",
            "date",
            "time",
        ],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            noiseType: {
                type: "select",
                label: "Tipo de Ruido",
                required: true,
                options: [
                    { label: "Música", value: "MUSICA" },
                    { label: "Construcción", value: "CONSTRUCCION" },
                    { label: "Fiesta", value: "FIESTA" },
                    { label: "Otro", value: "OTRO" },
                ],
            },
            intensity: {
                type: "select",
                label: "Intensidad",
                required: true,
                options: [
                    { label: "Baja", value: "BAJA" },
                    { label: "Media", value: "MEDIA" },
                    { label: "Alta", value: "ALTA" },
                ],
            },
            frequency: {
                type: "select",
                label: "Frecuencia",
                required: true,
                options: [
                    { label: "Ocasional", value: "OCASIONAL" },
                    { label: "Frecuente", value: "FRECUENTE" },
                    { label: "Constante", value: "CONSTANTE" },
                ],
            },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    /* ───────────── 4. SEGURIDAD ───────────── */
    {
        title: "Problema de Seguridad",
        category: "SEGURIDAD",
        content: "Reporte de seguridad\n\nTipo: {securityIssueType}\n\n" +
            "Ubicación: {location}\n\nDescripción: {description}\n\n" +
            "Fecha: {date}\nHora: {time}",
        requiredFields: [
            "securityIssueType",
            "description",
            "location",
            "date",
            "time",
        ],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            securityIssueType: {
                type: "select",
                label: "Tipo de Problema",
                required: true,
                options: [
                    { label: "Sospecha", value: "SOSPECHA" },
                    { label: "Robo", value: "ROBO" },
                    { label: "Vandalismo", value: "VANDALISMO" },
                    { label: "Otro", value: "OTRO" },
                ],
            },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    /* ───────────── 5. MASCOTAS ───────────── */
    {
        title: "Problema con Mascotas",
        category: "CONVIVENCIA",
        subcategory: "MASCOTAS",
        content: "Reporte sobre mascotas\n\nTipo de mascota: {petType}\n\n" +
            "Ubicación: {location}\n\nDescripción: {description}\n\n" +
            "Fecha: {date}\nHora: {time}",
        requiredFields: ["petType", "description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            petType: {
                type: "select",
                label: "Tipo de Mascota",
                required: true,
                options: [
                    { label: "Perro", value: "PERRO" },
                    { label: "Gato", value: "GATO" },
                    { label: "Otro", value: "OTRO" },
                ],
            },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    /* ───────────── 6. PARQUEADERO ───────────── */
    {
        title: "Problema de Parqueadero",
        category: "CONVIVENCIA",
        subcategory: "PARQUEADERO",
        content: "Problema de parqueadero\n\nTipo: {parkingIssueType}\nPlaca: {licensePlate}\n\n" +
            "Ubicación: {location}\n\nDescripción: {description}\n\n" +
            "Fecha: {date}\nHora: {time}",
        requiredFields: [
            "parkingIssueType",
            "licensePlate",
            "description",
            "location",
            "date",
            "time",
        ],
        fields: {
            parkingIssueType: {
                type: "select",
                label: "Tipo de Problema",
                required: true,
                options: [
                    { label: "Ocupación indebida", value: "OCUPACION_INDEBIDA" },
                    { label: "Bloqueo", value: "BLOQUEO" },
                    { label: "Daños", value: "DANOS" },
                    { label: "Otro", value: "OTRO" },
                ],
            },
            licensePlate: {
                type: "text",
                label: "Placa del Vehículo",
                required: true,
            },
            description: { type: "textarea", label: "Descripción", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
        },
    },
    /* ───────────── 7. QUEJA SOBRE RESIDENTE ───────────── */
    {
        title: "Queja sobre Residente",
        category: "CONVIVENCIA",
        subcategory: "RESIDENTE",
        content: "Queja sobre residente\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    /* ───────────── 8. OTRA SOLICITUD ───────────── */
    {
        title: "Otra Solicitud",
        category: "OTRO",
        content: "Otra solicitud\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    /* ──────────── NUEVAS ──────────── */
    {
        title: "Queja por Basura o Residuos",
        category: "CONVIVENCIA",
        subcategory: "BASURA",
        content: "Queja por mala disposición de basura\n\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Reporte de Iluminación Deficiente",
        category: "MANTENIMIENTO",
        subcategory: "ILUMINACION",
        content: "Reporte de iluminación deficiente\n\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Reporte de Fuga de Agua",
        category: "MANTENIMIENTO",
        subcategory: "AGUA",
        content: "Reporte de fuga de agua\n\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Solicitud de Jardinería",
        category: "MANTENIMIENTO",
        subcategory: "JARDINERIA",
        content: "Solicitud de jardinería\n\nÁrea a intervenir: {location}\n\nDescripción: {description}\n\nFecha propuesta: {date}\nHora propuesta: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Área", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Reporte de Vandalismo",
        category: "SEGURIDAD",
        subcategory: "VANDALISMO",
        content: "Reporte de acto vandálico\n\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Reporte de Robo",
        category: "SEGURIDAD",
        subcategory: "ROBO",
        content: "Reporte de robo\n\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Queja por Mal Uso de Áreas Comunes",
        category: "CONVIVENCIA",
        subcategory: "AREAS_COMUNES",
        content: "Queja por mal uso de áreas comunes\n\nÁrea afectada: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Área", required: true },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
    {
        title: "Interrupción de Servicios Públicos",
        category: "OTRO",
        subcategory: "SERVICIOS_PUBLICOS",
        content: "Reporte de interrupción de servicio público\n\nServicio afectado: {serviceType}\nUbicación: {location}\n\nDescripción: {description}\n\nFecha: {date}\nHora: {time}",
        requiredFields: ["serviceType", "description", "location", "date", "time"],
        fields: {
            date: { type: "date", label: "Fecha", required: true },
            time: { type: "time", label: "Hora", required: true },
            location: { type: "text", label: "Ubicación", required: true },
            serviceType: {
                type: "text",
                label: "Servicio afectado (agua, luz, etc.)",
                required: true,
            },
            description: { type: "textarea", label: "Descripción", required: true },
        },
    },
];
