import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { notification } from "antd";
import { ReservationData } from "../types/reservation.types";

export const useCreateReservation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ReservationData) =>
      Http.post("/api/reservations", {
        data: {
          date: data.date.format("YYYY-MM-DD"),
          startTime: data.startTime.format("HH:mm:ss.SSS"),
          endTime: data.endTime.format("HH:mm:ss.SSS"),
          socialArea: data.socialArea,
          eventType: data.eventType,
          guests: data.guests,
        },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["all-reservations"] });
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
      queryClient.invalidateQueries({ queryKey: ["reservations", "admin"] });

      notification.success({
        message: "Reserva creada",
        description: "La reserva se ha creado correctamente",
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error?.message ||
        "Error al crear la reserva. Por favor, inténtalo de nuevo.";

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    },
  });
};
