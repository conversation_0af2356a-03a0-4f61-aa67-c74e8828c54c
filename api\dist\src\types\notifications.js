"use strict";
/**
 * Tipos de notificaciones del sistema
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createNotification = exports.getNotificationConfig = exports.NotificationConfig = exports.NotificationPriority = exports.NotificationType = void 0;
var NotificationType;
(function (NotificationType) {
    // Notificaciones generales
    NotificationType["INFO"] = "info";
    NotificationType["SUCCESS"] = "success";
    NotificationType["WARNING"] = "warning";
    NotificationType["ERROR"] = "error";
    // Notificaciones de huéspedes
    NotificationType["GUEST_ARRIVAL"] = "guest_arrival";
    NotificationType["GUEST_CONFIRMATION"] = "guest_confirmation";
    NotificationType["GUEST_CHECKOUT"] = "guest_checkout";
    NotificationType["GUEST_LATE_ARRIVAL"] = "guest_late_arrival";
    // Notificaciones de reservas
    NotificationType["NEW_RESERVATION"] = "new_reservation";
    NotificationType["RESERVATION_CANCELLED"] = "reservation_cancelled";
    NotificationType["RESERVATION_MODIFIED"] = "reservation_modified";
    NotificationType["PAYMENT_RECEIVED"] = "payment_received";
    // Notificaciones del sistema
    NotificationType["SYSTEM_MAINTENANCE"] = "system_maintenance";
    NotificationType["SYSTEM_UPDATE"] = "system_update";
    NotificationType["BACKUP_COMPLETED"] = "backup_completed";
    // Notificaciones de staff
    NotificationType["STAFF_MESSAGE"] = "staff_message";
    NotificationType["SHIFT_CHANGE"] = "shift_change";
    NotificationType["TASK_ASSIGNED"] = "task_assigned";
    // Alertas críticas
    NotificationType["SECURITY_ALERT"] = "security_alert";
    NotificationType["EMERGENCY"] = "emergency";
    NotificationType["SYSTEM_ERROR"] = "system_error";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority["LOW"] = "low";
    NotificationPriority["NORMAL"] = "normal";
    NotificationPriority["HIGH"] = "high";
    NotificationPriority["CRITICAL"] = "critical";
})(NotificationPriority || (exports.NotificationPriority = NotificationPriority = {}));
// Configuraciones de notificaciones por tipo
exports.NotificationConfig = {
    [NotificationType.GUEST_ARRIVAL]: {
        icon: '🏨',
        color: 'blue',
        sound: true,
        persistent: true,
        priority: NotificationPriority.HIGH,
    },
    [NotificationType.GUEST_CONFIRMATION]: {
        icon: '✅',
        color: 'green',
        sound: true,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.GUEST_CHECKOUT]: {
        icon: '🚪',
        color: 'orange',
        sound: false,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.NEW_RESERVATION]: {
        icon: '📅',
        color: 'purple',
        sound: true,
        persistent: true,
        priority: NotificationPriority.HIGH,
    },
    [NotificationType.PAYMENT_RECEIVED]: {
        icon: '💰',
        color: 'green',
        sound: true,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.SECURITY_ALERT]: {
        icon: '🚨',
        color: 'red',
        sound: true,
        persistent: true,
        priority: NotificationPriority.CRITICAL,
    },
    [NotificationType.EMERGENCY]: {
        icon: '🆘',
        color: 'red',
        sound: true,
        persistent: true,
        priority: NotificationPriority.CRITICAL,
    },
    [NotificationType.SYSTEM_ERROR]: {
        icon: '⚠️',
        color: 'red',
        sound: false,
        persistent: true,
        priority: NotificationPriority.HIGH,
    },
    [NotificationType.STAFF_MESSAGE]: {
        icon: '💬',
        color: 'blue',
        sound: false,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.SYSTEM_MAINTENANCE]: {
        icon: '🔧',
        color: 'yellow',
        sound: false,
        persistent: true,
        priority: NotificationPriority.HIGH,
    },
    [NotificationType.INFO]: {
        icon: 'ℹ️',
        color: 'blue',
        sound: false,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.SUCCESS]: {
        icon: '✅',
        color: 'green',
        sound: false,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.WARNING]: {
        icon: '⚠️',
        color: 'orange',
        sound: false,
        persistent: false,
        priority: NotificationPriority.NORMAL,
    },
    [NotificationType.ERROR]: {
        icon: '❌',
        color: 'red',
        sound: false,
        persistent: false,
        priority: NotificationPriority.HIGH,
    },
};
// Funciones helper
const getNotificationConfig = (type) => {
    return exports.NotificationConfig[type] || exports.NotificationConfig[NotificationType.INFO];
};
exports.getNotificationConfig = getNotificationConfig;
const createNotification = (type, title, message, options = {}) => {
    const config = (0, exports.getNotificationConfig)(type);
    return {
        type,
        priority: config.priority,
        title: `${config.icon} ${title}`,
        message,
        read: false,
        createdAt: new Date().toISOString(),
        ...options,
    };
};
exports.createNotification = createNotification;
