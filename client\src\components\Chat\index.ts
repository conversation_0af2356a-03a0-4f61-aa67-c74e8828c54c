/**
 * Exportaciones principales del sistema de chat
 */

// Componente principal
export { ChatContainer } from "./ChatContainer/ChatContainer";

// Componentes individuales
export { ChatChannelList } from "./ChatChannelList/ChatChannelList";
export { ChatMessageList } from "./ChatMessageList/ChatMessageList";
export { ChatMessage } from "./ChatMessage/ChatMessage";
export { ChatInput } from "./ChatInput/ChatInput";
export { ChatHeader } from "./ChatHeader/ChatHeader";

// Componentes auxiliares
export { MessageReactions } from "./ChatMessage/MessageReactions";
export { EmojiPicker } from "./ChatMessage/EmojiPicker";

// Hooks
export { useChat } from "../../hooks/chat/useChat";
export { useWebSocketChat } from "../../hooks/useWebSocketChat";
export {
  useChatChannels,
  useCreateChatChannel,
} from "../../hooks/chat/useChatChannels";
export {
  useChatMessages,
  useSendChatMessage,
} from "../../hooks/chat/useChatMessages";

// Servicios
export { chatService } from "../../services/chat.service";

// Tipos
export type * from "../../types/chat";
