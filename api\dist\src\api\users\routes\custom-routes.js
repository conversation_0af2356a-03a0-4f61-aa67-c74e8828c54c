module.exports = {
    routes: [
        // Eliminamos la ruta /users/me porque está siendo manejada por la extensión del plugin
        // Mantenemos solo la ruta de actualización de usuario
        {
            method: "PUT",
            path: "/users/:id",
            handler: "user.update",
            config: {
                prefix: "api",
                policies: [],
                middlewares: [],
            },
        },
        // Nueva ruta para obtener usuarios públicos
        {
            method: "GET",
            path: "/users/public",
            handler: "public-users.findPublic",
            config: {
                prefix: "api",
                policies: [],
                middlewares: [],
            },
        },
    ],
};
