import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { notification } from "antd";

export const useDeleteReservation = () => {
  const queryClient = useQueryClient();

  const { mutate: deleteReservation, isPending: isLoading } = useMutation({
    mutationFn: async (id: string) => {
      const response = await Http.delete(`/api/reservations/${id}`);
      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "Reserva eliminada",
        description: "La reserva ha sido eliminada exitosamente.",
      });
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
    onError: (error: any) => {
      notification.error({
        message: "Error",
        description: error.message || "Hubo un error al eliminar la reserva.",
      });
    },
  });

  return {
    deleteReservation,
    isLoading,
  };
};
