/**
 * Componente para mostrar la lista de canales de chat
 */

import React from "react";
import { Badge, Typography, Skeleton, Empty, Tooltip } from "antd";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import type { ChatChannelListProps } from "@app/types/chat";
import * as S from "./ChatChannelList.styles";

const { Text } = Typography;

export const ChatChannelList: React.FC<ChatChannelListProps> = ({
  channels,
  selectedChannelId,
  onChannelSelect,
  loading = false,
}) => {
  if (loading) {
    return (
      <S.ChannelListContainer>
        <S.ChannelListHeader>
          <Text strong>Canales</Text>
        </S.ChannelListHeader>
        <S.ChannelList>
          {Array.from({ length: 5 }).map((_, index) => (
            <S.ChannelItem key={index} $isSelected={false} $hasUnread={false}>
              <Skeleton.Avatar size="small" />
              <div style={{ flex: 1, marginLeft: 8 }}>
                <Skeleton.Input style={{ width: "80%", height: 16 }} active />
                <Skeleton.Input
                  style={{ width: "60%", height: 12, marginTop: 4 }}
                  active
                />
              </div>
            </S.ChannelItem>
          ))}
        </S.ChannelList>
      </S.ChannelListContainer>
    );
  }

  if (channels.length === 0) {
    return (
      <S.ChannelListContainer>
        <S.ChannelListHeader>
          <Text strong>Canales</Text>
        </S.ChannelListHeader>
        <S.EmptyContainer>
          <Empty
            description="No hay canales disponibles"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </S.EmptyContainer>
      </S.ChannelListContainer>
    );
  }

  return (
    <S.ChannelListContainer>
      <S.ChannelListHeader>
        <Text strong>Canales</Text>
        <Badge count={channels.length} size="small" />
      </S.ChannelListHeader>

      <S.ChannelList>
        {channels.map((channel) => {
          const isSelected = channel.id === selectedChannelId;
          const hasUnread = false; // TODO: Implementar lógica de no leídos
          const lastMessageTime =
            channel.lastMessage?.createdAt || channel.lastActivity;

          return (
            <S.ChannelItem
              key={channel.id}
              $isSelected={isSelected}
              $hasUnread={hasUnread}
              onClick={() => onChannelSelect(channel.id)}
            >
              {/* Icono del canal */}
              <S.ChannelIcon style={{ backgroundColor: channel.color }}>
                {channel.icon}
              </S.ChannelIcon>

              {/* Información del canal */}
              <S.ChannelInfo>
                <S.ChannelHeader>
                  <S.ChannelName $hasUnread={hasUnread}>
                    {channel.name}
                  </S.ChannelName>
                  {lastMessageTime && (
                    <S.ChannelTime>
                      {formatDistanceToNow(new Date(lastMessageTime), {
                        addSuffix: false,
                        locale: es,
                      })}
                    </S.ChannelTime>
                  )}
                </S.ChannelHeader>

                <S.ChannelPreview>
                  {channel.lastMessage ? (
                    <Text ellipsis>
                      <Text type="secondary" style={{ fontSize: "11px" }}>
                        {channel.lastMessage.author.displayName ||
                          channel.lastMessage.author.username}
                        :
                      </Text>{" "}
                      {channel.lastMessage.content}
                    </Text>
                  ) : (
                    <Text type="secondary" style={{ fontSize: "11px" }}>
                      {channel.description || "No hay mensajes"}
                    </Text>
                  )}
                </S.ChannelPreview>
              </S.ChannelInfo>

              {/* Indicadores */}
              <S.ChannelIndicators>
                {hasUnread && (
                  <S.UnreadBadge>
                    {/* TODO: Mostrar número de mensajes no leídos */}•
                  </S.UnreadBadge>
                )}

                {!channel.canWrite && (
                  <Tooltip title="Solo lectura">
                    <S.ReadOnlyIndicator>👁️</S.ReadOnlyIndicator>
                  </Tooltip>
                )}
              </S.ChannelIndicators>
            </S.ChannelItem>
          );
        })}
      </S.ChannelList>
    </S.ChannelListContainer>
  );
};
