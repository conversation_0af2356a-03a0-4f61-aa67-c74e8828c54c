import { Switch, Typography, message } from "antd";
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import { BaseButtonsForm } from "@app/components/common/forms/BaseButtonsForm/BaseButtonsForm";
import { BaseSpace } from "@app/components/common/BaseSpace/BaseSpace";
import { useAppSelector } from "@app/hooks/reduxHooks";
import { useUpdateUserProfile } from "@app/pages/DataUsersTablesPage/hook/use-update-user-profile";
import { useState, useEffect } from "react";

const { Text } = Typography;

export const ProfilePrivacySwitch = () => {
  const user = useAppSelector((state) => state.user.user);
  const { updateUserProfile, isPending } = useUpdateUserProfile();
  const [localValue, setLocalValue] = useState(false);

  // Inicializar el valor local basado en el usuario
  useEffect(() => {
    if (user) {
      // Solo residentes y arrendatarios pueden tener isPublic false
      const roleName = user.role?.type?.toLowerCase();
      if (["residente", "arrendatario"].includes(roleName || "")) {
        setLocalValue(user.isPublic !== undefined ? user.isPublic : false);
      } else {
        setLocalValue(true); // Otros roles siempre públicos
      }
    }
  }, [user]);

  const handleChange = async (checked: boolean) => {
    if (!user) return;

    // Verificar si el usuario puede cambiar la privacidad
    const roleName = user.role?.type?.toLowerCase();
    if (!["residente", "arrendatario"].includes(roleName || "")) {
      message.info("Los usuarios con tu rol siempre tienen perfil público");
      return;
    }

    setLocalValue(checked);

    try {
      await updateUserProfile({
        userId: user.id,
        payload: { isPublic: checked },
      });

      message.success(
        checked
          ? "Tu perfil ahora es público y aparece en el directorio"
          : "Tu perfil ahora es privado y no aparece en el directorio",
      );
    } catch (error) {
      // Revertir el cambio local si falla
      setLocalValue(!checked);
      message.error("Error al actualizar la configuración de privacidad");
    }
  };

  // Solo mostrar para residentes y arrendatarios
  const roleName = user?.role?.type?.toLowerCase();
  if (!["residente", "arrendatario"].includes(roleName || "")) {
    return (
      <BaseButtonsForm.Item
        label="Visibilidad del Perfil"
        tooltip="Tu rol siempre tiene perfil público en el directorio"
      >
        <BaseSpace direction="vertical" size="small" style={{ width: "100%" }}>
          <Switch
            checked={true}
            disabled={true}
            checkedChildren={<EyeOutlined />}
            unCheckedChildren={<EyeInvisibleOutlined />}
            style={{ marginBottom: 8 }}
          />
          <Text type="secondary" style={{ fontSize: "12px" }}>
            <EyeOutlined style={{ color: "#52c41a", marginRight: 4 }} />
            <strong>Siempre Público:</strong> Tu rol aparece automáticamente en
            el directorio
          </Text>
        </BaseSpace>
      </BaseButtonsForm.Item>
    );
  }

  return (
    <BaseButtonsForm.Item
      label="Visibilidad del Perfil"
      name="isPublic"
      tooltip="Controla si tu perfil aparece en el directorio público para que otros residentes puedan contactarte"
      valuePropName="checked"
    >
      <BaseSpace direction="vertical" size="small" style={{ width: "100%" }}>
        <Switch
          checked={localValue}
          onChange={handleChange}
          loading={isPending}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
          style={{ marginBottom: 8 }}
        />
        <Text type="secondary" style={{ fontSize: "12px" }}>
          {localValue ? (
            <>
              <EyeOutlined style={{ color: "#52c41a", marginRight: 4 }} />
              <strong>Público:</strong> Tu perfil aparece en el directorio para
              que otros residentes puedan contactarte
            </>
          ) : (
            <>
              <EyeInvisibleOutlined
                style={{ color: "#ff4d4f", marginRight: 4 }}
              />
              <strong>Privado:</strong> Tu perfil no aparece en el directorio
              público
            </>
          )}
        </Text>
      </BaseSpace>
    </BaseButtonsForm.Item>
  );
};
