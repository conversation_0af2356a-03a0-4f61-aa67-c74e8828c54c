/**
 * Hook principal para manejar el estado del chat
 */

import { useState, useEffect, useCallback, useMemo } from "react";
import { message as antMessage } from "antd";
import { useWebSocketChat } from "./useWebSocketChat";
import { chatService } from "@app/services/chat.service";
import { readUser } from "@app/services/localStorage.service";
import type {
  ChatChannel,
  ChatMessage,
  ChatUIState,
  UseChatReturn,
  ChatTypingIndicator,
  CreateChatMessageRequest,
} from "@app/types/chat";

export const useChat = (): UseChatReturn => {
  // Estado principal
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(
    null,
  );

  // Estados de carga
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);

  // Estado de UI
  const [uiState, setUIState] = useState<ChatUIState>({
    selectedChannelId: null,
    isLoading: false,
    error: null,
    typingUsers: {},
    onlineUsers: {},
    replyingTo: null,
    editingMessage: null,
    searchQuery: "",
    searchResults: [],
  });

  // Usuario actual
  const currentUser = readUser();
  const currentUserId = currentUser?.id || 0;

  // Canal seleccionado
  const selectedChannel = useMemo(() => {
    return channels.find((channel) => channel.id === selectedChannelId) || null;
  }, [channels, selectedChannelId]);

  // Callbacks para WebSocket
  const handleMessageReceived = useCallback(
    (newMessage: ChatMessage) => {
      // Solo agregar si es del canal actual
      if (newMessage.channel?.id === selectedChannelId) {
        setMessages((prev) => {
          // Evitar duplicados
          const exists = prev.some((msg) => msg.id === newMessage.id);
          if (exists) return prev;

          return [...prev, newMessage];
        });
      }

      // Actualizar último mensaje del canal
      setChannels((prev) =>
        prev.map((channel) => {
          if (channel.id === newMessage.channel?.id) {
            return {
              ...channel,
              lastMessage: newMessage,
              lastActivity: newMessage.createdAt,
            };
          }
          return channel;
        }),
      );
    },
    [selectedChannelId],
  );

  const handleMessageUpdated = useCallback(
    (messageId: number, updates: Partial<ChatMessage>) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, ...updates } : msg,
        ),
      );
    },
    [],
  );

  const handleMessageDeleted = useCallback((messageId: number) => {
    setMessages((prev) =>
      prev.map((msg) =>
        msg.id === messageId
          ? { ...msg, isDeleted: true, content: "[Mensaje eliminado]" }
          : msg,
      ),
    );
  }, []);

  const handleReactionUpdated = useCallback(
    (messageId: number, reactions: Record<string, number[]>) => {
      setMessages((prev) =>
        prev.map((msg) => (msg.id === messageId ? { ...msg, reactions } : msg)),
      );
    },
    [],
  );

  const handleTypingUpdate = useCallback((typingData: ChatTypingIndicator) => {
    setUIState((prev) => {
      const channelTyping = prev.typingUsers[typingData.channelId] || [];

      if (typingData.isTyping) {
        // Agregar usuario a la lista de escribiendo
        const exists = channelTyping.some(
          (user) => user.userId === typingData.userId,
        );
        if (!exists) {
          return {
            ...prev,
            typingUsers: {
              ...prev.typingUsers,
              [typingData.channelId]: [...channelTyping, typingData],
            },
          };
        }
      } else {
        // Quitar usuario de la lista de escribiendo
        return {
          ...prev,
          typingUsers: {
            ...prev.typingUsers,
            [typingData.channelId]: channelTyping.filter(
              (user) => user.userId !== typingData.userId,
            ),
          },
        };
      }

      return prev;
    });
  }, []);

  const handleUserPresenceUpdate = useCallback(
    (userId: number, isOnline: boolean) => {
      setUIState((prev) => ({
        ...prev,
        onlineUsers: {
          ...prev.onlineUsers,
          [userId]: isOnline,
        },
      }));
    },
    [],
  );

  // WebSocket hook
  const { isConnected, connectionError, sendTypingIndicator } =
    useWebSocketChat({
      onMessageReceived: handleMessageReceived,
      onMessageUpdated: handleMessageUpdated,
      onMessageDeleted: handleMessageDeleted,
      onReactionUpdated: handleReactionUpdated,
      onTypingUpdate: handleTypingUpdate,
      onUserPresenceUpdate: handleUserPresenceUpdate,
    });

  // Cargar canales
  const loadChannels = useCallback(async () => {
    setIsLoadingChannels(true);
    try {
      const response = await chatService.getChannels();
      setChannels(response.data);

      // Seleccionar primer canal si no hay ninguno seleccionado
      if (!selectedChannelId && response.data.length > 0) {
        setSelectedChannelId(response.data[0].id);
      }
    } catch (error) {
      console.error("Error cargando canales:", error);
      antMessage.error("Error cargando canales de chat");
      setUIState((prev) => ({ ...prev, error: "Error cargando canales" }));
    } finally {
      setIsLoadingChannels(false);
    }
  }, [selectedChannelId]);

  // Cargar mensajes de un canal
  const loadMessages = useCallback(async (channelId: number) => {
    setIsLoadingMessages(true);
    try {
      const response = await chatService.getChannel(channelId);
      setMessages(response.data.messages || []);
    } catch (error) {
      console.error("Error cargando mensajes:", error);
      antMessage.error("Error cargando mensajes");
      setUIState((prev) => ({ ...prev, error: "Error cargando mensajes" }));
    } finally {
      setIsLoadingMessages(false);
    }
  }, []);

  // Seleccionar canal
  const selectChannel = useCallback((channelId: number) => {
    setSelectedChannelId(channelId);
    setUIState((prev) => ({
      ...prev,
      selectedChannelId: channelId,
      replyingTo: null,
      editingMessage: null,
    }));
  }, []);

  // Enviar mensaje
  const sendMessage = useCallback(
    async (content: string, replyTo?: number) => {
      if (!selectedChannelId || !content.trim()) return;

      setIsSendingMessage(true);
      try {
        const messageData: CreateChatMessageRequest = {
          channelId: selectedChannelId,
          content: content.trim(),
          messageType: "text",
          replyTo,
        };

        const response = await chatService.sendMessage(messageData);

        // El mensaje se agregará automáticamente vía WebSocket
        // pero lo agregamos localmente para feedback inmediato
        setMessages((prev) => [...prev, response.data]);

        // Limpiar estado de respuesta
        setUIState((prev) => ({ ...prev, replyingTo: null }));
      } catch (error) {
        console.error("Error enviando mensaje:", error);
        antMessage.error("Error enviando mensaje");
      } finally {
        setIsSendingMessage(false);
      }
    },
    [selectedChannelId],
  );

  // Editar mensaje
  const editMessage = useCallback(
    async (messageId: number, content: string) => {
      try {
        await chatService.editMessage(messageId, { content });
        setUIState((prev) => ({ ...prev, editingMessage: null }));
        antMessage.success("Mensaje editado");
      } catch (error) {
        console.error("Error editando mensaje:", error);
        antMessage.error("Error editando mensaje");
      }
    },
    [],
  );

  // Eliminar mensaje
  const deleteMessage = useCallback(async (messageId: number) => {
    try {
      await chatService.deleteMessage(messageId);
      antMessage.success("Mensaje eliminado");
    } catch (error) {
      console.error("Error eliminando mensaje:", error);
      antMessage.error("Error eliminando mensaje");
    }
  }, []);

  // Agregar reacción
  const addReaction = useCallback(async (messageId: number, emoji: string) => {
    try {
      await chatService.addReaction(messageId, { emoji });
    } catch (error) {
      console.error("Error agregando reacción:", error);
      antMessage.error("Error agregando reacción");
    }
  }, []);

  // Buscar mensajes
  const searchMessages = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setUIState((prev) => ({ ...prev, searchQuery: "", searchResults: [] }));
        return;
      }

      try {
        const response = await chatService.searchMessages(
          query,
          selectedChannelId || undefined,
        );
        setUIState((prev) => ({
          ...prev,
          searchQuery: query,
          searchResults: response.data.results,
        }));
      } catch (error) {
        console.error("Error buscando mensajes:", error);
        antMessage.error("Error en la búsqueda");
      }
    },
    [selectedChannelId],
  );

  // Setters para UI state
  const setReplyingTo = useCallback((message: ChatMessage | null) => {
    setUIState((prev) => ({ ...prev, replyingTo: message }));
  }, []);

  const setEditingMessage = useCallback((message: ChatMessage | null) => {
    setUIState((prev) => ({ ...prev, editingMessage: message }));
  }, []);

  // Efectos
  useEffect(() => {
    loadChannels();
  }, []);

  useEffect(() => {
    if (selectedChannelId) {
      loadMessages(selectedChannelId);
    }
  }, [selectedChannelId, loadMessages]);

  // Actualizar estado de conexión en UI
  useEffect(() => {
    setUIState((prev) => ({
      ...prev,
      error: connectionError,
    }));
  }, [connectionError]);

  return {
    // State
    channels,
    selectedChannel,
    messages,
    uiState: {
      ...uiState,
      selectedChannelId,
    },

    // Actions
    selectChannel,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    searchMessages,
    setReplyingTo,
    setEditingMessage,

    // Loading states
    isLoadingChannels,
    isLoadingMessages,
    isSendingMessage,
  };
};
