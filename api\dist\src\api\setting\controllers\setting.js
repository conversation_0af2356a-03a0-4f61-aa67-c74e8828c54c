"use strict";
/**
 * setting controller
 */
const { createCoreController } = require("@strapi/strapi").factories;
module.exports = createCoreController("api::setting.setting", ({ strapi }) => ({
    async find(ctx) {
        var _a;
        const { user } = ctx.state;
        const isAdmin = ((_a = user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin";
        if (!isAdmin) {
            return ctx.forbidden("Only administrators can access settings");
        }
        const response = await super.find(ctx);
        return response;
    },
    async update(ctx) {
        var _a;
        const { user } = ctx.state;
        const isAdmin = ((_a = user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin";
        if (!isAdmin) {
            return ctx.forbidden("Only administrators can modify settings");
        }
        const response = await super.update(ctx);
        return response;
    },
}));
