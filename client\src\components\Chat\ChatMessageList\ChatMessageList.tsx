/**
 * Componente para mostrar la lista de mensajes del chat
 */

import React, { useEffect, useRef, useMemo, useState } from "react";
import { Spin, Empty, Divider, Typography, Button } from "antd";
import { format, isToday, isYesterday } from "date-fns";
import { es } from "date-fns/locale";
import { DownOutlined } from "@ant-design/icons";
import { ChatMessage } from "../ChatMessage/ChatMessage";
import type { ChatMessageListProps } from "@app/types/chat";
import * as S from "./ChatMessageList.styles";

const { Text } = Typography;

export const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  loading,
  onReply,
  onEdit,
  onDelete,
  onReaction,
  currentUserId,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // Detectar si el usuario está haciendo scroll manual
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px de tolerancia
      setIsUserScrolling(!isAtBottom);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll automático al final cuando llegan nuevos mensajes (solo si no está scrolleando)
  useEffect(() => {
    if (messagesEndRef.current && !isUserScrolling) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isUserScrolling]);

  // Función para ir al final
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      setIsUserScrolling(false);
    }
  };

  // Agrupar mensajes por fecha
  const groupedMessages = useMemo(() => {
    const groups: Array<{
      date: string;
      dateLabel: string;
      messages: Array<{
        message: any;
        showAvatar: boolean;
        compact: boolean;
      }>;
    }> = [];

    let currentDate = "";
    let currentGroup: any = null;

    messages.forEach((message, index) => {
      const messageDate = format(new Date(message.createdAt), "yyyy-MM-dd");

      // Nueva fecha, crear nuevo grupo
      if (messageDate !== currentDate) {
        currentDate = messageDate;

        // Formatear etiqueta de fecha
        const date = new Date(message.createdAt);
        let dateLabel: string;

        if (isToday(date)) {
          dateLabel = "Hoy";
        } else if (isYesterday(date)) {
          dateLabel = "Ayer";
        } else {
          dateLabel = format(date, "EEEE, d MMMM yyyy", { locale: es });
        }

        currentGroup = {
          date: messageDate,
          dateLabel,
          messages: [],
        };
        groups.push(currentGroup);
      }

      // Determinar si mostrar avatar y si usar formato compacto
      const prevMessage = index > 0 ? messages[index - 1] : null;

      const isSameAuthor = prevMessage?.author.id === message.author.id;
      const isWithinTimeWindow =
        prevMessage &&
        new Date(message.createdAt).getTime() -
          new Date(prevMessage.createdAt).getTime() <
          5 * 60 * 1000; // 5 minutos

      const showAvatar = !isSameAuthor || !isWithinTimeWindow;
      const compact = isSameAuthor && isWithinTimeWindow && !message.replyTo;

      currentGroup.messages.push({
        message,
        showAvatar,
        compact,
      });
    });

    return groups;
  }, [messages]);

  if (loading && messages.length === 0) {
    return (
      <S.LoadingContainer>
        <Spin size="large" tip="Cargando mensajes..." />
        <Text
          type="secondary"
          style={{ marginTop: "16px", textAlign: "center" }}
        >
          Conectando con el servidor...
        </Text>
      </S.LoadingContainer>
    );
  }

  if (messages.length === 0) {
    return (
      <S.EmptyContainer>
        <Empty
          description={
            <div style={{ textAlign: "center" }}>
              <Text
                type="secondary"
                style={{
                  fontSize: "16px",
                  display: "block",
                  marginBottom: "8px",
                }}
              >
                ¡Sé el primero en escribir! 🎉
              </Text>
              <Text type="secondary" style={{ fontSize: "14px" }}>
                Este canal está esperando su primer mensaje.
              </Text>
            </div>
          }
          image={
            <div style={{ fontSize: "48px", marginBottom: "16px" }}>💬</div>
          }
        />
      </S.EmptyContainer>
    );
  }

  return (
    <S.MessagesContainer ref={containerRef}>
      {groupedMessages.map((group) => (
        <div key={group.date}>
          {/* Separador de fecha */}
          <S.DateSeparator>
            <Divider>
              <Text type="secondary" style={{ fontSize: "12px" }}>
                {group.dateLabel}
              </Text>
            </Divider>
          </S.DateSeparator>

          {/* Mensajes del día */}
          {group.messages.map(({ message, showAvatar, compact }) => (
            <ChatMessage
              key={message.id}
              message={message}
              currentUserId={currentUserId}
              onReply={onReply}
              onEdit={onEdit}
              onDelete={onDelete}
              onReaction={onReaction}
              showAvatar={showAvatar}
              compact={compact}
            />
          ))}
        </div>
      ))}

      {/* Indicador de carga para mensajes adicionales */}
      {loading && messages.length > 0 && (
        <S.LoadingMore>
          <Spin size="small" />
        </S.LoadingMore>
      )}

      {/* Botón flotante para ir al final */}
      {isUserScrolling && (
        <div
          style={{
            position: "absolute",
            bottom: "20px",
            right: "20px",
            zIndex: 10,
          }}
        >
          <Button
            type="primary"
            shape="circle"
            icon={<DownOutlined />}
            onClick={scrollToBottom}
            size="small"
            style={{
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
            }}
          />
        </div>
      )}

      {/* Referencia para scroll automático */}
      <div ref={messagesEndRef} />
    </S.MessagesContainer>
  );
};
