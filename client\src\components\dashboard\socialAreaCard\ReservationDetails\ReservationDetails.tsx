import { useRef } from "react";
import { Descriptions, List, Button, Tag, Row, Col } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import { jsPDF } from "jspdf";
import dayjs from "dayjs";
import { useCurrentUser } from "@app/hooks/useCurrentUser";
import type { Reservation } from "../types/reservation.types";
import { getLabelPropuse } from "@app/pages/CommonAreasPage/components/ReservationDetails/components/ReservationInfo";

export const ReservationDetails = ({
  reservation,
}: {
  reservation: Reservation;
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const { user } = useCurrentUser();

  const canDownloadPDF = () => {
    if (!user) return false;
    if (user.role?.type === "admin") return true;
    if (user.id === reservation.attributes.owner.data.id) return true;
    return false;
  };

  const handleDownloadPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text("Detalles de la Reservación", 20, 20);
    doc.setFontSize(12);
    doc.text(`Estado: ${reservation.attributes.status}`, 20, 35);
    doc.text(`Área: ${getAreaName(reservation.attributes.socialArea)}`, 20, 45);
    doc.text(
      `Fecha: ${dayjs(reservation.attributes.date).format("DD/MM/YYYY")}`,
      20,
      55,
    );
    doc.text(
      `Hora: ${formatHour(reservation.attributes.startTime)} - ${formatHour(
        reservation.attributes.endTime,
      )}`,
      20,
      65,
    );
    doc.text(
      `Solicitante: ${reservation.attributes.owner.data.attributes.firstName} ${
        reservation.attributes.owner.data.attributes.lastName
      }`,
      20,
      75,
    );
    doc.text(`Propósito: ${reservation.attributes.eventType}`, 20, 85);

    doc.text("Lista de Invitados:", 20, 100);
    let y = 110;
    if (reservation.attributes.guests?.length > 0) {
      reservation.attributes.guests.forEach((guest, index) => {
        doc.text(`${index + 1}. ${guest.name} ${guest.lastName}`, 25, y);
        y += 10;
      });
    } else {
      doc.text("Sin invitados registrados", 25, y);
    }

    doc.save(
      `reservacion_${dayjs(reservation.attributes.date).format(
        "DDMMYYYY",
      )}.pdf`,
    );
  };

  const getStatusTag = (status: string) => {
    const colors: Record<string, string> = {
      pending: "warning",
      approved: "success",
      rejected: "error",
    };
    const texts: Record<string, string> = {
      pending: "Pendiente",
      approved: "Confirmada",
      rejected: "Rechazada",
    };
    return <Tag color={colors[status]}>{texts[status]}</Tag>;
  };

  const getAreaName = (area: string) => {
    const areaNames = {
      communalHall: "Salón comunal",
      pool: "Piscina",
      bbq: "BBQ",
      terrace: "Terraza",
    };
    return areaNames[area as keyof typeof areaNames] || area || "N/A";
  };

  const formatHour = (time: string) =>
    dayjs(`2000-01-01 ${time}`).format("hh:mm A");

  return (
    <div ref={contentRef} style={{ width: "100%", padding: 16 }}>
      {canDownloadPDF() && (
        <Row justify="center" style={{ marginBottom: 16 }}>
          <Col xs={24} sm={16} md={12} lg={8}>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownloadPDF}
              block
            >
              Descargar PDF
            </Button>
          </Col>
        </Row>
      )}

      <Descriptions
        bordered
        layout="vertical"
        column={{ xs: 1, sm: 1, md: 2, lg: 3 }}
        style={{ background: "#fff" }}
      >
        <Descriptions.Item label="Estado">
          {getStatusTag(reservation.attributes.status)}
        </Descriptions.Item>

        <Descriptions.Item label="Área">
          {getAreaName(reservation.attributes.socialArea)}
        </Descriptions.Item>

        <Descriptions.Item label="Fecha">
          {dayjs(reservation.attributes.date).format("DD/MM/YYYY")}
        </Descriptions.Item>

        <Descriptions.Item label="Hora">
          {`${formatHour(reservation.attributes.startTime)} - ${formatHour(
            reservation.attributes.endTime,
          )}`}
        </Descriptions.Item>

        <Descriptions.Item label="Solicitante">
          {`${
            reservation.attributes.owner.data.attributes.firstName
          } ${reservation.attributes.owner.data.attributes.lastName}`}
        </Descriptions.Item>

        <Descriptions.Item label="Propósito" span={1}>
          {getLabelPropuse(reservation.attributes.eventType)}
        </Descriptions.Item>

        {reservation.attributes.rejectionReason && (
          <Descriptions.Item label="Motivo de Rechazo" span={1}>
            {reservation.attributes.rejectionReason}
          </Descriptions.Item>
        )}

        {canDownloadPDF() && (
          <Descriptions.Item label="Invitados" span={1}>
            <List
              size="small"
              dataSource={reservation.attributes.guests || []}
              locale={{ emptyText: "Sin invitados registrados" }}
              renderItem={(guest, index) => (
                <List.Item>
                  {index + 1}. {guest.name} {guest.lastName}
                </List.Item>
              )}
            />
          </Descriptions.Item>
        )}
      </Descriptions>
    </div>
  );
};
