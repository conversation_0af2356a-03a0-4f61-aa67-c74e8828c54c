import { fetchApi } from "@app/api/fetchApi";

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  address?: string;
  phone?: string;
  status: boolean;
  coefficient: number;
  imgUrl?: {
    url: string;
    formats: {
      thumbnail?: {
        url: string;
        width: number;
        height: number;
      };
      small?: {
        url: string;
        width: number;
        height: number;
      };
      medium?: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
  role: {
    id: number;
    name: string;
    description: string;
    type: string;
  };
  blocked: boolean;
  confirmed: boolean;
  profession?: string;
  isPublic?: boolean;
  signature?: {
    url: string;
    formats: {
      thumbnail?: {
        url: string;
        width: number;
        height: number;
      };
      small?: {
        url: string;
        width: number;
        height: number;
      };
      medium?: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
}

export interface Role {
  id: number;
  name: string;
  description: string;
  type: string;
}

export const getUsersData = async (): Promise<User[]> => {
  const response = await fetchApi("/api/users?populate=role");
  return response;
};

export const getRolesData = async (): Promise<Role[]> => {
  const response = await fetchApi("/api/users-permissions/roles");
  return response.roles || [];
};

export const updateUserRole = async (
  userId: number,
  roleId: number,
): Promise<User> => {
  const response = await fetchApi(`/api/users/${userId}`, {
    method: "PUT",
    body: JSON.stringify({
      role: roleId,
    }),
  });
  return response;
};
