const mysql = require("mysql2/promise");
require("dotenv").config();
// Banner de inicio
function mostrarBanner() {
    console.log('\n===========================================================');
    console.log('🌟 LIMPIADOR DE BASE DE DATOS - PALMAS PRODUCCIÓN 🌟');
    console.log('===========================================================\n');
    console.log('⚠️ ADVERTENCIA: Este script eliminará todos los datos de la base de datos.');
    console.log('⚠️ Esta operación no se puede deshacer.');
    console.log('\nAcciones que realizará este script:');
    console.log('  • Eliminará completamente algunas tablas de sistema');
    console.log('  • Limpiará el contenido de todas las tablas de contenido');
    console.log('\nDespues de ejecutar este script, deberá ejecutar los scripts de inicialización');
    console.log('para restaurar los datos básicos del sistema.');
    console.log('\n===========================================================\n');
}
async function cleanDatabase() {
    let connection;
    try {
        console.log("🔌 Conectando a la base de datos...");
        connection = await mysql.createConnection({
            host: process.env.DATABASE_HOST,
            port: process.env.DATABASE_PORT,
            user: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_NAME,
        });
        console.log("✅ Conexión exitosa a la base de datos");
        // Lista de tablas a eliminar completamente
        const tablesToDrop = [
            "up_users_role_links",
            "up_users_role_lnk",
            "up_permissions_role_links",
            "up_permissions_role_lnk",
            "admin_permissions_role_links",
            "admin_permissions_role_lnk",
            "admin_users_roles_links",
            "admin_users_roles_lnk",
            "strapi_migrations",
            "strapi_database_schema",
            "strapi_migrations_versions",
            "strapi_migrations_lock"
        ];
        // Eliminar tablas que necesitan ser eliminadas por completo
        console.log("💥 Eliminando tablas completamente...");
        let droppedCount = 0;
        let dropErrorCount = 0;
        for (const table of tablesToDrop) {
            try {
                await connection.execute(`DROP TABLE IF EXISTS ${table}`);
                droppedCount++;
                console.log(`  ✅ Tabla ${table} eliminada`);
            }
            catch (error) {
                dropErrorCount++;
                console.log(`  ❌ Error al eliminar tabla ${table}: ${error.message}`);
            }
        }
        console.log(`✨ ${droppedCount} tablas eliminadas completamente`);
        if (dropErrorCount > 0) {
            console.log(`⚠️ ${dropErrorCount} tablas no pudieron ser eliminadas`);
        }
        // Lista de tablas a limpiar (DELETE FROM)
        console.log("\n🧹 Limpiando contenido de tablas...");
        const tablesToClean = [
            "abouts",
            "admin_permissions",
            "admin_roles",
            "admin_users",
            "articles",
            "articles_author_lnk",
            "articles_category_lnk",
            "articles_cmps",
            "authors",
            "blocks",
            "businesses",
            "businesses_business_user_lnk",
            "categories",
            "components_shared_media",
            "components_shared_quotes",
            "components_shared_rich_texts",
            "components_shared_seos",
            "components_shared_sliders",
            "components_visit_vehicles",
            "components_visit_visitors",
            "dependents",
            "dependents_users_permissions_user_lnk",
            "files",
            "files_folder_lnk",
            "files_related_mph",
            "frequent_visitors",
            "frequent_visitors_user_lnk",
            "globals",
            "globals_cmps",
            "i18n_locale",
            "notifications",
            "notifications_user_lnk",
            "payment_comments",
            "payment_comments_author_lnk",
            "payment_comments_payment_lnk",
            "payments",
            "payments_owner_lnk",
            "pets",
            "pets_owner_lnk",
            "pool_accesses",
            "pool_accesses_dependents_lnk",
            "pool_accesses_user_lnk",
            "pool_cleanings",
            "pool_sanctions",
            "pool_sanctions_user_lnk",
            "rentals",
            "rentals_rentals_user_lnk",
            "reservations",
            "reservations_owner_lnk",
            "sanctions",
            "sanctions_sanctions_user_lnk",
            "settings",
            "sse_services",
            "strapi_api_token_permissions",
            "strapi_api_token_permissions_token_lnk",
            "strapi_api_tokens",
            "strapi_core_store_settings",
            "strapi_history_versions",
            "strapi_migrations_internal",
            "strapi_release_actions",
            "strapi_release_actions_release_lnk",
            "strapi_releases",
            "strapi_transfer_token_permissions",
            "strapi_transfer_token_permissions_token_lnk",
            "strapi_transfer_tokens",
            "strapi_webhooks",
            "strapi_workflows",
            "strapi_workflows_stages",
            "strapi_workflows_stages_permissions_lnk",
            "strapi_workflows_stages_workflow_lnk",
            "survey_responses",
            "survey_responses_respondent_lnk",
            "survey_responses_survey_lnk",
            "surveys",
            "up_permissions",
            "up_roles",
            "up_users",
            "upload_folders",
            "upload_folders_parent_lnk",
            "vehicles",
            "vehicles_owner_lnk",
            "visits",
            "visits_cmps",
            "visits_user_lnk"
        ];
        // Limpiar tablas (DELETE FROM)
        let cleanedCount = 0;
        let cleanErrorCount = 0;
        for (const table of tablesToClean) {
            try {
                await connection.execute(`DELETE FROM ${table}`);
                cleanedCount++;
                console.log(`  ✅ Tabla ${table} limpiada`);
            }
            catch (error) {
                cleanErrorCount++;
                console.log(`  ❌ Error al limpiar tabla ${table}: ${error.message}`);
            }
        }
        console.log(`✨ ${cleanedCount} tablas limpiadas exitosamente`);
        if (cleanErrorCount > 0) {
            console.log(`⚠️ ${cleanErrorCount} tablas no pudieron ser limpiadas`);
        }
        console.log("\n👏 Base de datos limpiada exitosamente");
    }
    catch (error) {
        console.error("❌ Error:", error);
    }
    finally {
        if (connection) {
            await connection.end();
            console.log("📛 Conexión a la base de datos cerrada");
        }
    }
}
// Ejecutar el script
mostrarBanner();
cleanDatabase();
