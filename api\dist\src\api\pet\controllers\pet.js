"use strict";
/**
 * pet controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::pet.pet", ({ strapi }) => ({
    async create(ctx) {
        var _a, _b, _c, _d, _e, _f, _g;
        try {
            const data = JSON.parse(ctx.request.body.data || "{}");
            const imgUrl = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a.imgUrl;
            if (imgUrl) {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::pet.pet",
                        field: "imgUrl",
                    },
                    files: imgUrl,
                });
                if (uploadedFiles && uploadedFiles.length > 0) {
                    const petData = {
                        ...data,
                        owner: data.owner,
                        isVaccinated: (_b = data.isVaccinated) !== null && _b !== void 0 ? _b : false,
                        isSterilized: (_c = data.isSterilized) !== null && _c !== void 0 ? _c : false,
                        imgUrl: uploadedFiles[0].id,
                    };
                    // Subir archivo de vacunación si existe
                    if (data.isVaccinated && ((_d = ctx.request.files) === null || _d === void 0 ? void 0 : _d.vaccinationRecords)) {
                        const vaxFiles = await strapi.plugins.upload
                            .service("upload")
                            .upload({
                            data: {
                                ref: "api::pet.pet",
                                field: "vaccinationRecords",
                            },
                            files: ctx.request.files.vaccinationRecords,
                        });
                        if ((_e = vaxFiles === null || vaxFiles === void 0 ? void 0 : vaxFiles[0]) === null || _e === void 0 ? void 0 : _e.id) {
                            petData.vaccinationRecords = vaxFiles[0].id;
                        }
                    }
                    const createdPet = await strapi.entityService.create("api::pet.pet", {
                        data: petData,
                    });
                    return ctx.send(createdPet);
                }
                else {
                    throw new Error("No se pudo subir la imagen.");
                }
            }
            else {
                const petData = {
                    ...data,
                    owner: data.owner,
                    isVaccinated: (_f = data.isVaccinated) !== null && _f !== void 0 ? _f : false,
                    isSterilized: (_g = data.isSterilized) !== null && _g !== void 0 ? _g : false,
                };
                const createdPet = await strapi.entityService.create("api::pet.pet", {
                    data: petData,
                });
                return ctx.send(createdPet);
            }
        }
        catch (error) {
            console.error("Error en createWithImage para Pet:", error);
            return ctx.badRequest({
                error: error.message + " Error al crear mascota.",
            });
        }
    },
    async findByOwner(ctx) {
        const { ownerId } = ctx.params;
        try {
            const query = {
                where: {
                    owner: parseInt(ownerId),
                    publishedAt: { $notNull: true },
                },
                populate: {
                    imgUrl: true,
                    vaccinationRecords: true,
                    owner: true,
                },
            };
            const pets = await strapi.db
                .query("api::pet.pet")
                .findMany(query);
            const processedPets = pets.map((pet) => {
                var _a, _b, _c, _d, _e;
                return ({
                    id: pet.id,
                    name: pet.name,
                    color: pet.color,
                    breed: pet.breed,
                    type: pet.type,
                    gender: pet.gender,
                    isVaccinated: pet.isVaccinated || false,
                    isSterilized: pet.isSterilized || false,
                    vaccinationRecords: ((_a = pet.vaccinationRecords) === null || _a === void 0 ? void 0 : _a.url) || null,
                    imgUrl: ((_d = (_c = (_b = pet.imgUrl) === null || _b === void 0 ? void 0 : _b.formats) === null || _c === void 0 ? void 0 : _c.thumbnail) === null || _d === void 0 ? void 0 : _d.url) || ((_e = pet.imgUrl) === null || _e === void 0 ? void 0 : _e.url) || null,
                    owner: pet.owner
                        ? {
                            id: pet.owner.id,
                            name: `${pet.owner.firstName || ""} ${pet.owner.lastName || ""}`.trim(),
                        }
                        : null,
                });
            });
            return ctx.send(processedPets);
        }
        catch (error) {
            console.error("Error en findByOwner:", error);
            ctx.throw(500, error);
        }
    },
    async delete(ctx) {
        const { id } = ctx.params;
        try {
            const pet = await strapi.db.query("api::pet.pet").findOne({
                where: { id },
            });
            if (!pet) {
                return ctx.notFound("La mascota no existe.");
            }
            await strapi.db.query("api::pet.pet").delete({
                where: { id },
            });
            return { message: "Mascota eliminada exitosamente." };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async update(ctx) {
        var _a, _b, _c, _d, _e, _f, _g;
        try {
            const { id } = ctx.params;
            const data = ctx.request.body.data
                ? typeof ctx.request.body.data === "string"
                    ? JSON.parse(ctx.request.body.data)
                    : ctx.request.body.data
                : {};
            const currentPet = (await strapi.entityService.findOne("api::pet.pet", id, {
                populate: ["imgUrl", "vaccinationRecords", "owner"],
            }));
            // Start with basic data
            const updateData = {
                name: data.name,
                breed: data.breed,
                color: data.color,
                type: data.type,
                gender: data.gender,
                isVaccinated: data.isVaccinated,
                isSterilized: data.isSterilized,
                owner: ((_a = data.owner) === null || _a === void 0 ? void 0 : _a.id) || data.owner,
            };
            // Handle vaccination records update if needed
            if (data.isVaccinated && ((_b = ctx.request.files) === null || _b === void 0 ? void 0 : _b.vaccinationRecords)) {
                const vaxFiles = await strapi.plugins.upload.service("upload").upload({
                    data: {
                        ref: "api::pet.pet",
                        refId: id,
                        field: "vaccinationRecords",
                    },
                    files: ctx.request.files.vaccinationRecords,
                });
                if ((_c = vaxFiles === null || vaxFiles === void 0 ? void 0 : vaxFiles[0]) === null || _c === void 0 ? void 0 : _c.id) {
                    updateData.vaccinationRecords = vaxFiles[0].id;
                }
            }
            else if (data.isVaccinated === false) {
                // If vaccination is unchecked, remove the file
                updateData.vaccinationRecords = null;
            }
            else if ((_d = currentPet === null || currentPet === void 0 ? void 0 : currentPet.vaccinationRecords) === null || _d === void 0 ? void 0 : _d.id) {
                // Preserve existing vaccination record if not being updated
                updateData.vaccinationRecords = currentPet.vaccinationRecords.id;
            }
            // Handle image upload if needed
            if ((_e = ctx.request.files) === null || _e === void 0 ? void 0 : _e.imgUrl) {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::pet.pet",
                        refId: id,
                        field: "imgUrl",
                    },
                    files: ctx.request.files.imgUrl,
                });
                if ((_f = uploadedFiles === null || uploadedFiles === void 0 ? void 0 : uploadedFiles[0]) === null || _f === void 0 ? void 0 : _f.id) {
                    updateData.imgUrl = uploadedFiles[0].id;
                }
            }
            else if ((_g = currentPet === null || currentPet === void 0 ? void 0 : currentPet.imgUrl) === null || _g === void 0 ? void 0 : _g.id) {
                // Preserve existing image if not being updated
                updateData.imgUrl = currentPet.imgUrl.id;
            }
            // Update the pet with all the data
            const updatedPet = await strapi.entityService.update("api::pet.pet", id, {
                data: updateData,
            });
            return ctx.send(updatedPet);
        }
        catch (error) {
            console.error("Error al actualizar mascota:", error);
            return ctx.badRequest({
                error: error.message + " Error al actualizar mascota",
            });
        }
    },
    async findAll(ctx) {
        try {
            const pets = await strapi.db.query("api::pet.pet").findMany({
                populate: {
                    imgUrl: true,
                    vaccinationRecords: true,
                    owner: {
                        fields: ["address", "id", "firstName", "lastName"],
                    },
                },
            });
            // Transformar los datos al formato esperado por el frontend
            const processedPets = pets.map((pet) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j;
                return ({
                    id: pet.id,
                    name: pet.name,
                    color: pet.color,
                    breed: pet.breed,
                    type: pet.type,
                    gender: pet.gender,
                    imgUrl: ((_c = (_b = (_a = pet.imgUrl) === null || _a === void 0 ? void 0 : _a.formats) === null || _b === void 0 ? void 0 : _b.thumbnail) === null || _c === void 0 ? void 0 : _c.url) || ((_d = pet.imgUrl) === null || _d === void 0 ? void 0 : _d.url) || null,
                    isVaccinated: pet.isVaccinated || false,
                    isSterilized: pet.isSterilized || false,
                    vaccinationRecords: ((_e = pet.vaccinationRecords) === null || _e === void 0 ? void 0 : _e.url) || null,
                    address: ((_f = pet.owner) === null || _f === void 0 ? void 0 : _f.address) || null,
                    ownerName: `${((_g = pet.owner) === null || _g === void 0 ? void 0 : _g.firstName) || ""} ${((_h = pet.owner) === null || _h === void 0 ? void 0 : _h.lastName) || ""}`.trim(),
                    ownerId: (_j = pet.owner) === null || _j === void 0 ? void 0 : _j.id,
                });
            });
            return ctx.send(processedPets);
        }
        catch (error) {
            console.error("Error en findAll:", error);
            ctx.throw(500, error);
        }
    },
}));
