import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";

export interface ConsentData {
  user: number;
  fecha_aceptacion: string;
  ip_address?: string;
  user_agent?: string;
  version_politica: string;
  tipo_consentimiento: "registro" | "actualizacion_politica" | "marketing" | "cookies";
  activo: boolean;
}

export interface Consent {
  id: number;
  attributes: ConsentData & {
    createdAt: string;
    updatedAt: string;
  };
}

interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Función auxiliar para obtener la IP del cliente
const getClientIP = async (): Promise<string> => {
  try {
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    return data.ip || "";
  } catch (error) {
    console.warn("No se pudo obtener la IP del cliente:", error);
    return "";
  }
};

// Hook para crear consentimiento
export const useCreateConsent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (consentData: Omit<ConsentData, "ip_address" | "user_agent">) => {
      const clientIP = await getClientIP();
      
      const fullConsentData: ConsentData = {
        ...consentData,
        ip_address: clientIP,
        user_agent: navigator.userAgent,
      };

      const { data } = await Http.post("/api/consents", {
        data: fullConsentData,
      });
      return data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas con consentimientos
      queryClient.invalidateQueries({ queryKey: ["consents"] });
    },
  });
};

// Hook para obtener consentimientos de un usuario
export const useGetUserConsents = (userId?: number) => {
  return useQuery<StrapiResponse<Consent[]>>({
    queryKey: ["consents", "user", userId],
    queryFn: async () => {
      if (!userId) throw new Error("User ID is required");
      
      const { data } = await Http.get(
        `/api/consents?filters[user][id][$eq]=${userId}&sort=createdAt:desc`
      );
      return data;
    },
    enabled: !!userId,
  });
};

// Hook para obtener el último consentimiento activo de un usuario por tipo
export const useGetLatestConsentByType = (
  userId?: number,
  tipo?: ConsentData["tipo_consentimiento"]
) => {
  return useQuery<Consent | null>({
    queryKey: ["consents", "latest", userId, tipo],
    queryFn: async () => {
      if (!userId || !tipo) return null;
      
      const { data } = await Http.get(
        `/api/consents?filters[user][id][$eq]=${userId}&filters[tipo_consentimiento][$eq]=${tipo}&filters[activo][$eq]=true&sort=createdAt:desc&pagination[limit]=1`
      );
      
      if (data.data && data.data.length > 0) {
        return data.data[0];
      }
      
      return null;
    },
    enabled: !!userId && !!tipo,
  });
};

// Hook para verificar si un usuario tiene consentimiento válido
export const useHasValidConsent = (
  userId?: number,
  tipo?: ConsentData["tipo_consentimiento"]
) => {
  const { data: latestConsent, ...rest } = useGetLatestConsentByType(userId, tipo);
  
  return {
    hasValidConsent: latestConsent !== null && latestConsent.attributes.activo,
    latestConsent,
    ...rest,
  };
};
