// Script para sembrar reglas de advertencia directamente en la base de datos
const mysql = require("mysql2/promise");
const warningRules = require("./warning-rules");
require("dotenv").config();
// Banner de inicio
function mostrarBanner() {
    console.log('\n===========================================================');
    console.log('🌟 INICIALIZADOR DE REGLAS DE CONVIVENCIA - PALMAS PRODUCCIÓN 🌟');
    console.log('===========================================================\n');
    console.log(`Este script creará ${warningRules.length} reglas de convivencia en la base de datos.`);
    // Agrupar reglas por capítulo para un resumen más compacto
    const reglasPorCapitulo = {};
    warningRules.forEach(rule => {
        if (!reglasPorCapitulo[rule.chapter]) {
            reglasPorCapitulo[rule.chapter] = [];
        }
        reglasPorCapitulo[rule.chapter].push(rule);
    });
    console.log('\nResumen de reglas por capítulo:');
    Object.keys(reglasPorCapitulo).sort().forEach(capitulo => {
        console.log(`  • Capítulo ${capitulo}: ${reglasPorCapitulo[capitulo].length} reglas`);
    });
    console.log('\n===========================================================\n');
}
// Configuración de la base de datos desde variables de entorno
const dbConfig = {
    host: process.env.DATABASE_HOST || "localhost",
    port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
};
async function seedWarningRules() {
    let connection;
    try {
        console.log("🔌 Conectando a la base de datos...");
        connection = await mysql.createConnection(dbConfig);
        console.log("✅ Conexión exitosa a la base de datos");
        // Eliminar reglas existentes
        console.log("🗑️ Eliminando reglas existentes...");
        await connection.execute("DELETE FROM warning_rules");
        console.log("✅ Reglas existentes eliminadas");
        // Insertar nuevas reglas
        console.log("📜 Insertando nuevas reglas de convivencia...");
        let successCount = 0;
        let errorCount = 0;
        // Agrupar por capítulo para mejor visualización
        const reglasPorCapitulo = {};
        warningRules.forEach(rule => {
            if (!reglasPorCapitulo[rule.chapter]) {
                reglasPorCapitulo[rule.chapter] = [];
            }
            reglasPorCapitulo[rule.chapter].push(rule);
        });
        // Insertar por capítulo
        for (const capitulo of Object.keys(reglasPorCapitulo).sort()) {
            console.log(`\n  📓 Capítulo ${capitulo} (${reglasPorCapitulo[capitulo].length} reglas):`);
            for (const rule of reglasPorCapitulo[capitulo]) {
                try {
                    const [result] = await connection.execute("INSERT INTO warning_rules (article, description, chapter, published_at, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW(), NOW())", [rule.article, rule.description, rule.chapter]);
                    successCount++;
                    console.log(`    ✅ Artículo ${rule.article}: ${rule.description.substring(0, 50)}${rule.description.length > 50 ? '...' : ''}`);
                }
                catch (error) {
                    errorCount++;
                    console.error(`    ❌ Error al insertar regla "${rule.article}":`, error.message);
                }
            }
        }
        console.log(`\n✨ Proceso completado. ${successCount} reglas insertadas exitosamente.`);
        if (errorCount > 0) {
            console.log(`⚠️ Atención: ${errorCount} reglas no pudieron ser insertadas.`);
        }
    }
    catch (error) {
        console.error("❌ Error durante el proceso:", error.message);
    }
    finally {
        if (connection) {
            await connection.end();
            console.log("📛 Conexión a la base de datos cerrada");
        }
    }
}
// Ejecutar el script
mostrarBanner();
seedWarningRules().catch((error) => {
    console.error("❌ Error fatal:", error);
    process.exit(1);
});
