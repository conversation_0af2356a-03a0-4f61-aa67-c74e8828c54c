{"kind": "collectionType", "collectionName": "surveys", "info": {"singularName": "survey", "pluralName": "surveys", "displayName": "Survey", "description": "Manage surveys created with Survey.js"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "json", "required": true}, "description": {"type": "json"}, "content": {"type": "json", "required": true}, "isActive": {"type": "boolean", "default": true}, "responses": {"type": "relation", "relation": "oneToMany", "target": "api::survey-response.survey-response", "mappedBy": "survey"}}}