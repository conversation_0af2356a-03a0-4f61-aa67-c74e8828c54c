"use strict";
/**
 * Servicio de gestión de notificaciones avanzado
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationManager = void 0;
const websocket_1 = require("./websocket");
const notifications_1 = require("../types/notifications");
class NotificationManager {
    /**
     * Enviar notificación de confirmación de huésped
     */
    async sendGuestConfirmationNotification(guestData, confirmedBy) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.GUEST_CONFIRMATION, 'Huésped Confirmado', `${guestData.name} ha sido confirmado por ${confirmedBy.username}`, {
            data: {
                guestId: guestData.id,
                guestName: guestData.name,
                confirmedBy: confirmedBy.username,
                confirmedAt: new Date().toISOString(),
                reservationId: guestData.reservationId,
            },
            actionUrl: `/guests/${guestData.id}`,
            actionLabel: '<PERSON><PERSON>',
        });
        // Enviar a usuarios con rol de recepción y administradores
        await this.sendToRoles(['reception', 'admin'], notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar notificación de llegada de huésped
     */
    async sendGuestArrivalNotification(guestData) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.GUEST_ARRIVAL, 'Nuevo Huésped Llegó', `${guestData.name} ha llegado al hotel`, {
            data: {
                guestId: guestData.id,
                guestName: guestData.name,
                arrivalTime: new Date().toISOString(),
                roomNumber: guestData.roomNumber,
                reservationId: guestData.reservationId,
            },
            actionUrl: `/guests/${guestData.id}/confirm`,
            actionLabel: 'Confirmar Llegada',
        });
        // Enviar a usuarios con rol de recepción
        await this.sendToRoles(['reception'], notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar notificación de nueva reserva
     */
    async sendNewReservationNotification(reservationData) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.NEW_RESERVATION, 'Nueva Reserva', `Nueva reserva de ${reservationData.guestName} para ${reservationData.checkInDate}`, {
            data: {
                reservationId: reservationData.id,
                guestName: reservationData.guestName,
                checkInDate: reservationData.checkInDate,
                checkOutDate: reservationData.checkOutDate,
                roomType: reservationData.roomType,
                totalAmount: reservationData.totalAmount,
            },
            actionUrl: `/reservations/${reservationData.id}`,
            actionLabel: 'Ver Reserva',
        });
        // Enviar a usuarios con rol de recepción y administradores
        await this.sendToRoles(['reception', 'admin'], notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar notificación de pago recibido
     */
    async sendPaymentReceivedNotification(paymentData) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.PAYMENT_RECEIVED, 'Pago Recibido', `Pago de $${paymentData.amount} recibido de ${paymentData.guestName}`, {
            data: {
                paymentId: paymentData.id,
                amount: paymentData.amount,
                guestName: paymentData.guestName,
                paymentMethod: paymentData.method,
                reservationId: paymentData.reservationId,
            },
            actionUrl: `/payments/${paymentData.id}`,
            actionLabel: 'Ver Pago',
        });
        // Enviar a usuarios con rol de contabilidad y administradores
        await this.sendToRoles(['accounting', 'admin'], notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar alerta de seguridad
     */
    async sendSecurityAlert(alertData, reportedBy) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.SECURITY_ALERT, 'Alerta de Seguridad', alertData.description, {
            priority: notifications_1.NotificationPriority.CRITICAL,
            data: {
                alertId: alertData.id,
                location: alertData.location,
                severity: alertData.severity,
                reportedBy: reportedBy.username,
                reportedAt: new Date().toISOString(),
            },
            actionUrl: `/security/alerts/${alertData.id}`,
            actionLabel: 'Ver Alerta',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 horas
        });
        // Enviar a todos los usuarios (alerta crítica)
        await this.broadcast(notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar mensaje de staff
     */
    async sendStaffMessage(fromUser, toUserIds, message) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.STAFF_MESSAGE, `Mensaje de ${fromUser.username}`, message, {
            data: {
                fromUserId: fromUser.id,
                fromUsername: fromUser.username,
                messageId: Date.now(),
            },
            userIds: toUserIds,
        });
        // Enviar a usuarios específicos
        await this.sendToUsers(toUserIds, notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar notificación de mantenimiento del sistema
     */
    async sendSystemMaintenanceNotification(maintenanceData) {
        const notification = (0, notifications_1.createNotification)(notifications_1.NotificationType.SYSTEM_MAINTENANCE, 'Mantenimiento del Sistema', `Mantenimiento programado: ${maintenanceData.description}`, {
            priority: notifications_1.NotificationPriority.HIGH,
            data: {
                startTime: maintenanceData.startTime,
                endTime: maintenanceData.endTime,
                affectedServices: maintenanceData.affectedServices,
            },
            expiresAt: maintenanceData.endTime,
        });
        // Enviar a todos los usuarios
        await this.broadcast(notification);
        // Guardar en base de datos
        await this.saveNotification(notification);
        return notification;
    }
    /**
     * Enviar a usuarios específicos
     */
    async sendToUsers(userIds, notification) {
        const sent = websocket_1.websocketService.sendNotificationToUsers(userIds, notification);
        console.log(`📤 Notificación enviada a ${sent} usuario(s) específico(s)`);
        return sent;
    }
    /**
     * Enviar a usuarios por rol
     */
    async sendToRoles(roles, notification) {
        let totalSent = 0;
        for (const role of roles) {
            const sent = websocket_1.websocketService.sendNotificationToRole(role, notification);
            totalSent += sent;
        }
        console.log(`📤 Notificación enviada a ${totalSent} usuario(s) con roles: ${roles.join(', ')}`);
        return totalSent;
    }
    /**
     * Broadcast a todos los usuarios
     */
    async broadcast(notification) {
        const sent = websocket_1.websocketService.broadcast({
            type: 'notification',
            data: notification,
            timestamp: new Date().toISOString(),
        });
        console.log(`📡 Notificación broadcast a ${sent} usuario(s)`);
        return sent;
    }
    /**
     * Guardar notificación en base de datos
     */
    async saveNotification(notification) {
        try {
            const saved = await strapi.entityService.create('api::notification.notification', {
                data: {
                    type: notification.type,
                    priority: notification.priority,
                    title: notification.title,
                    message: notification.message,
                    data: notification.data,
                    userId: notification.userId,
                    userIds: notification.userIds,
                    role: notification.role,
                    read: false,
                    actionUrl: notification.actionUrl,
                    actionLabel: notification.actionLabel,
                    expiresAt: notification.expiresAt,
                },
            });
            console.log(`💾 Notificación guardada en BD: ${saved.id}`);
            return saved;
        }
        catch (error) {
            console.error('❌ Error guardando notificación:', error);
            return null;
        }
    }
    /**
     * Obtener estadísticas de notificaciones
     */
    getStats() {
        return {
            websocket: websocket_1.websocketService.getStats(),
            connectedUsers: websocket_1.websocketService.getConnectedUsers(),
        };
    }
}
// Exportar instancia singleton
exports.notificationManager = new NotificationManager();
exports.default = exports.notificationManager;
