"use strict";
/**
 * payment controller
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const path = __importStar(require("path"));
const cloudinary = require("cloudinary").v2;
const strapi_1 = require("@strapi/strapi");
/**
 * Formatea una fecha a un string legible
 * @param dateString - Fecha en formato string o Date
 * @returns String con la fecha formateada en formato dd/mm/aaaa, hh:mm
 */
const formatDate = (dateString) => {
    if (!dateString)
        return "";
    const date = new Date(dateString);
    // Validar que la fecha sea válida
    if (isNaN(date.getTime()))
        return "";
    return date.toLocaleDateString("es-ES", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
    });
};
exports.default = strapi_1.factories.createCoreController("api::payment.payment", ({ strapi }) => ({
    async find(ctx) {
        try {
            const payments = (await strapi.entityService.findMany("api::payment.payment", {
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                    imgUrl: true,
                },
                sort: { dateOfPayment: "desc" },
            }));
            const simplifiedPayments = payments.map((payment) => {
                var _a;
                return ({
                    id: payment.id,
                    amount: payment.amount,
                    dateOfPayment: payment.dateOfPayment,
                    monthOfPayment: payment.monthOfPayment || [],
                    beneficiary: payment.beneficiary || "",
                    bank: payment.bank,
                    description: payment.description,
                    transactionId: payment.transactionId || "",
                    status: payment.status || "pending",
                    rejectionReason: payment.rejectionReason,
                    owner: payment.owner
                        ? {
                            id: payment.owner.id,
                            username: payment.owner.username,
                            firstName: payment.owner.firstName,
                            lastName: payment.owner.lastName,
                            imgUrl: (_a = payment.owner.imgUrl) === null || _a === void 0 ? void 0 : _a.url,
                            address: payment.owner.address,
                        }
                        : null,
                    imgUrl: payment.imgUrl
                        ? {
                            url: payment.imgUrl.url,
                            ext: payment.imgUrl.ext,
                            name: payment.imgUrl.name,
                        }
                        : null,
                });
            });
            return { data: simplifiedPayments };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async create(ctx) {
        var _a, _b, _c, _d, _e, _f;
        try {
            const data = JSON.parse(ctx.request.body.data || "{}");
            const imgUrl = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a.imgUrl;
            if (imgUrl) {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::payment.payment",
                        refId: data.id,
                        field: "imgUrl",
                    },
                    files: imgUrl,
                });
                data.imgUrl = uploadedFiles[0].id;
            }
            const payment = (await strapi.entityService.create("api::payment.payment", {
                data,
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                    imgUrl: true,
                },
            }));
            // Enviar correo al administrador sobre el nuevo pago
            try {
                // Buscar usuarios administradores
                const adminUsers = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findMany({
                    where: {
                        role: {
                            type: "admin",
                        },
                    },
                });
                if (adminUsers && adminUsers.length > 0) {
                    // Preparar datos para la plantilla
                    const emailData = {
                        payment: {
                            reference: payment.transactionId || `#${payment.id}`,
                            amount: `$${payment.amount.toLocaleString("es-CO")}`,
                            date: formatDate(payment.dateOfPayment),
                            concept: payment.description ||
                                ((_b = payment.monthOfPayment) === null || _b === void 0 ? void 0 : _b.join(", ")) ||
                                "Pago de administración",
                            bank: payment.bank || "No especificado",
                            ownerName: payment.owner
                                ? `${payment.owner.firstName || ""} ${payment.owner.lastName || ""}`.trim() || payment.owner.username
                                : "No especificado",
                            ownerEmail: ((_c = payment.owner) === null || _c === void 0 ? void 0 : _c.email) || "No especificado",
                            ownerAddress: ((_d = payment.owner) === null || _d === void 0 ? void 0 : _d.address) || "No especificado",
                            imgUrl: ((_e = payment.imgUrl) === null || _e === void 0 ? void 0 : _e.url) || null,
                        },
                        URL: process.env.FRONTEND_URL || "https://ccpalcan.com",
                    };
                    // Leer la plantilla HTML
                    const templatePath = path.join(process.cwd(), "config", "email-templates", "payment-registered.html");
                    let htmlContent = "";
                    try {
                        const fs = require("fs");
                        htmlContent = fs.readFileSync(templatePath, "utf8");
                        // Reemplazar variables en la plantilla
                        htmlContent = htmlContent
                            .replace(/<%= payment\.reference %>/g, emailData.payment.reference)
                            .replace(/<%= payment\.amount %>/g, emailData.payment.amount)
                            .replace(/<%= payment\.date %>/g, emailData.payment.date)
                            .replace(/<%= payment\.concept %>/g, emailData.payment.concept)
                            .replace(/<%= payment\.bank %>/g, emailData.payment.bank)
                            .replace(/<%= payment\.ownerName %>/g, emailData.payment.ownerName)
                            .replace(/<%= payment\.ownerEmail %>/g, emailData.payment.ownerEmail)
                            .replace(/<%= payment\.ownerAddress %>/g, emailData.payment.ownerAddress)
                            .replace(/<%= URL %>/g, emailData.URL);
                        // Manejar la imagen del comprobante
                        if (emailData.payment.imgUrl) {
                            htmlContent = htmlContent.replace(/<% if \(payment\.imgUrl\) { %>([\s\S]*?)<% } else { %>([\s\S]*?)<% } %>/g, `<img src="${emailData.payment.imgUrl}" alt="Comprobante de pago" class="payment-image">`);
                        }
                        else {
                            htmlContent = htmlContent.replace(/<% if \(payment\.imgUrl\) { %>([\s\S]*?)<% } else { %>([\s\S]*?)<% } %>/g, `<p>No se adjuntó comprobante de pago.</p>`);
                        }
                    }
                    catch (readError) {
                        console.error(`Error al leer la plantilla HTML:`, readError);
                        // Si no podemos leer la plantilla, usamos un HTML básico
                        htmlContent = `<h1>Nuevo Pago Registrado</h1>
              <p>Se ha registrado un nuevo pago en el sistema que requiere verificación.</p>
              <p><strong>Propietario:</strong> ${emailData.payment.ownerName}</p>
              <p><strong>Email:</strong> ${emailData.payment.ownerEmail}</p>
              <p><strong>Referencia:</strong> ${emailData.payment.reference}</p>
              <p><strong>Monto:</strong> ${emailData.payment.amount}</p>
              <p><strong>Fecha:</strong> ${emailData.payment.date}</p>
              <p><strong>Concepto:</strong> ${emailData.payment.concept}</p>
              <p><strong>Banco:</strong> ${emailData.payment.bank}</p>
              <p><a href="${emailData.URL}/admin/payments">Verificar pago</a></p>`;
                    }
                    // Enviar correo a cada administrador
                    for (const admin of adminUsers) {
                        if (admin.email) {
                            // Configuración avanzada para evitar spam
                            const to = admin.email;
                            const from = process.env.SENDGRID_EMAIL || "<EMAIL>";
                            const subject = `Nuevo pago registrado - CCPALCAN`;
                            const text = `Se ha registrado un nuevo pago de ${emailData.payment.ownerName} por un monto de ${emailData.payment.amount}. Por favor, verifica este pago.`;
                            // Generar un ID de mensaje único para rastreo
                            const messageId = `payment-${payment.id}-${Date.now()}@ccpalcan.com`;
                            // Crear una fecha para los encabezados RFC822
                            const date = new Date().toUTCString();
                            await strapi.plugins.email.services.email.send({
                                to,
                                from: {
                                    email: from,
                                    name: "Administración CCPALCAN",
                                },
                                replyTo: from,
                                subject,
                                text,
                                html: htmlContent,
                                // Cabeceras adicionales avanzadas para mejorar la entregabilidad
                                headers: {
                                    "List-Unsubscribe": `<mailto:<EMAIL>?subject=unsubscribe>, <${process.env.FRONTEND_URL || "https://ccpalcan.com"}/unsubscribe>`,
                                    "List-Unsubscribe-Post": "List-Unsubscribe=One-Click",
                                    "Feedback-ID": `payment:ccpalcan:${payment.id}:${Date.now()}`,
                                    "X-Entity-Ref-ID": messageId,
                                    "Message-ID": `<${messageId}>`,
                                    Date: date,
                                    Precedence: "bulk",
                                    "X-Mailer": "CCPALCAN-Mailer",
                                    "X-Auto-Response-Suppress": "OOF, AutoReply",
                                    "Auto-Submitted": "auto-generated",
                                },
                            });
                        }
                    }
                }
                else {
                }
            }
            catch (emailError) {
                console.error("Error al enviar correo de notificación de nuevo pago:", emailError);
                // No interrumpimos el flujo si falla el envío de correo
            }
            const simplifiedPayment = {
                id: payment.id,
                amount: payment.amount,
                dateOfPayment: payment.dateOfPayment,
                monthOfPayment: payment.monthOfPayment || [],
                beneficiary: payment.beneficiary || "",
                bank: payment.bank,
                description: payment.description,
                transactionId: payment.transactionId || "",
                status: payment.status || "pending",
                rejectionReason: payment.rejectionReason,
                owner: payment.owner
                    ? {
                        id: payment.owner.id,
                        username: payment.owner.username,
                        firstName: payment.owner.firstName,
                        lastName: payment.owner.lastName,
                        imgUrl: (_f = payment.owner.imgUrl) === null || _f === void 0 ? void 0 : _f.url,
                        address: payment.owner.address,
                    }
                    : null,
                imgUrl: payment.imgUrl
                    ? {
                        url: payment.imgUrl.url,
                        ext: payment.imgUrl.ext,
                        name: payment.imgUrl.name,
                    }
                    : null,
            };
            // Obtener el admin y enviar notificación
            const adminUser = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { role: { type: "admin" } },
                populate: ["role"],
            });
            if (adminUser) {
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: "Nuevo pago registrado",
                        type: "new_payment",
                        message: `El residente ${payment.owner.firstName} ${payment.owner.lastName} ha registrado un nuevo pago por $${payment.amount} al banco ${payment.bank} el ${formatDate(payment.dateOfPayment)} con el ID de transacción ${payment.transactionId}`,
                        user: adminUser.id,
                        data: { paymentId: payment.id },
                    },
                });
            }
            return { data: simplifiedPayment };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async getDownloadUrl(ctx) {
        try {
            const { publicId } = ctx.params;
            if (!publicId) {
                return ctx.badRequest("Public ID is required");
            }
            // Configurar Cloudinary
            cloudinary.config({
                cloud_name: process.env.CLOUDINARY_NAME,
                api_key: process.env.CLOUDINARY_KEY,
                api_secret: process.env.CLOUDINARY_SECRET,
                secure: true,
            });
            const url = cloudinary.url(publicId, {
                flags: "attachment",
                resource_type: "image",
            });
            return { data: { url } };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async delete(ctx) {
        try {
            const { id } = ctx.params;
            const payment = (await strapi.entityService.delete("api::payment.payment", id, {
                populate: ["owner"],
            }));
            const simplifiedPayment = {
                id: payment.id,
                amount: payment.amount,
                dateOfPayment: formatDate(payment.dateOfPayment),
                status: payment.status,
                rejectionReason: payment.rejectionReason,
            };
            // Notificar al propietario
            if (payment === null || payment === void 0 ? void 0 : payment.owner) {
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: "Pago eliminado",
                        type: "payment_deleted",
                        message: `Tu pago del ${formatDate(payment.dateOfPayment)} ha sido eliminado`,
                        user: payment.owner,
                        data: { paymentId: payment.id },
                    },
                });
            }
            return { data: simplifiedPayment };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async update(ctx) {
        var _a, _b, _c;
        try {
            const { id } = ctx.params;
            const data = JSON.parse(ctx.request.body.data || "{}");
            const imgUrl = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a.imgUrl;
            const user = ctx.state.user;
            // Si no se proporciona un owner, usar el usuario autenticado
            if (!data.owner) {
                data.owner = user === null || user === void 0 ? void 0 : user.id;
            }
            // Si se proporciona un owner, asegurarse de que sea un ID válido
            if (data.owner && typeof data.owner !== "number") {
                console.error("Invalid owner ID:", data.owner);
                throw ctx.badRequest("El propietario debe ser un ID válido");
            }
            // Manejar la imagen de manera más efectiva
            if (imgUrl) {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::payment.payment",
                        refId: id,
                        field: "imgUrl",
                    },
                    files: imgUrl,
                });
                if (uploadedFiles && uploadedFiles.length > 0) {
                    data.imgUrl = uploadedFiles[0].id;
                }
            }
            else if (data.imgUrl &&
                Array.isArray(data.imgUrl) &&
                data.imgUrl.length > 0) {
                // Si hay una URL existente, buscar el archivo en Strapi
                try {
                    const existingFile = await strapi.entityService.findMany("plugin::upload.file", {
                        filters: {
                            url: {
                                $eq: data.imgUrl[0].url,
                            },
                        },
                    });
                    if (existingFile && existingFile.length > 0) {
                        data.imgUrl = existingFile[0].id;
                    }
                    else {
                        console.warn("No se encontró el archivo existente");
                        // Si no se encuentra, mantener la URL
                        data.imgUrl = data.imgUrl[0].url;
                    }
                }
                catch (error) {
                    console.error("Error buscando archivo:", error);
                    // Si hay error, mantener la URL
                    data.imgUrl = data.imgUrl[0].url;
                }
            }
            else {
                // Si no hay imagen, no hacer nada
            }
            // Simplificar los datos antes de actualizar
            const simplifiedData = {
                ...data,
                owner: data.owner || ((_b = ctx.state.user) === null || _b === void 0 ? void 0 : _b.id),
                // Convertir todos los meses a minúsculas y mantenerlos como array
                monthOfPayment: Array.isArray(data.monthOfPayment)
                    ? data.monthOfPayment.map((month) => month.toLowerCase())
                    : data.monthOfPayment
                        ? [data.monthOfPayment.toLowerCase()]
                        : null,
                // Mantener solo las propiedades que necesitamos
                beneficiary: data.beneficiary,
                bank: data.bank,
                dateOfPayment: data.dateOfPayment,
                transactionId: data.transactionId,
                amount: data.amount,
                description: data.description,
                status: data.status,
                rejectionReason: data.rejectionReason,
            };
            const payment = (await strapi.entityService.update("api::payment.payment", id, {
                data: simplifiedData,
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                    imgUrl: true,
                },
            }));
            const simplifiedPayment = {
                id: payment.id,
                amount: payment.amount,
                dateOfPayment: payment.dateOfPayment,
                monthOfPayment: payment.monthOfPayment || [],
                beneficiary: payment.beneficiary || "",
                bank: payment.bank,
                description: payment.description,
                transactionId: payment.transactionId || "",
                status: payment.status || "pending",
                rejectionReason: payment.rejectionReason,
                owner: payment.owner
                    ? {
                        id: payment.owner.id,
                        username: payment.owner.username,
                        firstName: payment.owner.firstName,
                        lastName: payment.owner.lastName,
                        imgUrl: (_c = payment.owner.imgUrl) === null || _c === void 0 ? void 0 : _c.url,
                        address: payment.owner.address,
                    }
                    : null,
                imgUrl: payment.imgUrl
                    ? {
                        url: payment.imgUrl.url,
                        ext: payment.imgUrl.ext,
                        name: payment.imgUrl.name,
                    }
                    : null,
            };
            // Notificar al propietario
            if (payment === null || payment === void 0 ? void 0 : payment.owner) {
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: "Pago actualizado",
                        type: "payment_updated",
                        message: `Tu pago del ${formatDate(payment.dateOfPayment)} ha sido actualizado`,
                        user: payment.owner,
                        data: { paymentId: payment.id },
                    },
                });
            }
            return { data: simplifiedPayment };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async verify(ctx) {
        const { id } = ctx.params;
        ctx.request.body = {
            ...ctx.request.body,
            status: "verified",
        };
        return await this.updateStatus.bind(this)(ctx);
    },
    async reject(ctx) {
        const { id } = ctx.params;
        const { reason } = ctx.request.body;
        if (!reason) {
            return ctx.badRequest("Rejection reason is required");
        }
        ctx.request.body = {
            ...ctx.request.body,
            status: "rejected",
        };
        return await this.updateStatus.bind(this)(ctx);
    },
    async updateStatus(ctx) {
        var _a;
        try {
            const { id } = ctx.params;
            const { status, reason } = ctx.request.body;
            if (!status || !["pending", "verified", "rejected"].includes(status)) {
                return ctx.badRequest("Invalid status");
            }
            if (status === "rejected" && !reason) {
                return ctx.badRequest("Rejection reason is required");
            }
            const payment = (await strapi.entityService.update("api::payment.payment", id, {
                data: {
                    status,
                    ...(status === "rejected" ? { rejectionReason: reason } : {}),
                },
                populate: ["owner"],
            }));
            if (!payment) {
                return ctx.notFound("Payment not found");
            }
            const simplifiedPayment = {
                id: payment.id,
                status: payment.status,
                rejectionReason: payment.rejectionReason,
            };
            // Enviar notificación al propietario
            if (payment.owner) {
                const statusText = status === "verified" ? "verificado" : "rechazado";
                const message = status === "verified"
                    ? `Tu pago de monto ${payment.amount} ha sido ${statusText} por el administrador`
                    : `Tu pago de monto ${payment.amount} ha sido ${statusText} por el administrador${reason ? `. Razón: ${reason}` : ""}`;
                // Crear notificación interna
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: `Pago ${statusText}`,
                        type: `payment_${status}`,
                        message,
                        user: payment.owner.id,
                        data: {
                            paymentId: payment.id,
                            status,
                            reason: reason || undefined,
                        },
                    },
                });
                // Enviar correo electrónico al propietario
                try {
                    // Preparar datos para la plantilla
                    const emailData = {
                        user: {
                            username: payment.owner.username ||
                                payment.owner.firstName ||
                                "Usuario",
                            email: payment.owner.email,
                        },
                        payment: {
                            reference: payment.transactionId || `#${payment.id}`,
                            amount: `$${payment.amount.toLocaleString("es-CO")}`,
                            date: formatDate(payment.dateOfPayment),
                            concept: payment.description ||
                                ((_a = payment.monthOfPayment) === null || _a === void 0 ? void 0 : _a.join(", ")) ||
                                "Pago de administración",
                        },
                        URL: process.env.FRONTEND_URL || "https://ccpalcan.com",
                    };
                    // Agregar razón de rechazo si es necesario
                    if (status === "rejected" && reason) {
                        emailData.payment.rejectionReason = reason;
                    }
                    // Obtener el contenido HTML de la plantilla directamente
                    const templateName = status === "verified" ? "payment-verified" : "payment-rejected";
                    const templatePath = path.join(process.cwd(), "config", "email-templates", `${templateName}.html`);
                    // Leer la plantilla HTML
                    let htmlContent = "";
                    try {
                        const fs = require("fs");
                        htmlContent = fs.readFileSync(templatePath, "utf8");
                        // Reemplazar variables en la plantilla
                        // Reemplazos básicos para las variables más comunes
                        htmlContent = htmlContent
                            .replace(/<%= user\.username %>/g, emailData.user.username)
                            .replace(/<%= payment\.reference %>/g, emailData.payment.reference)
                            .replace(/<%= payment\.amount %>/g, emailData.payment.amount)
                            .replace(/<%= payment\.date %>/g, emailData.payment.date)
                            .replace(/<%= payment\.concept %>/g, emailData.payment.concept)
                            .replace(/<%= URL %>/g, emailData.URL);
                        // Si es un rechazo, reemplazar la razón
                        if (status === "rejected" && emailData.payment.rejectionReason) {
                            htmlContent = htmlContent.replace(/<%= payment\.rejectionReason %>/g, emailData.payment.rejectionReason);
                        }
                    }
                    catch (readError) {
                        console.error(`Error al leer la plantilla HTML:`, readError);
                        // Si no podemos leer la plantilla, usamos un HTML básico
                        htmlContent = `<h1>Pago ${statusText}</h1>
              <p>Hola ${emailData.user.username},</p>
              <p>Tu pago con referencia ${emailData.payment.reference} por un monto de ${emailData.payment.amount} ha sido ${statusText}.</p>
              ${status === "rejected" && emailData.payment.rejectionReason
                            ? `<p>Razón: ${emailData.payment.rejectionReason}</p>`
                            : ""}
              <p>Fecha del pago: ${emailData.payment.date}</p>
              <p>Concepto: ${emailData.payment.concept}</p>
              <p>Gracias por usar nuestro sistema.</p>`;
                    }
                    // Enviar el correo usando sendEmail con configuración avanzada para evitar spam
                    // Generar un ID de mensaje único para rastreo
                    const messageId = `payment-${payment.id}-${status}-${Date.now()}@ccpalcan.com`;
                    // Crear una fecha para los encabezados RFC822
                    const date = new Date().toUTCString();
                    // Preparar el remitente
                    const from = process.env.SENDGRID_EMAIL || "<EMAIL>";
                    await strapi.plugins.email.services.email.send({
                        to: payment.owner.email,
                        from: {
                            email: from,
                            name: "Administración CCPALCAN",
                        },
                        replyTo: from,
                        subject: `Pago ${statusText} - CCPALCAN`,
                        text: `Hola ${emailData.user.username}, tu pago con referencia ${emailData.payment.reference} por un monto de ${emailData.payment.amount} ha sido ${statusText}.${status === "rejected" && emailData.payment.rejectionReason
                            ? ` Razón: ${emailData.payment.rejectionReason}`
                            : ""}`,
                        html: htmlContent,
                        // Cabeceras adicionales avanzadas para mejorar la entregabilidad
                        headers: {
                            "List-Unsubscribe": `<mailto:<EMAIL>?subject=unsubscribe>, <${process.env.FRONTEND_URL || "https://ccpalcan.com"}/unsubscribe>`,
                            "List-Unsubscribe-Post": "List-Unsubscribe=One-Click",
                            "Feedback-ID": `payment:ccpalcan:${payment.id}:${status}:${Date.now()}`,
                            "X-Entity-Ref-ID": messageId,
                            "Message-ID": `<${messageId}>`,
                            Date: date,
                            Precedence: "bulk",
                            "X-Mailer": "CCPALCAN-Mailer",
                            "X-Auto-Response-Suppress": "OOF, AutoReply",
                            "Auto-Submitted": "auto-generated",
                        },
                    });
                }
                catch (emailError) {
                    console.error(`Error al enviar correo de pago ${statusText}:`, emailError);
                    // No interrumpimos el flujo si falla el envío de correo
                }
            }
            return { data: simplifiedPayment };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async addComment(ctx) {
        var _a;
        try {
            const { id } = ctx.params;
            const { comment } = ctx.request.body;
            if (!comment) {
                return ctx.badRequest("Comment is required");
            }
            // Obtener el pago actual para verificar sus comentarios
            const currentPayment = (await strapi.entityService.findOne("api::payment.payment", id, {
                populate: ["owner"],
            }));
            if (!currentPayment) {
                return ctx.notFound("Payment not found");
            }
            // Preparar el nuevo comentario y los comentarios existentes
            let existingComments = [];
            try {
                existingComments = currentPayment.comments
                    ? JSON.parse(currentPayment.comments)
                    : [];
            }
            catch (e) {
                // Si hay un error al parsear, asumimos que no hay comentarios
                existingComments = [];
            }
            const newComment = {
                content: comment,
                author: ctx.state.user.id,
                createdAt: new Date().toISOString(),
            };
            // Actualizar el pago con el nuevo comentario
            const payment = (await strapi.entityService.update("api::payment.payment", id, {
                data: {
                    comments: JSON.stringify([...existingComments, newComment]),
                },
                populate: ["owner"],
            }));
            let parsedComments = [];
            try {
                parsedComments = payment.comments ? JSON.parse(payment.comments) : [];
            }
            catch (e) {
                // Si hay un error al parsear, retornamos un array vacío
                parsedComments = [];
            }
            const simplifiedPayment = {
                id: payment.id,
                comments: parsedComments.map((comment) => ({
                    id: comment.id,
                    content: comment.content,
                    createdAt: comment.createdAt,
                    author: typeof comment.author === "number"
                        ? { id: comment.author }
                        : {
                            id: comment.author.id,
                            username: comment.author.username,
                            firstName: comment.author.firstName,
                            lastName: comment.author.lastName,
                        },
                })),
            };
            // Notificar al propietario si el comentario es de un admin
            if ((payment === null || payment === void 0 ? void 0 : payment.owner) && ((_a = ctx.state.user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin") {
                const lastComment = parsedComments[parsedComments.length - 1];
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: "Nuevo comentario en pago",
                        type: "payment_comment",
                        message: `Un administrador ha agregado un comentario a tu pago`,
                        user: payment.owner.id,
                        data: {
                            paymentId: payment.id,
                            commentId: (lastComment === null || lastComment === void 0 ? void 0 : lastComment.id) || null,
                        },
                    },
                });
            }
            return { data: simplifiedPayment };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async getComments(ctx) {
        try {
            const { id } = ctx.params;
            const payment = (await strapi.entityService.findOne("api::payment.payment", id, {
                populate: ["owner"],
            }));
            if (!payment) {
                return ctx.notFound("Payment not found");
            }
            // Intentar parsear los comentarios
            let comments = [];
            try {
                comments = payment.comments ? JSON.parse(payment.comments) : [];
            }
            catch (e) {
                // Si hay un error al parsear, retornamos un array vacío
                comments = [];
            }
            const simplifiedComments = comments.map((comment) => ({
                id: comment.id,
                content: comment.content,
                createdAt: comment.createdAt,
                author: typeof comment.author === "number"
                    ? { id: comment.author }
                    : {
                        id: comment.author.id,
                        username: comment.author.username,
                        firstName: comment.author.firstName,
                        lastName: comment.author.lastName,
                    },
            }));
            return { data: simplifiedComments };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async updateBulkStatus(ctx) {
        try {
            const { ids } = ctx.request.body;
            const { status, reason } = ctx.request.body;
            if (!Array.isArray(ids) || ids.length === 0) {
                return ctx.badRequest("Invalid payment IDs");
            }
            if (!status || !["verified", "rejected"].includes(status)) {
                return ctx.badRequest("Invalid status");
            }
            if (status === "rejected" && !reason) {
                return ctx.badRequest("Rejection reason is required");
            }
            // Verificar que todos los pagos existen
            const payments = await strapi.db
                .query("api::payment.payment")
                .findMany({
                where: { id: { $in: ids } },
                populate: ["owner"],
            });
            if (payments.length !== ids.length) {
                return ctx.badRequest("Some payments were not found");
            }
            // Actualizar los pagos
            await strapi.db.query("api::payment.payment").updateMany({
                where: { id: { $in: ids } },
                data: {
                    status,
                    ...(status === "rejected" ? { rejectionReason: reason } : {}),
                },
            });
            // Obtener los pagos actualizados
            const updatedPayments = await strapi.db
                .query("api::payment.payment")
                .findMany({
                where: { id: { $in: ids } },
            });
            const simplifiedPayments = updatedPayments.map((payment) => ({
                id: payment.id,
                status: payment.status,
                rejectionReason: payment.rejectionReason,
            }));
            // Enviar notificaciones a los propietarios
            await Promise.all(payments.map(async (payment) => {
                var _a;
                // Crear notificación interna
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: `Pago ${status === "verified" ? "verificado" : "rechazado"}`,
                        type: `payment_${status}`,
                        message: `Tu pago ha sido ${status === "verified" ? "verificado" : "rechazado"}${status === "rejected" ? `. Razón: ${reason}` : ""}`,
                        user: payment.owner.id,
                        data: {
                            paymentId: payment.id,
                            status,
                            reason: reason || undefined,
                        },
                    },
                });
                // Enviar correo electrónico al propietario
                try {
                    if (payment.owner && payment.owner.email) {
                        // Preparar datos para la plantilla
                        const emailData = {
                            user: {
                                username: payment.owner.username ||
                                    payment.owner.firstName ||
                                    "Usuario",
                                email: payment.owner.email,
                            },
                            payment: {
                                reference: payment.transactionId || `#${payment.id}`,
                                amount: `$${payment.amount.toLocaleString("es-CO")}`,
                                date: formatDate(payment.dateOfPayment),
                                concept: payment.description ||
                                    ((_a = payment.monthOfPayment) === null || _a === void 0 ? void 0 : _a.join(", ")) ||
                                    "Pago de administración",
                                ...(status === "rejected" && reason
                                    ? { rejectionReason: reason }
                                    : {}),
                            },
                            URL: process.env.FRONTEND_URL || "https://ccpalcan.com",
                        };
                        // Obtener el contenido HTML de la plantilla directamente
                        const templateName = status === "verified"
                            ? "payment-verified"
                            : "payment-rejected";
                        const templatePath = path.join(process.cwd(), "config", "email-templates", `${templateName}.html`);
                        // Leer la plantilla HTML
                        let htmlContent = "";
                        try {
                            const fs = require("fs");
                            htmlContent = fs.readFileSync(templatePath, "utf8");
                            // Reemplazar variables en la plantilla
                            // Reemplazos básicos para las variables más comunes
                            htmlContent = htmlContent
                                .replace(/<%= user\.username %>/g, emailData.user.username)
                                .replace(/<%= payment\.reference %>/g, emailData.payment.reference)
                                .replace(/<%= payment\.amount %>/g, emailData.payment.amount)
                                .replace(/<%= payment\.date %>/g, emailData.payment.date)
                                .replace(/<%= payment\.concept %>/g, emailData.payment.concept)
                                .replace(/<%= URL %>/g, emailData.URL);
                            // Si es un rechazo, reemplazar la razón
                            if (status === "rejected" &&
                                emailData.payment.rejectionReason) {
                                htmlContent = htmlContent.replace(/<%= payment\.rejectionReason %>/g, emailData.payment.rejectionReason);
                            }
                        }
                        catch (readError) {
                            console.error(`[Bulk] Error al leer la plantilla HTML:`, readError);
                            // Si no podemos leer la plantilla, usamos un HTML básico
                            htmlContent = `<h1>Pago ${status === "verified" ? "Verificado" : "Rechazado"}</h1>
                  <p>Hola ${emailData.user.username},</p>
                  <p>Tu pago con referencia ${emailData.payment.reference} por un monto de ${emailData.payment.amount} ha sido ${status === "verified" ? "verificado" : "rechazado"}.</p>
                  ${status === "rejected" && emailData.payment.rejectionReason
                                ? `<p>Razón: ${emailData.payment.rejectionReason}</p>`
                                : ""}
                  <p>Fecha del pago: ${emailData.payment.date}</p>
                  <p>Concepto: ${emailData.payment.concept}</p>
                  <p>Gracias por usar nuestro sistema.</p>`;
                        }
                        // Enviar el correo usando sendEmail en lugar de sendTemplatedEmail
                        await strapi.plugins.email.services.email.send({
                            to: payment.owner.email,
                            from: process.env.SENDGRID_EMAIL || "<EMAIL>",
                            subject: `Pago ${status === "verified" ? "Verificado" : "Rechazado"} - CCPALCAN`,
                            text: `Hola ${emailData.user.username}, tu pago con referencia ${emailData.payment.reference} por un monto de ${emailData.payment.amount} ha sido ${status === "verified" ? "verificado" : "rechazado"}.${status === "rejected" && emailData.payment.rejectionReason
                                ? ` Razón: ${emailData.payment.rejectionReason}`
                                : ""}`,
                            html: htmlContent,
                        });
                    }
                }
                catch (emailError) {
                    console.error(`[Bulk] Error al enviar correo de pago ${status}:`, emailError);
                    // No interrumpimos el flujo si falla el envío de correo
                }
            }));
            return { data: simplifiedPayments };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async getStats(ctx) {
        try {
            const [payments, totalAmount] = await Promise.all([
                strapi.db.query("api::payment.payment").findMany({
                    select: ["id", "status", "amount"],
                    where: {},
                }),
                strapi.db.query("api::payment.payment").findMany({
                    select: ["amount"],
                    where: { status: "verified" },
                }),
            ]);
            const stats = {
                total: payments.length,
                verified: payments.filter((p) => p.status === "verified").length,
                rejected: payments.filter((p) => p.status === "rejected").length,
                pending: payments.filter((p) => p.status === "pending").length,
                totalAmount: totalAmount.reduce((sum, p) => sum + (p.amount || 0), 0),
            };
            return { data: stats };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
    async findByOwner(ctx) {
        try {
            const { ownerId } = ctx.params;
            if (!ownerId) {
                return ctx.badRequest("Owner ID is required");
            }
            const payments = await strapi.db
                .query("api::payment.payment")
                .findMany({
                where: { owner: ownerId },
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                    imgUrl: true,
                },
            });
            const simplifiedPayments = payments.map((payment) => {
                var _a;
                return ({
                    id: payment.id,
                    amount: payment.amount,
                    dateOfPayment: payment.dateOfPayment,
                    monthOfPayment: payment.monthOfPayment || [],
                    beneficiary: payment.beneficiary || "",
                    bank: payment.bank,
                    description: payment.description,
                    transactionId: payment.transactionId || "",
                    status: payment.status || "pending",
                    rejectionReason: payment.rejectionReason,
                    owner: payment.owner
                        ? {
                            id: payment.owner.id,
                            username: payment.owner.username,
                            firstName: payment.owner.firstName,
                            lastName: payment.owner.lastName,
                            imgUrl: (_a = payment.owner.imgUrl) === null || _a === void 0 ? void 0 : _a.url,
                            address: payment.owner.address,
                        }
                        : null,
                    imgUrl: payment.imgUrl
                        ? {
                            url: payment.imgUrl.url,
                            ext: payment.imgUrl.ext,
                            name: payment.imgUrl.name,
                        }
                        : null,
                });
            });
            return { data: simplifiedPayments };
        }
        catch (error) {
            return ctx.throw(500, error);
        }
    },
}));
