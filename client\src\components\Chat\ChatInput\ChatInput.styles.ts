/**
 * Estilos para el componente ChatInput
 */

import styled from 'styled-components';

export const InputContainer = styled.div`
  border-top: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  background-color: ${({ theme }) => theme.background};
  padding: 12px 16px;
`;

export const ReplyIndicator = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f8f9fa' : '#1a1a1a'};
  border-left: 3px solid ${({ theme }) => theme.primary};
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 6px;

  .reply-content {
    flex: 1;
    min-width: 0;
    
    > * {
      display: block;
      margin-bottom: 2px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
`;

export const MainInputContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const InputWrapper = styled.div`
  display: flex;
  align-items: flex-end;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#ffffff' : '#1f1f1f'};
  border: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
  border-radius: 8px;
  transition: border-color 0.2s ease;
  overflow: hidden;

  &:focus-within {
    border-color: ${({ theme }) => theme.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}20;
  }

  .ant-input {
    background-color: transparent !important;
    
    &:focus {
      border-color: transparent !important;
      box-shadow: none !important;
    }
    
    &::placeholder {
      color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
    }
  }
`;

export const ActionButtons = styled.div`
  padding: 4px 8px;
  display: flex;
  align-items: center;
  border-left: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  
  .ant-btn {
    border: none;
    box-shadow: none;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
    }
    
    &.ant-btn-primary {
      &:hover:not(:disabled) {
        background-color: ${({ theme }) => theme.primary};
        transform: scale(1.05);
      }
      
      &:disabled {
        background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
        border-color: ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
        color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
      }
    }
  }
`;

export const InputFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
  margin-top: 4px;
`;

export const TypingIndicator = styled.div`
  padding: 4px 8px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 11px;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;

  .typing-dots {
    display: flex;
    gap: 1px;
    
    span {
      width: 3px;
      height: 3px;
      background-color: currentColor;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }

  @keyframes typing {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export const CharacterCount = styled.span<{ $isNearLimit: boolean; $isOverLimit: boolean }>`
  font-size: 11px;
  color: ${({ $isOverLimit, $isNearLimit, theme }) => {
    if ($isOverLimit) return theme.error || '#ff4d4f';
    if ($isNearLimit) return theme.warning || '#faad14';
    return theme.textSecondary || '#8c8c8c';
  }};
  font-weight: ${({ $isOverLimit, $isNearLimit }) => 
    $isOverLimit || $isNearLimit ? '500' : 'normal'
  };
`;

export const MentionSuggestions = styled.div`
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#ffffff' : '#1f1f1f'};
  border: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
`;

export const MentionItem = styled.div<{ $isSelected: boolean }>`
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: ${({ $isSelected, theme }) => 
    $isSelected 
      ? theme.primary + '20'
      : 'transparent'
  };
  
  &:hover {
    background-color: ${({ theme }) => theme.primary + '10'};
  }
  
  .mention-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.primary};
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
  }
  
  .mention-info {
    flex: 1;
    min-width: 0;
    
    .mention-name {
      font-weight: 500;
      color: ${({ theme }) => theme.textMain};
    }
    
    .mention-username {
      font-size: 12px;
      color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
    }
  }
`;

export const FilePreview = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f8f9fa' : '#1a1a1a'};
  border-radius: 6px;
`;

export const FileItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#ffffff' : '#2a2a2a'};
  border: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
  border-radius: 4px;
  font-size: 12px;
  
  .file-icon {
    color: ${({ theme }) => theme.primary};
  }
  
  .file-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .file-size {
    color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  }
  
  .remove-file {
    cursor: pointer;
    color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
    
    &:hover {
      color: ${({ theme }) => theme.error || '#ff4d4f'};
    }
  }
`;

export const EmojiButton = styled.button`
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
  }
`;
