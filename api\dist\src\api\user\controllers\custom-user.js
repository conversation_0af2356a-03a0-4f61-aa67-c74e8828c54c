"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("plugin::users-permissions.user", ({ strapi }) => ({
    // ACTUALIZAR AVATAR
    async updateImgUrl(ctx) {
        var _a, _b;
        const { id } = ctx.params;
        if (!ctx.state.user) {
            return ctx.forbidden("Usuario no autenticado");
        }
        if (ctx.state.user.id !== Number(id)) {
            return ctx.forbidden("No tienes permiso para modificar este recurso");
        }
        try {
            const imgUrl = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a.imgUrl;
            if (!imgUrl) {
                return ctx.badRequest("No se recibió el archivo imgUrl");
            }
            console.log("Archivo imgUrl recibido:", imgUrl);
            const uploadedFiles = await strapi.plugins["upload"].services.upload.upload({
                data: {},
                files: imgUrl,
            });
            console.log("Archivo subido:", uploadedFiles);
            if (!((_b = uploadedFiles[0]) === null || _b === void 0 ? void 0 : _b.id)) {
                return ctx.internalServerError("No se pudo subir la imagen");
            }
            const updatedUser = await strapi
                .query("plugin::users-permissions.user")
                .update({
                where: { id },
                data: {
                    imgUrl: uploadedFiles[0].id,
                },
            });
            return ctx.send(updatedUser);
        }
        catch (error) {
            console.error("Error al procesar la imagen:", error);
            return ctx.internalServerError("Error al procesar la imagen");
        }
    },
    // ACTUALIZAR FIRMA
    // Obtener la firma del usuario actual
    async getSignature(ctx) {
        if (!ctx.state.user) {
            return ctx.forbidden("Usuario no autenticado");
        }
        try {
            const userId = ctx.state.user.id;
            const user = await strapi
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: userId },
                populate: ["signature"],
            });
            if (!user.signature) {
                return ctx.notFound("No se encontró la firma del usuario");
            }
            return ctx.send({
                signature: user.signature,
            });
        }
        catch (error) {
            console.error("Error al obtener la firma:", error);
            return ctx.internalServerError("Error al obtener la firma");
        }
    },
    // Actualizar la firma del usuario
    async updateSignature(ctx) {
        var _a, _b;
        if (!ctx.state.user) {
            return ctx.forbidden("Usuario no autenticado");
        }
        const userId = ctx.state.user.id;
        try {
            const signature = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a.signature;
            if (!signature) {
                return ctx.badRequest("No se recibió el archivo signature");
            }
            console.log("Archivo signature recibido:", signature);
            // Obtener el usuario actual para ver si ya tiene una firma
            const currentUser = await strapi
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: userId },
                populate: ["signature"],
            });
            // Si el usuario ya tiene una firma, eliminarla
            if (currentUser.signature) {
                try {
                    await strapi.plugins["upload"].services.upload.remove({
                        id: currentUser.signature.id,
                    });
                    console.log("Firma anterior eliminada:", currentUser.signature.id);
                }
                catch (error) {
                    console.error("Error al eliminar la firma anterior:", error);
                    // Continuar incluso si hay un error al eliminar la firma anterior
                }
            }
            // Subir la nueva firma
            const uploadedFiles = await strapi.plugins["upload"].services.upload.upload({
                data: {},
                files: signature,
            });
            console.log("Archivo de firma subido:", uploadedFiles);
            if (!((_b = uploadedFiles[0]) === null || _b === void 0 ? void 0 : _b.id)) {
                return ctx.internalServerError("No se pudo subir la firma");
            }
            // Actualizar el usuario con la nueva firma
            const updatedUser = await strapi
                .query("plugin::users-permissions.user")
                .update({
                where: { id: userId },
                data: {
                    signature: uploadedFiles[0].id,
                },
                populate: ["signature"],
            });
            return ctx.send(updatedUser);
        }
        catch (error) {
            console.error("Error al procesar la firma:", error);
            return ctx.internalServerError("Error al procesar la firma");
        }
    },
}));
