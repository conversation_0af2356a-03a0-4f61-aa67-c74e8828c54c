/**
 * Hook para manejar WebSocket del chat
 */

import { useEffect, useRef, useState, useCallback } from "react";
import { readToken } from "@app/services/localStorage.service";
import { api } from "@app/config/config";
import type {
  ChatWebSocketMessage,
  ChatMessage,
  ChatTypingIndicator,
  UseWebSocketChatReturn,
} from "@app/types/chat";

interface UseWebSocketChatProps {
  onMessageReceived?: (message: ChatMessage) => void;
  onMessageUpdated?: (messageId: number, updates: Partial<ChatMessage>) => void;
  onMessageDeleted?: (messageId: number) => void;
  onReactionUpdated?: (
    messageId: number,
    reactions: Record<string, number[]>,
  ) => void;
  onTypingUpdate?: (typingData: ChatTypingIndicator) => void;
  onUserPresenceUpdate?: (userId: number, isOnline: boolean) => void;
}

export const useWebSocketChat = ({
  onMessageReceived,
  onMessageUpdated,
  onMessageDeleted,
  onReactionUpdated,
  onTypingUpdate,
  onUserPresenceUpdate,
}: UseWebSocketChatProps = {}): UseWebSocketChatReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Obtener URL del WebSocket
  const getWebSocketUrl = useCallback(() => {
    const token = readToken();
    if (!token) {
      throw new Error("No hay token de autenticación");
    }

    // Convertir HTTP/HTTPS a WS/WSS
    const wsProtocol = api.startsWith("https") ? "wss" : "ws";
    const baseUrl = api.replace(/^https?:\/\//, "").replace("/api", "");

    return `${wsProtocol}://${baseUrl}/ws/notifications?token=${encodeURIComponent(token)}`;
  }, []);

  // Conectar WebSocket
  const connect = useCallback(() => {
    try {
      const wsUrl = getWebSocketUrl();
      console.log("🔌 Conectando a WebSocket del chat:", wsUrl);

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log("✅ WebSocket del chat conectado");
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const message: ChatWebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error("❌ Error procesando mensaje WebSocket:", error);
        }
      };

      ws.onclose = (event) => {
        console.log(
          "🔌 WebSocket del chat desconectado:",
          event.code,
          event.reason,
        );
        setIsConnected(false);
        wsRef.current = null;

        // Intentar reconectar si no fue un cierre intencional
        if (
          event.code !== 1000 &&
          reconnectAttemptsRef.current < maxReconnectAttempts
        ) {
          const delay = Math.min(
            1000 * Math.pow(2, reconnectAttemptsRef.current),
            30000,
          );
          console.log(
            `🔄 Reintentando conexión en ${delay}ms (intento ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`,
          );

          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, delay);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setConnectionError(
            "No se pudo reconectar al chat. Recarga la página.",
          );
        }
      };

      ws.onerror = (error) => {
        console.error("❌ Error en WebSocket del chat:", error);
        setConnectionError("Error de conexión con el chat");
      };
    } catch (error) {
      console.error("❌ Error creando conexión WebSocket:", error);
      setConnectionError("Error al conectar con el chat");
    }
  }, [getWebSocketUrl]);

  // Manejar mensajes WebSocket
  const handleWebSocketMessage = useCallback(
    (message: ChatWebSocketMessage) => {
      console.log("📨 Mensaje WebSocket recibido:", message.type, message.data);

      switch (message.type) {
        case "chat_message":
          if (onMessageReceived) {
            onMessageReceived(message.data);
          }
          break;

        case "chat_message_updated":
          if (onMessageUpdated) {
            onMessageUpdated(message.data.id, {
              content: message.data.content,
              isEdited: message.data.isEdited,
              editedAt: message.data.editedAt,
            });
          }
          break;

        case "chat_message_deleted":
          if (onMessageDeleted) {
            onMessageDeleted(message.data.id);
          }
          break;

        case "chat_reaction_updated":
          if (onReactionUpdated) {
            onReactionUpdated(message.data.messageId, message.data.reactions);
          }
          break;

        case "chat_typing":
          if (onTypingUpdate) {
            onTypingUpdate(message.data);
          }
          break;

        case "chat_user_presence":
          if (onUserPresenceUpdate) {
            onUserPresenceUpdate(message.data.userId, message.data.isOnline);
          }
          break;

        default:
          console.log(
            "📨 Tipo de mensaje WebSocket no manejado:",
            message.type,
          );
      }
    },
    [
      onMessageReceived,
      onMessageUpdated,
      onMessageDeleted,
      onReactionUpdated,
      onTypingUpdate,
      onUserPresenceUpdate,
    ],
  );

  // Enviar indicador de escritura
  const sendTypingIndicator = useCallback(
    (channelId: number, isTyping: boolean) => {
      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        return;
      }

      try {
        // Limpiar timeout anterior
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }

        // Enviar indicador de escritura
        wsRef.current.send(
          JSON.stringify({
            type: "chat_typing",
            data: {
              channelId,
              isTyping,
            },
            timestamp: new Date().toISOString(),
          }),
        );

        // Si está escribiendo, programar envío de "no escribiendo" después de 3 segundos
        if (isTyping) {
          typingTimeoutRef.current = setTimeout(() => {
            sendTypingIndicator(channelId, false);
          }, 3000);
        }
      } catch (error) {
        console.error("❌ Error enviando indicador de escritura:", error);
      }
    },
    [],
  );

  // Desconectar WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, "Desconexión intencional");
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionError(null);
    reconnectAttemptsRef.current = 0;
  }, []);

  // Efecto para conectar/desconectar
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Efecto para limpiar timeouts al desmontar
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    isConnected,
    connectionError,
    sendTypingIndicator,
  };
};
