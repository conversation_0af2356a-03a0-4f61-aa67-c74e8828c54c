"use strict";
/**
 * Base notification service
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ strapi }) => ({
    async send(recipient, notification, config) {
        try {
            if (!(config === null || config === void 0 ? void 0 : config.enabled)) {
                return;
            }
            const { channels } = config;
            // 1. Crear notificación interna en Strapi
            await strapi.service("api::notification.notification").create({
                data: {
                    title: notification.title,
                    type: notification.type,
                    message: notification.message,
                    user: recipient.id,
                    data: notification.data,
                },
            });
            // 2. Enviar notificación por WebSocket
            if (strapi.websocket) {
                strapi.websocket.sendNotificationToUser(recipient.id, notification);
            }
            // 3. Si el email está habilitado, enviar correo
            if (channels.email && recipient.email) {
                await strapi.plugins["email"].services.email.send({
                    to: recipient.email,
                    subject: notification.title,
                    text: notification.message,
                    html: this.getEmailTemplate(notification, recipient),
                });
            }
            // 4. Enviar WhatsApp si está habilitado
            if (channels.whatsapp && recipient.phone) {
                try {
                    await strapi
                        .service("api::whatsapp.whatsapp")
                        .send(recipient.phone, `${notification.title}\\n\\n${notification.message}`);
                }
                catch (error) {
                    console.error("Error sending WhatsApp notification:", error);
                }
            }
        }
        catch (error) {
            console.error("Error sending notification:", error);
        }
    },
    getEmailTemplate(notification, recipient) {
        return `
      <h2>Estimado(a) ${recipient.firstName} ${recipient.lastName}</h2>
      <p>${notification.message}</p>
      <p>Por favor, ingrese a la plataforma para más detalles.</p>
    `;
    },
});
