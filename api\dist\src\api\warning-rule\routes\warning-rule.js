"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: 'GET',
            path: '/warning-rules',
            handler: 'warning-rule.find',
            config: {
                policies: [],
                description: 'Obtener todos los artículos infringidos',
                tag: {
                    plugin: 'warning-rule',
                    name: 'WarningRule',
                    actionType: 'find'
                }
            }
        },
        {
            method: 'GET',
            path: '/warning-rules/:id',
            handler: 'warning-rule.findOne',
            config: {
                policies: [],
                description: 'Obtener un artículo específico',
                tag: {
                    plugin: 'warning-rule',
                    name: 'WarningRule',
                    actionType: 'findOne'
                }
            }
        },
        {
            method: 'POST',
            path: '/warning-rules',
            handler: 'warning-rule.create',
            config: {
                policies: [],
                description: 'Crear un nuevo artículo',
                tag: {
                    plugin: 'warning-rule',
                    name: 'WarningRule',
                    actionType: 'create'
                }
            }
        },
        {
            method: 'PUT',
            path: '/warning-rules/:id',
            handler: 'warning-rule.update',
            config: {
                policies: [],
                description: 'Actualizar un artículo existente',
                tag: {
                    plugin: 'warning-rule',
                    name: 'WarningRule',
                    actionType: 'update'
                }
            }
        },
        {
            method: 'DELETE',
            path: '/warning-rules/:id',
            handler: 'warning-rule.delete',
            config: {
                policies: [],
                description: 'Eliminar un artículo',
                tag: {
                    plugin: 'warning-rule',
                    name: 'WarningRule',
                    actionType: 'delete'
                }
            }
        }
    ]
};
