{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payment", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"amount": {"type": "decimal", "required": true}, "dateOfPayment": {"type": "datetime", "required": true}, "monthOfPayment": {"type": "json", "required": false}, "beneficiary": {"type": "string", "required": false}, "transactionId": {"type": "string", "required": false}, "bank": {"type": "string"}, "description": {"type": "text"}, "status": {"type": "enumeration", "enum": ["pending", "verified", "rejected"], "default": "pending"}, "owner": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "payments"}, "imgUrl": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "comments": {"type": "relation", "relation": "oneToMany", "target": "api::payment-comment.payment-comment", "mappedBy": "payment"}, "rejectionReason": {"type": "text"}}}