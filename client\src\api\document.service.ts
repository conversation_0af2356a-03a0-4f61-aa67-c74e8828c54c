import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_BASE_URL } from "../config";

// Definición de tipos para documentos
export type DocumentType = "EXTERNAL" | "INTERNAL" | "CONSEJO";

export interface Document {
  id: string;
  name: string;
  url: string;
  type: DocumentType;
  createdAt: string;
  updatedAt: string;
}

export interface GetDocumentsParams {
  type?: DocumentType;
  limit?: number;
  page?: number;
}

export interface UploadDocumentResponse {
  document: Document;
  message: string;
}

// Crear el API service con RTK Query
export const documentApi = createApi({
  reducerPath: "documentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      // Obtener token del localStorage si existe
      const token = localStorage.getItem("token");
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ["Documents"],
  endpoints: (builder) => ({
    // Obtener documentos con filtrado opcional por tipo
    getDocuments: builder.query<Document[], GetDocumentsParams>({
      query: (params) => ({
        url: "/documents",
        params,
      }),
      providesTags: ["Documents"],
    }),

    // Obtener un documento por ID
    getDocumentById: builder.query<Document, string>({
      query: (id) => `/documents/${id}`,
      providesTags: (result, error, id) => [{ type: "Documents", id }],
    }),

    // Subir un nuevo documento
    uploadDocument: builder.mutation<UploadDocumentResponse, FormData>({
      query: (formData) => ({
        url: "/documents",
        method: "POST",
        body: formData,
        formData: true,
      }),
      invalidatesTags: ["Documents"],
    }),

    // Actualizar un documento existente
    updateDocument: builder.mutation<
      Document,
      { id: string; formData: FormData }
    >({
      query: ({ id, formData }) => ({
        url: `/documents/${id}`,
        method: "PUT",
        body: formData,
        formData: true,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Documents", id }],
    }),

    // Eliminar un documento
    deleteDocument: builder.mutation<
      { success: boolean; message: string },
      string
    >({
      query: (id) => ({
        url: `/documents/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Documents", id }],
    }),
  }),
});

// Exportar hooks generados automáticamente
export const {
  useGetDocumentsQuery,
  useGetDocumentByIdQuery,
  useUploadDocumentMutation,
  useUpdateDocumentMutation,
  useDeleteDocumentMutation,
} = documentApi;
