"use strict";
/**
 * pool-cleaning controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::pool-cleaning.pool-cleaning", ({ strapi }) => ({
    async create(ctx) {
        try {
            const { type, ph, chlorine, notes, cleanedAt } = ctx.request.body.data;
            const entry = await strapi.entityService.create("api::pool-cleaning.pool-cleaning", {
                data: {
                    type,
                    ph,
                    chlorine,
                    notes,
                    cleanedAt: cleanedAt || new Date().toISOString(),
                },
            });
            return ctx.send(entry);
        }
        catch (error) {
            console.error("Error al crear registro de limpieza:", error);
            return ctx.badRequest({
                error: error.message + " Error al crear registro de limpieza",
            });
        }
    },
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const { type, ph, chlorine, notes, cleanedAt } = ctx.request.body.data;
            const entry = await strapi.entityService.update("api::pool-cleaning.pool-cleaning", id, {
                data: {
                    type,
                    ph,
                    chlorine,
                    notes,
                    cleanedAt,
                },
            });
            return ctx.send(entry);
        }
        catch (error) {
            console.error("Error al actualizar registro de limpieza:", error);
            return ctx.badRequest({
                error: error.message + " Error al actualizar registro de limpieza",
            });
        }
    },
    async delete(ctx) {
        try {
            const { id } = ctx.params;
            const entry = await strapi.entityService.delete("api::pool-cleaning.pool-cleaning", id);
            return ctx.send(entry);
        }
        catch (error) {
            console.error("Error al eliminar registro de limpieza:", error);
            return ctx.badRequest({
                error: error.message + " Error al eliminar registro de limpieza",
            });
        }
    },
    async findAll(ctx) {
        try {
            const entries = await strapi.db
                .query("api::pool-cleaning.pool-cleaning")
                .findMany({
                orderBy: { cleanedAt: "DESC" },
            });
            // Transformar los datos al formato esperado por el frontend
            const processedEntries = entries.map((entry) => ({
                id: entry.id,
                type: entry.type,
                ph: entry.ph,
                chlorine: entry.chlorine,
                notes: entry.notes,
                cleanedAt: entry.cleanedAt,
            }));
            return ctx.send(processedEntries);
        }
        catch (error) {
            console.error("Error en findAll:", error);
            ctx.throw(500, error);
        }
    },
}));
