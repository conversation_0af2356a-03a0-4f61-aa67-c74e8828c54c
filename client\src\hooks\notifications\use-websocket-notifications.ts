/**
 * Hook para manejar notificaciones en tiempo real con WebSockets
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { message } from "antd";
import { useAuthContext } from "@app/context/auth/AuthContext";

interface WebSocketNotification {
  id: number;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  data?: any;
  read: boolean;
  createdAt: string;
  user?: any;
}

interface WebSocketMessage {
  type: "connection" | "notification" | "heartbeat" | "broadcast" | "pong";
  data?: WebSocketNotification;
  notification?: WebSocketNotification;
  message?: string;
  timestamp?: string;
}

type ConnectionStatus = "CONNECTING" | "OPEN" | "CLOSED" | "ERROR";

interface UseWebSocketNotificationsReturn {
  connectionStatus: ConnectionStatus;
  connectionError: string | null;
  isConnected: boolean;
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => boolean;
  lastMessage: WebSocketMessage | null;
}

const MAX_RETRIES = 10;
const RETRY_INTERVAL = 3000; // 3 segundos
const HEARTBEAT_INTERVAL = 30000; // 30 segundos

export const useWebSocketNotifications =
  (): UseWebSocketNotificationsReturn => {
    const { user, token, isAuthenticated } = useAuthContext();
    const queryClient = useQueryClient();

    const [connectionStatus, setConnectionStatus] =
      useState<ConnectionStatus>("CLOSED");
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(
      null,
    );

    const wsRef = useRef<WebSocket | null>(null);
    const retryCountRef = useRef(0);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const reconnectAttemptsRef = useRef(0);

    const sendMessage = useCallback((message: any): boolean => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        try {
          wsRef.current.send(JSON.stringify(message));
          console.log("\ud83d\udce4 Mensaje enviado:", message);
          return true;
        } catch (error) {
          console.error("\u274c Error enviando mensaje:", error);
          return false;
        }
      } else {
        console.log("\u274c WebSocket no est\u00e1 conectado");
        return false;
      }
    }, []);

    const stopHeartbeat = useCallback(() => {
      if (heartbeatTimeoutRef.current) {
        clearInterval(heartbeatTimeoutRef.current);
        heartbeatTimeoutRef.current = null;
      }
    }, []);

    const startHeartbeat = useCallback(() => {
      stopHeartbeat();

      heartbeatTimeoutRef.current = setInterval(() => {
        sendMessage({ type: "ping", timestamp: new Date().toISOString() });
      }, HEARTBEAT_INTERVAL);
    }, [sendMessage, stopHeartbeat]);

    const handleNotification = useCallback(
      (notification: WebSocketNotification | undefined) => {
        if (!notification) return;

        console.log("\ud83d\udd14 Nueva notificaci\u00f3n:", notification);

        queryClient.setQueryData(
          ["notifications", user?.id],
          (oldData: any) => {
            if (!oldData) return { data: [notification] };

            const exists = oldData.data.some(
              (n: any) => n.id === notification.id,
            );

            if (exists) {
              return {
                ...oldData,
                data: oldData.data.map((n: any) =>
                  n.id === notification.id ? notification : n,
                ),
              };
            } else {
              return {
                ...oldData,
                data: [notification, ...oldData.data],
              };
            }
          },
        );

        message.info({ content: notification.title, duration: 4 });
      },
      [queryClient, user?.id],
    );

    const handleBroadcast = useCallback((data: WebSocketMessage) => {
      console.log("\ud83d\udce1 Broadcast recibido:", data);

      if (data.notification) {
        message.info({
          content: `\ud83d\udce1 ${data.notification.title}`,
          duration: 6,
        });
      }
    }, []);

    const scheduleReconnect = useCallback(() => {
      if (retryCountRef.current >= MAX_RETRIES) {
        console.log("\u274c M\u00e1ximo n\u00famero de reintentos alcanzado");
        return;
      }

      retryCountRef.current++;
      reconnectAttemptsRef.current++;

      const delay = Math.min(
        RETRY_INTERVAL * Math.pow(2, reconnectAttemptsRef.current - 1),
        30000,
      );

      console.log(
        `\u23f3 Reintentando conexi\u00f3n en ${delay}ms (intento ${retryCountRef.current}/${MAX_RETRIES})`,
      );

      retryTimeoutRef.current = setTimeout(() => {
        connect();
      }, delay);
    }, []);

    const connect = useCallback(() => {
      if (!user || !token || !isAuthenticated) {
        console.log("\u274c WebSocket: Usuario no autenticado");
        return;
      }

      if (wsRef.current?.readyState === WebSocket.OPEN) {
        console.log("\u2705 WebSocket: Ya conectado");
        return;
      }

      try {
        console.log(
          `\ud83d\udd17 Conectando WebSocket para usuario ${user.id}...`,
        );
        setConnectionStatus("CONNECTING");
        setConnectionError(null);

        const wsUrl = `ws://localhost:1337/ws/notifications?token=${token}`;
        const ws = new WebSocket(wsUrl);
        wsRef.current = ws;

        ws.onopen = () => {
          console.log("\u2705 WebSocket conectado exitosamente");
          setConnectionStatus("OPEN");
          setConnectionError(null);
          retryCountRef.current = 0;
          reconnectAttemptsRef.current = 0;

          if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
            retryTimeoutRef.current = null;
          }

          startHeartbeat();

          sendMessage({
            type: "subscribe",
            topics: ["notifications", "broadcasts"],
          });
        };

        ws.onmessage = (event) => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            console.log("\ud83d\udce8 Mensaje WebSocket recibido:", data);

            setLastMessage(data);

            switch (data.type) {
              case "connection":
                console.log("\ud83d\udd17 Conexi\u00f3n WebSocket establecida");
                break;
              case "notification":
                handleNotification(data.data || data.notification);
                break;
              case "broadcast":
                handleBroadcast(data);
                break;
              case "heartbeat":
                console.log("\ud83d\udc93 Heartbeat recibido");
                break;
              case "pong":
                console.log("\ud83c\udfd3 Pong recibido");
                break;
              default:
                console.log("\u2753 Tipo de mensaje desconocido:", data.type);
            }
          } catch (error) {
            console.error("\u274c Error procesando mensaje WebSocket:", error);
          }
        };

        ws.onerror = (error) => {
          console.error("\u274c Error WebSocket:", error);
          setConnectionStatus("ERROR");
          setConnectionError("Error en la conexi\u00f3n WebSocket");
        };

        ws.onclose = (event) => {
          console.log(
            `\ud83d\udd0c WebSocket cerrado. C\u00f3digo: ${event.code}, Raz\u00f3n: ${event.reason}`,
          );
          setConnectionStatus("CLOSED");
          stopHeartbeat();

          if (event.code !== 1000 && retryCountRef.current < MAX_RETRIES) {
            scheduleReconnect();
          } else if (retryCountRef.current >= MAX_RETRIES) {
            setConnectionError(
              "No se pudo reconectar despu\u00e9s de varios intentos",
            );
          }
        };
      } catch (error) {
        console.error("\u274c Error creando WebSocket:", error);
        setConnectionStatus("ERROR");
        setConnectionError("Error creando conexi\u00f3n WebSocket");
      }
    }, [
      user,
      token,
      isAuthenticated,
      startHeartbeat,
      sendMessage,
      handleNotification,
      handleBroadcast,
    ]);

    const disconnect = useCallback(() => {
      console.log("\ud83d\udd0c Desconectando WebSocket...");

      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }

      stopHeartbeat();

      if (wsRef.current) {
        wsRef.current.close(1000, "Desconexi\u00f3n intencional");
        wsRef.current = null;
      }

      setConnectionStatus("CLOSED");
      setConnectionError(null);
      retryCountRef.current = 0;
      reconnectAttemptsRef.current = 0;
    }, [stopHeartbeat]);

    useEffect(() => {
      if (user && isAuthenticated && token) {
        connect();
      } else {
        disconnect();
      }

      return () => {
        disconnect();
      };
    }, [user?.id, isAuthenticated, token, connect, disconnect]);

    const isConnected = connectionStatus === "OPEN";

    return {
      connectionStatus,
      connectionError,
      isConnected,
      connect,
      disconnect,
      sendMessage,
      lastMessage,
    };
  };
