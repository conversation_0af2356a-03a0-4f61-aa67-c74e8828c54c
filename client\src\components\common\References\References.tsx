import { Typography } from "antd";
import { Link } from "react-router-dom";
import * as S from "./References.styles";

export const References = () => {
  return (
    <S.ReferencesWrapper>
      <S.Text>
        Made with ❤️ by{" "}
        <a
          href="https://codecastle.vercel.app/"
          target="_blank"
          rel="noreferrer"
        >
          <Typography.Text strong type="warning">
            C
          </Typography.Text>
          <Typography.Text>ode</Typography.Text>
          <Typography.Text strong type="warning">
            C
          </Typography.Text>
          <Typography.Text>astle </Typography.Text>
          <Typography.Text strong>{"<🏰>"}</Typography.Text>
        </a>
        &copy; {new Date().getFullYear()}. All rights reserved .
      </S.Text>
      <S.Text style={{ marginTop: "8px", fontSize: "12px" }}>
        <Link
          to="/auth/privacy-policy-page"
          style={{ color: "#666", textDecoration: "underline" }}
        >
          Política de Privacidad
        </Link>
      </S.Text>
    </S.ReferencesWrapper>
  );
};
