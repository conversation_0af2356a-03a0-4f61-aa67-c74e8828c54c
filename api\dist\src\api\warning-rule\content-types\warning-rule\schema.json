{"kind": "collectionType", "collectionName": "warning_rules", "info": {"singularName": "warning-rule", "pluralName": "warning-rules", "displayName": "Warning Rule", "description": "Reglas y artículos que pueden ser infringidos"}, "options": {"draftAndPublish": false}, "attributes": {"article": {"type": "string", "required": true, "unique": true}, "description": {"type": "text", "required": true}, "chapter": {"type": "string", "required": true}, "warnings": {"type": "relation", "relation": "manyToMany", "target": "api::warning.warning", "inversedBy": "rules"}}}