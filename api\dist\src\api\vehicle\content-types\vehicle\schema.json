{"kind": "collectionType", "collectionName": "vehicles", "info": {"singularName": "vehicle", "pluralName": "vehicles", "displayName": "Vehicle", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"brand": {"type": "string"}, "color": {"type": "string"}, "plaque": {"type": "string"}, "type": {"type": "enumeration", "enum": ["CAR", "MOTORBIKE", "BICYCLE"]}, "imgUrl": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "owner": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "vehicles"}}}