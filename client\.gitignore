b# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions



# testing
/coverage

# production
/dist

# misc
.DS_Store
.env
.env.development
.env.example
.env.production

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.idea

# generated files
/public/themes

.eslintcache
eslintcache
