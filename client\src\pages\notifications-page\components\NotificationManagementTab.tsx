import { Card, Space, Typography, Di<PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from "antd";
import {
  SendOutlined,
  TeamOutlined,
  UserOutlined,
  InfoCircleOutlined,
  NotificationOutlined,
} from "@ant-design/icons";
import { BulkNotificationButton } from "@app/components/notifications/BulkNotificationButton";
import { useAppSelector } from "@app/hooks/reduxHooks";

const { Title, Text, Paragraph } = Typography;

export const NotificationManagementTab = () => {
  const user = useAppSelector((state) => state.user.user);

  // Verificar si el usuario tiene permisos
  const hasPermission = () => {
    if (!user?.role?.type) return false;
    const allowedRoles = ["admin", "consejero", "vigilante"];
    return allowedRoles.includes(user.role.type.toLowerCase());
  };

  const userRole = user?.role?.type?.toLowerCase();
  const canManage = hasPermission();

  return (
    <div style={{ padding: "24px 0" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* Header */}
        <div>
          <Title level={3}>
            <SendOutlined style={{ marginRight: 8 }} />
            Gestión de Notificaciones Masivas
          </Title>
          <Paragraph type="secondary">
            Envía notificaciones importantes a todos los usuarios o grupos
            específicos del sistema.
          </Paragraph>
        </div>

        {/* Información de permisos */}
        <Alert
          message={
            canManage
              ? `Tienes permisos para enviar notificaciones masivas (Rol: ${userRole})`
              : `No tienes permisos para enviar notificaciones masivas (Rol: ${userRole})`
          }
          description={
            canManage
              ? "Puedes enviar notificaciones a todos los usuarios o filtrar por roles específicos."
              : "Solo los administradores, consejeros y vigilantes pueden enviar notificaciones masivas."
          }
          type={canManage ? "success" : "warning"}
          showIcon
          icon={canManage ? <UserOutlined /> : <InfoCircleOutlined />}
        />

        {/* Panel principal */}
        <Card
          title={
            <Space>
              <NotificationOutlined />
              <span>Panel de Envío</span>
            </Space>
          }
          style={{ width: "100%" }}
        >
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            {canManage ? (
              <>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    Enviar Notificación Masiva
                  </Title>
                  <Text type="secondary">
                    Crea y envía notificaciones que aparecerán en tiempo real
                    para todos los usuarios seleccionados.
                  </Text>
                </div>

                <Divider style={{ margin: "16px 0" }} />

                <div style={{ textAlign: "center" }}>
                  <BulkNotificationButton size="large" type="primary" />
                </div>

                <Divider style={{ margin: "16px 0" }} />

                <div>
                  <Title level={5}>
                    Plantillas Disponibles para tu Rol ({userRole}):
                  </Title>
                  <ul style={{ paddingLeft: 20 }}>
                    {userRole === "vigilante" && (
                      <>
                        <li>
                          💧 <strong>Servicios de Agua</strong> -
                          Llegada/suspensión del servicio (Aqualia)
                        </li>
                        <li>
                          🗑️ <strong>Recolección de Basura</strong> - Horarios
                          disponibles/no disponibles
                        </li>
                        <li>
                          ⚡ <strong>Servicios Eléctricos</strong> - Personal de
                          CENS trabajando
                        </li>
                        <li>
                          🔥 <strong>Servicios de Gas</strong> - Personal de
                          Gases del Oriente
                        </li>
                      </>
                    )}
                    {userRole === "consejero" && (
                      <>
                        <li>
                          📋 <strong>Asambleas</strong> - Convocatorias y
                          reuniones
                        </li>
                        <li>
                          💰 <strong>Pagos</strong> - Recordatorios de
                          administración
                        </li>
                        <li>
                          📝 <strong>Normas</strong> - Actualizaciones de
                          convivencia
                        </li>
                        <li>
                          🏗️ <strong>Mantenimiento</strong> - Trabajos en zonas
                          comunes
                        </li>
                      </>
                    )}
                    {userRole === "admin" && (
                      <>
                        <li>
                          💻 <strong>Sistema</strong> - Actualizaciones y
                          mantenimiento
                        </li>
                        <li>
                          🆕 <strong>Funcionalidades</strong> - Nuevas
                          características
                        </li>
                        <li>
                          🔧 <strong>Soporte Técnico</strong> - Información del
                          sistema
                        </li>
                      </>
                    )}
                  </ul>
                </div>

                <div>
                  <Title level={5}>Características del Sistema:</Title>
                  <ul style={{ paddingLeft: 20 }}>
                    <li>
                      📱 <strong>Notificaciones en tiempo real</strong> - Los
                      usuarios conectados las verán instantáneamente
                    </li>
                    <li>
                      🎯 <strong>Filtros por rol</strong> - Envía a todos o solo
                      a roles específicos
                    </li>
                    <li>
                      📝 <strong>Plantillas inteligentes</strong> - Organizadas
                      según tu rol y responsabilidades
                    </li>
                    <li>
                      🔔 <strong>Tipos de notificación</strong> - Info, éxito,
                      advertencia o error
                    </li>
                    <li>
                      📊 <strong>Reporte de envío</strong> - Confirmación de
                      cuántos usuarios recibieron la notificación
                    </li>
                  </ul>
                </div>
              </>
            ) : (
              <div style={{ textAlign: "center", padding: "40px 0" }}>
                <TeamOutlined
                  style={{ fontSize: 48, color: "#d9d9d9", marginBottom: 16 }}
                />
                <Title level={4} type="secondary">
                  Acceso Restringido
                </Title>
                <Text type="secondary">
                  Esta funcionalidad está disponible solo para:
                </Text>
                <div style={{ marginTop: 16 }}>
                  <Space direction="vertical">
                    <Text>
                      👑 <strong>Administradores</strong>
                    </Text>
                    <Text>
                      🏛️ <strong>Consejeros</strong>
                    </Text>
                    <Text>
                      🛡️ <strong>Vigilantes</strong>
                    </Text>
                  </Space>
                </div>
              </div>
            )}
          </Space>
        </Card>

        {/* Información adicional */}
        {canManage && (
          <Card
            size="small"
            style={{ backgroundColor: "#f6ffed", border: "1px solid #b7eb8f" }}
          >
            <Space direction="vertical" size="small">
              <Text strong>💡 Consejos específicos para {userRole}:</Text>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {userRole === "vigilante" && (
                  <>
                    <li>
                      🕐 <strong>Horarios:</strong> Especifica siempre horarios
                      exactos para servicios
                    </li>
                    <li>
                      🏢 <strong>Empresas:</strong> Menciona la empresa
                      responsable (Aqualia, CENS, Gases del Oriente)
                    </li>
                    <li>
                      ⏱️ <strong>Duración:</strong> Indica tiempo estimado de
                      trabajos o suspensiones
                    </li>
                    <li>
                      📍 <strong>Ubicación:</strong> Especifica áreas o bloques
                      afectados
                    </li>
                  </>
                )}
                {userRole === "consejero" && (
                  <>
                    <li>
                      📅 <strong>Fechas:</strong> Siempre incluye fechas y
                      horarios específicos
                    </li>
                    <li>
                      💰 <strong>Montos:</strong> Especifica valores exactos en
                      recordatorios de pago
                    </li>
                    <li>
                      📋 <strong>Orden del día:</strong> Lista los temas a
                      tratar en asambleas
                    </li>
                    <li>
                      📞 <strong>Contacto:</strong> Proporciona información de
                      contacto para dudas
                    </li>
                  </>
                )}
                {userRole === "admin" && (
                  <>
                    <li>
                      🔧 <strong>Mantenimiento:</strong> Especifica duración y
                      servicios afectados
                    </li>
                    <li>
                      🆕 <strong>Funcionalidades:</strong> Explica brevemente
                      las nuevas características
                    </li>
                    <li>
                      📞 <strong>Soporte:</strong> Incluye información de
                      contacto técnico
                    </li>
                    <li>
                      ⚠️ <strong>Impacto:</strong> Menciona si habrá
                      interrupciones del servicio
                    </li>
                  </>
                )}
                <li>
                  ✅ Usa las plantillas predefinidas y personalízalas según
                  necesites
                </li>
                <li>
                  🎯 Selecciona el tipo de notificación apropiado (info, éxito,
                  advertencia, error)
                </li>
                <li>
                  ⏰ Considera el horario para envíos masivos (evita horas muy
                  tempranas o tardías)
                </li>
              </ul>
            </Space>
          </Card>
        )}
      </Space>
    </div>
  );
};
