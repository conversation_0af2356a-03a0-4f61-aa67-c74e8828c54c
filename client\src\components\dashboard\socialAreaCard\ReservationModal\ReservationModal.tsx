import {
  MinusCircleOutlined,
  SolutionOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  Flex,
  Form,
  Input,
  Modal,
  Progress,
  Row,
  Select,
  Space,
  Steps,
  TimePicker,
  Typography,
  notification,
} from "antd";
import { AppDate } from "constants/Dates";
import dayjs from "dayjs";
import "dayjs/locale/es";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { useCreateReservation } from "../hooks/useCreateReservation";
import { ReservationData } from "../types/reservation.types";

const { Option } = Select;

const StyledForm = styled(Form)`
  .ant-form-item {
    margin-bottom: 1rem;
  }
`;

// Máximo número de invitados permitido
const MAX_GUESTS = 100;

interface ReservationModalProps {
  open: boolean;
  onCancel: () => void;
  date?: AppDate;
  socialArea?: string;
  isEditing?: boolean;
  initialValues?: {
    reservationDate?: string;
    startTime?: string;
    endTime?: string;
    eventType?: string;
    socialArea?: string;
    guests?: Array<{ name: string; lastName: string }>;
  };
  onEdit?: (values: ReservationData) => void;
}

export const ReservationModal: React.FC<ReservationModalProps> = ({
  open,
  onCancel,
  date,
  socialArea,
  isEditing = false,
  initialValues,
  onEdit,
}) => {
  const [form] = Form.useForm();
  const { mutate: createReservation, isPending } = useCreateReservation();
  const [currentStep, setCurrentStep] = useState(0);
  const [formStepData, setFormStepData] = useState<{
    startTime?: dayjs.Dayjs;
    endTime?: dayjs.Dayjs;
    eventType?: string;
    socialArea?: string;
    customEventType?: string;
  }>({});

  // Campos clave para verificar la validez del primer paso
  const startTime = Form.useWatch("startTime", form);
  const endTime = Form.useWatch("endTime", form);
  const eventType = Form.useWatch("eventType", form);
  const selectedArea = Form.useWatch("socialArea", form);

  const isStep1Valid = startTime && endTime && eventType && selectedArea;

  // Manejar paso siguiente
  const handleNextStep = async () => {
    try {
      // Validar campos del paso actual
      const values = await form.validateFields();

      // Guardar los datos del primer paso
      setFormStepData((prevData) => ({
        ...prevData,
        ...values,
      }));

      setCurrentStep(1);
    } catch (error) {
      notification.error({
        message: "Error",
        description:
          "Error al guardar el paso 1. Por favor, inténtalo de nuevo.",
      });
    }
  };

  // Manejar paso anterior
  const handlePreviousStep = () => {
    // Guardar los datos actuales del paso 2 si hay alguno
    form
      .validateFields(["guests"])
      .then((values) => {
        setFormStepData((prevData) => ({
          ...prevData,
          ...values,
        }));
      })
      .catch((error) => console.error("Error al guardar paso 2:", error));

    setCurrentStep(0);
  };

  // Manejar envío del formulario
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isEditing && onEdit) {
        // Formato para edición
        const editValues = {
          date: date || dayjs(),
          startTime: formStepData.startTime || dayjs(),
          endTime: formStepData.endTime || dayjs(),
          eventType: formStepData.eventType || "",
          socialArea: formStepData.socialArea || "",
          guests: values.guests || [],
        };
        onEdit(editValues);
      } else {
        // Formato para creación
        const createValues = {
          date: date || dayjs(),
          startTime: formStepData.startTime || dayjs(),
          endTime: formStepData.endTime || dayjs(),
          eventType: formStepData.eventType || "",
          socialArea: formStepData.socialArea || "",
          guests: values.guests || [],
        };
        createReservation(createValues, {
          onSuccess: () => {
            onCancel();
            form.resetFields();
          },
        });
      }
    } catch (error) {
      notification.error({
        message: "Error",
        description:
          "Error al crear la reserva. Por favor, inténtalo de nuevo.",
      });
    }
  };

  useEffect(() => {
    if (open) {
      if (isEditing && initialValues) {
        const formValues = {
          date: dayjs(initialValues.reservationDate),
          startTime: dayjs(initialValues.startTime, "HH:mm"),
          endTime: dayjs(initialValues.endTime, "HH:mm"),
          eventType: initialValues.eventType,
          socialArea: initialValues.socialArea,
          guests: initialValues.guests || [],
        };
        form.setFieldsValue(formValues);
        setFormStepData({
          startTime: formValues.startTime,
          endTime: formValues.endTime,
          eventType: formValues.eventType,
          socialArea: formValues.socialArea,
        });
      } else {
        form.resetFields();
        setFormStepData({});
      }
      setCurrentStep(0);
    }
  }, [open, isEditing, initialValues, form]);

  // Renderizar formulario de detalles de reserva (Paso 1)
  const renderDetailsStep = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Form.Item
          name="socialArea"
          label="Zona social solicitada"
          rules={[{ required: true, message: "Este campo es requerido" }]}
        >
          <Select disabled>
            <Option value="communalHall">Salón Social</Option>
          </Select>
        </Form.Item>
      </Col>
      <Col xs={24} md={12}>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.eventType !== currentValues.eventType
          }
        >
          {({ getFieldValue }) => (
            <>
              <Form.Item
                name="eventType"
                label="Tipo de evento"
                rules={[{ required: true, message: "Este campo es requerido" }]}
              >
                <Select>
                  <Option value="birthday">Fiesta de cumpleaños</Option>
                  <Option value="meeting">Reunión familiar</Option>
                  <Option value="celebration">Celebración especial</Option>
                  <Option value="babyShower">Baby Shower</Option>
                  <Option value="graduation">Graduación</Option>
                  <Option value="other">Otro</Option>
                </Select>
              </Form.Item>
              {getFieldValue("eventType") === "other" && (
                <Form.Item
                  name="customEventType"
                  rules={[
                    {
                      required: true,
                      message: "Por favor especifique el tipo de evento",
                    },
                  ]}
                >
                  <Input placeholder="Especifique el tipo de evento" />
                </Form.Item>
              )}
            </>
          )}
        </Form.Item>
      </Col>
      <Col xs={24} md={12}>
        <Form.Item
          name="startTime"
          label="Inicio"
          rules={[{ required: true, message: "Este campo es requerido" }]}
        >
          <TimePicker
            style={{ width: "100%" }}
            format="HH:mm A"
            minuteStep={30}
            placeholder="Hora"
            needConfirm
          />
        </Form.Item>
      </Col>
      <Col xs={24} md={12}>
        <Form.Item
          name="endTime"
          label="Finalización"
          rules={[
            { required: true, message: "Este campo es requerido" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || !getFieldValue("startTime")) {
                  return Promise.resolve();
                }
                const startTime = getFieldValue("startTime");
                if (value.isSame(startTime) || value.isBefore(startTime)) {
                  return Promise.reject(
                    new Error(
                      "La hora de finalización debe ser posterior a la hora de inicio",
                    ),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <TimePicker
            style={{ width: "100%" }}
            format="HH:mm A"
            minuteStep={15}
            placeholder="Hora"
            disabled={!startTime}
            disabledTime={() => {
              if (!startTime) return {};
              const selectedHour = startTime.hour();
              const selectedMinute = startTime.minute();

              return {
                disabledHours: () =>
                  Array.from({ length: selectedHour }, (_, i) => i),
                disabledMinutes: (hour) => {
                  if (hour === selectedHour) {
                    return Array.from(
                      { length: selectedMinute + 1 },
                      (_, i) => i,
                    );
                  }
                  return [];
                },
              };
            }}
          />
        </Form.Item>
      </Col>
    </Row>
  );

  // Renderizar formulario de invitados (Paso 2)
  const renderGuestsStep = () => (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <Form.List
          name="guests"
          rules={[
            {
              validator: async (_, guests) => {
                if (!guests || guests.length < 1) {
                  return Promise.reject(
                    new Error("Debe agregar al menos un invitado"),
                  );
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <Flex gap={16} vertical>
              <Typography.Title level={5} style={{ margin: 0 }}>
                Capacidad de invitados
              </Typography.Title>
              <Progress
                percent={Math.min((fields.length / MAX_GUESTS) * 100, 100)}
                status={fields.length >= MAX_GUESTS ? "exception" : "active"}
                format={() => `${fields.length}/${MAX_GUESTS}`}
              />

              <section
                style={{
                  maxHeight: "300px",
                  overflowY: "auto",
                  paddingRight: "8px",
                }}
              >
                {fields.map(({ key, name, ...restField }) => (
                  <Space.Compact
                    key={key}
                    block
                    style={{ width: "100%" }}
                    spellCheck
                  >
                    <Form.Item
                      style={{ width: "100%" }}
                      {...restField}
                      name={[name, "name"]}
                      rules={[
                        {
                          required: true,
                          message: "Por favor ingrese el nombre",
                        },
                        {
                          min: 2,
                          message: "El nombre debe tener al menos 2 caracteres",
                        },
                      ]}
                    >
                      <Input placeholder="Nombre" />
                    </Form.Item>
                    <Form.Item
                      style={{ width: "100%" }}
                      {...restField}
                      name={[name, "lastName"]}
                      rules={[
                        {
                          required: true,
                          message: "Por favor ingrese el apellido",
                        },
                        {
                          min: 2,
                          message:
                            "El apellido debe tener al menos 2 caracteres",
                        },
                      ]}
                    >
                      <Input placeholder="Apellido" />
                    </Form.Item>
                    <Form.Item>
                      <Button
                        onClick={() => remove(name)}
                        type="dashed"
                        danger
                        shape="circle"
                      >
                        <UserDeleteOutlined />
                      </Button>
                    </Form.Item>
                  </Space.Compact>
                ))}
              </section>
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<UserAddOutlined />}
                  disabled={fields.length >= MAX_GUESTS}
                >
                  {fields.length >= MAX_GUESTS
                    ? "Límite de invitados alcanzado"
                    : "Agregar"}
                </Button>
              </Form.Item>
              <Form.ErrorList errors={errors} />
            </Flex>
          )}
        </Form.List>
      </Col>
    </Row>
  );

  return (
    <Modal
      destroyOnHidden
      centered
      title={
        isEditing
          ? "Editar Reservación"
          : "Nueva Reservación: " + date?.format("DD/MM/YYYY")
      }
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
      maskClosable={false}
      closable
    >
      <StyledForm
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={true}
        initialValues={{
          socialArea: socialArea || "communalHall",
          eventType: "celebration",
          date: date,
        }}
      >
        <Steps
          style={{ marginBottom: 24 }}
          size="small"
          current={currentStep}
          items={[
            {
              title: "Detalles de la reserva",
              icon: <SolutionOutlined />,
            },
            {
              title: "Lista de invitados",
              icon: <UsergroupAddOutlined />,
            },
          ]}
        />

        {currentStep === 0 ? renderDetailsStep() : renderGuestsStep()}

        <Row justify="end" style={{ marginTop: 24 }}>
          {currentStep > 0 && (
            <Button style={{ marginRight: 8 }} onClick={handlePreviousStep}>
              Anterior
            </Button>
          )}

          {currentStep === 0 && (
            <Button
              type="primary"
              disabled={!isStep1Valid}
              onClick={handleNextStep}
            >
              Siguiente
            </Button>
          )}

          {currentStep === 1 && (
            <Button
              type="primary"
              onClick={handleSubmit}
              loading={isEditing ? false : isPending}
            >
              {isEditing ? "Guardar Cambios" : "Reservar"}
            </Button>
          )}
        </Row>
      </StyledForm>
    </Modal>
  );
};
