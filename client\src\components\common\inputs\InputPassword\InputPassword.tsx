import * as S from "./InputPassword.styles";
import { BaseInputProps, BaseInputRef } from "../BaseInput/BaseInput";
import { forwardRef, ReactNode } from "react";

interface InputPasswordProps extends BaseInputProps {
  className?: string;
  visibilityToggle?: boolean;
  iconRender?: (open: boolean) => ReactNode;
}

export const InputPassword = forwardRef<BaseInputRef, InputPasswordProps>(
  ({ className, children, ...props }, ref) => (
    <S.InputPassword ref={ref} className={className} {...props}>
      {children}
    </S.InputPassword>
  ),
);
