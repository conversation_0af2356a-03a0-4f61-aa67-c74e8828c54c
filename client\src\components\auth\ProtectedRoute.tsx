import { Navigate, Outlet, useLocation } from "react-router-dom";
import { Spin } from "antd";
import { useCurrentUser } from "@app/hooks/useCurrentUser";

interface ProtectedRouteProps {
  allowedRoles?: string[];
  redirectPath?: string;
}

/**
 * Componente para proteger rutas según roles de usuario
 * @param allowedRoles - Lista de roles permitidos para acceder a la ruta
 * @param redirectPath - Ruta a la que redirigir si el usuario no tiene permiso
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  allowedRoles = [],
  redirectPath = "/dashboard",
}) => {
  const { user, loading } = useCurrentUser();
  const location = useLocation();

  // Mientras se carga el usuario, mostrar un spinner
  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  // Si no hay usuario autenticado, redirigir al login
  if (!user) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Si no se especifican roles, cualquier usuario autenticado puede acceder
  if (allowedRoles.length === 0) {
    return <Outlet />;
  }

  // Verificar si el usuario tiene alguno de los roles permitidos
  const userRole = user.role?.name || "";
  const hasPermission = allowedRoles.some(
    (role) =>
      role.toLowerCase() === userRole.toLowerCase() ||
      user.role?.type === "admin",
  );

  // Si el usuario tiene un rol permitido, permitir acceso
  if (hasPermission) {
    return <Outlet />;
  }

  // Si el usuario no tiene permiso, redirigir
  return <Navigate to={redirectPath} replace />;
};

export default ProtectedRoute;
