import { useQuery } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { Reservation } from "../types/reservation.types";

export const useGetReservationById = (id: string) => {
  return useQuery<Reservation>({
    queryKey: ["reservations", id],
    queryFn: async () => {
      const response = await Http.get(`/api/reservations/${id}`);
      return response.data;
    },
    enabled: !!id,
  });
};
