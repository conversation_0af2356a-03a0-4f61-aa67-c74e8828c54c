"use strict";
/**
 * pool-access controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::pool-access.pool-access", ({ strapi }) => ({
    async create(ctx) {
        try {
            const { data } = ctx.request.body;
            const { user: userId, guestCount = 0, dependentIds = [] } = data;
            if (typeof guestCount !== 'number' || guestCount < 0 || guestCount > 5) {
                return ctx.badRequest('El número de invitados debe estar entre 0 y 5');
            }
            const entry = (await strapi.entityService.create("api::pool-access.pool-access", {
                data: {
                    user: userId,
                    guestCount,
                    dependents: dependentIds,
                    isAlone: guestCount === 0 && dependentIds.length === 0,
                    status: "active",
                    entryTime: new Date().toISOString(),
                    publishedAt: new Date().toISOString(),
                },
                populate: {
                    user: true,
                    dependents: true,
                },
            }));
            // Formatear la respuesta
            const formattedEntry = {
                ...entry,
                user: entry.user
                    ? {
                        id: entry.user.id,
                        documentId: entry.user.documentId,
                        firstName: entry.user.firstName,
                        lastName: entry.user.lastName,
                        status: entry.user.status,
                        address: entry.user.address,
                    }
                    : null,
            };
            return ctx.send({ data: formattedEntry });
        }
        catch (error) {
            console.error("Error al crear acceso a piscina:", error);
            return ctx.badRequest(error.message);
        }
    },
    async current(ctx) {
        try {
            const entries = (await strapi.entityService.findMany("api::pool-access.pool-access", {
                filters: {
                    status: "active",
                    exitTime: null,
                },
                populate: {
                    user: true,
                    dependents: true,
                },
            }));
            // Formatear la respuesta
            const formattedEntries = entries.map((entry) => ({
                ...entry,
                user: entry.user
                    ? {
                        id: entry.user.id,
                        documentId: entry.user.documentId,
                        firstName: entry.user.firstName,
                        lastName: entry.user.lastName,
                        status: entry.user.status,
                        address: entry.user.address,
                    }
                    : null,
            }));
            const totalPeople = formattedEntries.reduce((total, entry) => {
                var _a;
                return (total +
                    1 + // usuario principal
                    (entry.guestCount || 0) + // invitados
                    (((_a = entry.dependents) === null || _a === void 0 ? void 0 : _a.length) || 0) // dependientes
                );
            }, 0);
            return ctx.send({
                data: formattedEntries,
                meta: {
                    total: entries.length,
                    totalPeople,
                },
            });
        }
        catch (error) {
            console.error("Error al obtener accesos actuales:", error);
            return ctx.badRequest(error.message);
        }
    },
    async find(ctx) {
        try {
            const entries = (await strapi.entityService.findMany("api::pool-access.pool-access", {
                populate: {
                    user: true,
                    dependents: true,
                },
            }));
            // Formatear la respuesta
            const formattedEntries = entries.map((entry) => ({
                ...entry,
                user: entry.user
                    ? {
                        id: entry.user.id,
                        documentId: entry.user.documentId,
                        firstName: entry.user.firstName,
                        lastName: entry.user.lastName,
                        status: entry.user.status,
                        address: entry.user.address,
                    }
                    : null,
            }));
            return ctx.send({
                data: formattedEntries,
            });
        }
        catch (error) {
            console.error("Error en find:", error);
            return ctx.badRequest(error.message);
        }
    },
    async findCurrent(ctx) {
        try {
            const entries = (await strapi.entityService.findMany("api::pool-access.pool-access", {
                filters: {
                    $and: [
                        {
                            status: {
                                $eq: "active",
                            },
                        },
                    ],
                },
                populate: {
                    user: true,
                    dependents: true,
                },
            }));
            // Formatear la respuesta
            const formattedEntries = entries.map((entry) => ({
                ...entry,
                user: entry.user
                    ? {
                        id: entry.user.id,
                        documentId: entry.user.documentId,
                        firstName: entry.user.firstName,
                        lastName: entry.user.lastName,
                        status: entry.user.status,
                        address: entry.user.address,
                    }
                    : null,
            }));
            return ctx.send({
                data: formattedEntries,
            });
        }
        catch (error) {
            console.error("Error en findCurrent:", error);
            return ctx.badRequest(error.message);
        }
    },
    async exit(ctx) {
        try {
            const { id } = ctx.params;
            const entry = (await strapi.entityService.update("api::pool-access.pool-access", id, {
                data: {
                    exitTime: new Date().toISOString(),
                    status: "completed",
                },
                populate: {
                    user: true,
                    dependents: true,
                },
            }));
            // Formatear la respuesta
            const formattedEntry = {
                ...entry,
                user: entry.user
                    ? {
                        id: entry.user.id,
                        documentId: entry.user.documentId,
                        firstName: entry.user.firstName,
                        lastName: entry.user.lastName,
                        status: entry.user.status,
                        address: entry.user.address,
                    }
                    : null,
            };
            return ctx.send({
                data: formattedEntry,
            });
        }
        catch (error) {
            console.error("Error al registrar salida:", error);
            return ctx.badRequest(error.message);
        }
    },
}));
