/**
 * Estilos para el componente ChatHeader
 */

import styled from 'styled-components';

export const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  background-color: ${({ theme }) => theme.background};
  min-height: 60px;
`;

export const ChannelInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
`;

export const ChannelIcon = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const ChannelDetails = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

export const ChannelName = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  
  .ant-typography {
    margin: 0;
    color: ${({ theme }) => theme.textMain};
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

export const ReadOnlyBadge = styled.span`
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const ChannelStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 16px;
`;

export const TypingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 12px;
  font-style: italic;
`;

export const TypingDots = styled.div`
  display: flex;
  gap: 2px;
  
  span {
    width: 3px;
    height: 3px;
    background-color: currentColor;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }

  @keyframes typing {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export const ChannelActions = styled.div`
  display: flex;
  align-items: center;
  flex-shrink: 0;
  
  .ant-btn {
    border: none;
    box-shadow: none;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
    }
  }
`;

export const OnlineUsers = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 12px;
`;

export const ChannelDescription = styled.div`
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
`;

export const LastActivity = styled.div`
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 11px;
  
  &::before {
    content: '• ';
  }
`;

export const ChannelBadges = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const PrivateBadge = styled.span`
  background-color: ${({ theme }) => theme.warning || '#faad14'}20;
  color: ${({ theme }) => theme.warning || '#faad14'};
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const ModeratorBadge = styled.span`
  background-color: ${({ theme }) => theme.primary}20;
  color: ${({ theme }) => theme.primary};
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const ChannelStats = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 11px;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 2px;
  }
`;

export const QuickActions = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  
  .quick-action {
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
    }
  }
`;

export const NotificationBell = styled.div<{ $hasNotifications: boolean }>`
  position: relative;
  cursor: pointer;
  
  ${({ $hasNotifications, theme }) => $hasNotifications && `
    &::after {
      content: '';
      position: absolute;
      top: 2px;
      right: 2px;
      width: 6px;
      height: 6px;
      background-color: ${theme.error || '#ff4d4f'};
      border-radius: 50%;
      border: 1px solid ${theme.background};
    }
  `}
`;

export const ChannelMemberCount = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  border-radius: 10px;
  font-size: 11px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
`;

export const PinnedMessage = styled.div`
  background-color: ${({ theme }) => theme.primary}10;
  border-left: 3px solid ${({ theme }) => theme.primary};
  padding: 6px 12px;
  margin: 0 16px 8px 16px;
  border-radius: 0 4px 4px 0;
  font-size: 12px;
  
  .pinned-label {
    color: ${({ theme }) => theme.primary};
    font-weight: 500;
    margin-bottom: 2px;
  }
  
  .pinned-content {
    color: ${({ theme }) => theme.textMain};
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;
