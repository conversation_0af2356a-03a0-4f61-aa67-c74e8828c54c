/**
 * Script para restaurar el usuario administrador en Strapi
 *
 * Este script ejecuta un archivo SQL que contiene las instrucciones para restaurar
 * el usuario administrador en la base de datos.
 */
const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");
require("dotenv").config();
// Configuración de la base de datos desde variables de entorno
const dbConfig = {
    host: process.env.DATABASE_HOST || "localhost",
    port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    multipleStatements: true // Necesario para ejecutar múltiples statements SQL
};
// Función para mostrar el banner
function mostrarBanner() {
    console.log('\n===========================================================');
    console.log('🔐 RESTAURACIÓN DE ADMIN - PALMAS PRODUCCIÓN 🔐');
    console.log('===========================================================\n');
    console.log('Este script restaurará el usuario administrador en la base de datos.\n');
    console.log('===========================================================\n');
}
/**
 * Función principal para ejecutar la restauración del admin
 */
async function restoreAdmin() {
    let connection;
    try {
        // Leer el archivo SQL
        const sqlFilePath = path.join(__dirname, 'restore-admin.sql');
        const sqlContent = await fs.readFile(sqlFilePath, 'utf8');
        // Conectar a la base de datos
        console.log('\n🔌 Conectando a la base de datos...');
        connection = await mysql.createConnection(dbConfig);
        // Ejecutar las instrucciones SQL
        console.log('\n📝 Ejecutando instrucciones SQL...');
        await connection.query(sqlContent);
        console.log('\n✅ Administrador restaurado exitosamente!');
        console.log('\n🎉 El proceso de restauración ha finalizado correctamente.\n');
        console.log('Credenciales de acceso:');
        console.log('Usuario: <EMAIL>');
        console.log('Contraseña: (la contraseña que tenías configurada)\n');
    }
    catch (error) {
        console.error('\n❌ Error durante el proceso de restauración:', error);
        process.exit(1);
    }
    finally {
        // Cerrar la conexión si existe
        if (connection) {
            await connection.end();
        }
    }
}
// Ejecutar el script
mostrarBanner();
restoreAdmin();
