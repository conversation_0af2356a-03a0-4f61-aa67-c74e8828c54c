import axios from "axios";

const getAuthToken = () => {
  const auth = localStorage.getItem("auth");
  if (!auth) return null;
  try {
    const { jwt } = JSON.parse(auth);
    return jwt;
  } catch {
    return null;
  }
};

export const api = axios.create({
  baseURL: "http://localhost:1337/api",
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor para añadir el token a todas las peticiones
api.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
