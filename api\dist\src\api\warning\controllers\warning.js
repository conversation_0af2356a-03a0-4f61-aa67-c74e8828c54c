"use strict";
/**
 * warning controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
const sanitizeOutput = (entity) => {
    const sanitizedEntity = { ...entity };
    if (sanitizedEntity.resident) {
        sanitizedEntity.resident = {
            id: entity.resident.id,
            firstName: entity.resident.firstName,
            lastName: entity.resident.lastName,
            username: entity.resident.username,
            address: entity.resident.address
        };
    }
    return sanitizedEntity;
};
exports.default = strapi_1.factories.createCoreController('api::warning.warning', ({ strapi }) => ({
    async delete(ctx) {
        const { id } = ctx.params;
        try {
            // Verificar si la advertencia existe
            const warning = await strapi.entityService.findOne('api::warning.warning', id);
            if (!warning) {
                return ctx.notFound('Warning not found');
            }
            // Eliminar la advertencia
            const deletedWarning = await strapi.entityService.delete('api::warning.warning', id);
            // Retornar 204 No Content para indicar éxito sin contenido
            ctx.response.status = 204;
            return;
        }
        catch (error) {
            console.error('Error al eliminar la advertencia:', error);
            return ctx.badRequest('Error al eliminar la advertencia');
        }
    },
    async find(ctx) {
        const { data, meta } = await super.find(ctx);
        const populatedData = await Promise.all(data.map(async (warning) => {
            const entity = await strapi.entityService.findOne('api::warning.warning', warning.id, {
                populate: ['resident', 'rules', 'evidencePhotos', 'notifications', 'sanctionBy']
            });
            return sanitizeOutput(entity);
        }));
        return { data: populatedData, meta };
    },
    async findByResident(ctx) {
        const { id } = ctx.params;
        const warnings = await strapi.entityService.findMany('api::warning.warning', {
            filters: {
                resident: id
            },
            populate: ['resident', 'rules', 'evidencePhotos', 'notifications', 'sanctionBy']
        });
        const sanitizedData = warnings.map(warning => sanitizeOutput(warning));
        return { data: sanitizedData };
    },
    async create(ctx) {
        var _a;
        const { data } = ctx.request.body;
        // Obtener el usuario actual
        const currentUser = ctx.state.user;
        // Asegurarnos que solo pasamos los campos necesarios
        const sanitizedData = {
            date: data.date,
            time: data.time,
            type: data.type,
            description: data.description,
            resident: data.resident,
            rules: data.rules,
            notifications: data.notifications,
            sanctionBy: {
                firstName: currentUser.firstName || '',
                lastName: currentUser.lastName || '',
                role: ((_a = currentUser.role) === null || _a === void 0 ? void 0 : _a.name) || 'authenticated',
                userId: currentUser.id
            }
        };
        const entity = await strapi.entityService.create('api::warning.warning', {
            data: sanitizedData,
            populate: {
                resident: true,
                rules: true,
                evidencePhotos: true,
                notifications: true,
                sanctionBy: true
            }
        });
        // Enviar notificación al residente
        if (entity.resident) {
            const rulesText = entity.rules && entity.rules.length > 0
                ? `Reglas infringidas: ${entity.rules.map(rule => rule.name).join(', ')}`
                : '';
            const message = `Se ha generado una sanción por ${entity.type}. ${rulesText}\nDescripción: ${entity.description}`;
            await strapi.service('api::notification.notification').create({
                data: {
                    title: 'Nueva Sanción',
                    type: 'warning_created',
                    message,
                    user: entity.resident.id,
                    data: {
                        warningId: entity.id,
                        type: entity.type,
                        date: entity.date,
                        time: entity.time
                    },
                },
            });
        }
        return { data: sanitizeOutput(entity) };
    }
}));
