import { BaseButton } from "@app/components/common/BaseButton/BaseButton";
import { BaseSpace } from "@app/components/common/BaseSpace/BaseSpace";
import { CalendarOutlined } from "@ant-design/icons";
import styled from "styled-components";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
`;

const IconWrapper = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
`;

const Text = styled.p`
  font-size: 1rem;
  color: var(--text-main-color);
  margin-bottom: 1.5rem;
`;

export interface EmptyStateProps {
  onReserve: () => void;
  disabled?: boolean;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  onReserve,
  disabled,
}) => {
  return (
    <Container>
      <BaseSpace direction="vertical" align="center">
        <IconWrapper>
          <CalendarOutlined />
        </IconWrapper>
        <Text>
          {disabled
            ? "Este día ya tiene reservaciones"
            : "¿Deseas agendar en este día?"}
        </Text>
        <BaseButton type="primary" onClick={onReserve} disabled={disabled}>
          Reservar
        </BaseButton>
      </BaseSpace>
    </Container>
  );
};
