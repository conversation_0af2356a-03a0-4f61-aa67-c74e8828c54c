import { Navigate, useLocation } from "react-router-dom"; // Agregar esta línea
import { WithChildrenProps } from "@app/types/generalTypes";
import { useCurrentUser } from "@app/hooks/useCurrentUser";
import { useUserRole } from "@app/hooks/useRole";
import { Spin } from "antd";

interface RequireAuthProps extends WithChildrenProps {
  allowedRoles?: string[];
}

const RequireAuth = ({ children, allowedRoles = [] }: RequireAuthProps) => {
  const { user, loading: userLoading } = useCurrentUser();
  const { userRole, isAdmin } = useUserRole();
  const location = useLocation();

  // Mientras se carga el usuario, mostrar un spinner
  if (userLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  // Si no hay usuario autenticado, redirigir al login
  if (!user) {
    // Guardar la ubicación actual para redirigir después del login
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Si no se especifican roles, cualquier usuario autenticado puede acceder
  if (allowedRoles.length === 0) {
    return <>{children}</>;
  }

  // El rol Admin tiene acceso a todo
  if (isAdmin) {
    return <>{children}</>;
  }

  // Verificar si el usuario tiene alguno de los roles permitidos
  const hasPermission = allowedRoles.some(
    (role) => role === userRole || (role === "Authenticated" && userRole),
  );

  // Si el usuario tiene un rol permitido, permitir acceso
  if (hasPermission) {
    return <>{children}</>;
  }

  // Si el usuario no tiene permiso, redirigir a la página principal
  return <Navigate to="/" replace />;
};

export default RequireAuth;
