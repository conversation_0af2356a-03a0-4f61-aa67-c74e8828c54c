{"kind": "collectionType", "collectionName": "complaint_templates", "info": {"singularName": "complaint-template", "pluralName": "complaint-templates", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pre-defined templates for common complaints"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "unique": true, "minLength": 3, "maxLength": 100}, "content": {"type": "text", "required": true, "minLength": 10}, "category": {"type": "enumeration", "enum": ["MANTENIMIENTO", "MUDANZA", "CONVIVENCIA", "SEGURIDAD", "MASCOTAS", "AREAS_COMUNES", "OTROS"], "required": true}, "subcategory": {"type": "string", "required": false}, "requiredFields": {"type": "json", "required": true, "default": ["description"]}, "fields": {"type": "json", "required": true, "default": {"description": {"type": "text", "label": "Descripción", "required": true}}}, "oldCategory": {"type": "enumeration", "enum": ["maintenance", "security", "noise", "pets", "common_areas", "other"], "required": false, "default": "maintenance"}, "isActive": {"type": "boolean", "default": true, "required": true}, "complaints": {"type": "relation", "relation": "oneToMany", "target": "api::complaint.complaint", "mappedBy": "template"}}}