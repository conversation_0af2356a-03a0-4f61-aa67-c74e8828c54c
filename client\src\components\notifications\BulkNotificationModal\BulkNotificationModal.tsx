import { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Radio,
  Space,
  Typography,
  Divider,
  <PERSON><PERSON>,
  But<PERSON>,
} from "antd";
import {
  NotificationOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  SendOutlined,
} from "@ant-design/icons";
import { useSendBulkNotification } from "@app/hooks/notifications/use-send-bulk-notification";
import { useAppSelector } from "@app/hooks/reduxHooks";

const { TextArea } = Input;
const { Title, Text } = Typography;

interface BulkNotificationModalProps {
  open: boolean;
  onClose: () => void;
}

interface FormValues {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  targetType: "all" | "role";
  roleFilter?: string;
}

const notificationTypes = [
  {
    value: "info",
    label: "Información",
    icon: <InfoCircleOutlined />,
    color: "#1890ff",
  },
  {
    value: "success",
    label: "Éxito",
    icon: <CheckCircleOutlined />,
    color: "#52c41a",
  },
  {
    value: "warning",
    label: "Advertencia",
    icon: <WarningOutlined />,
    color: "#faad14",
  },
  {
    value: "error",
    label: "Error",
    icon: <CloseCircleOutlined />,
    color: "#ff4d4f",
  },
];

const roleOptions = [
  { value: "residente", label: "Residentes" },
  { value: "arrendatario", label: "Arrendatarios" },
  { value: "admin", label: "Administradores" },
  { value: "consejero", label: "Consejeros" },
  { value: "vigilante", label: "Vigilantes" },
  { value: "servicio", label: "Personal de Servicio" },
  { value: "convivencia", label: "Comité de Convivencia" },
];

// Plantillas organizadas por roles
const templatesByRole = {
  vigilante: [
    {
      title: "💧 Llegada del Agua",
      message: `¡Buenas noticias! El servicio de agua ha sido restablecido.

• El agua ya está disponible en todo el conjunto
• Pueden abrir las llaves normalmente
• Si persisten problemas, reportar a portería

Gracias por su paciencia.`,
      type: "success" as const,
      category: "Servicios Públicos",
    },
    {
      title: "🚰 Suspensión del Servicio de Agua",
      message: `Informamos que el servicio de agua será suspendido temporalmente.

• Empresa: Aqualia
• Motivo: [Mantenimiento/Reparación/Otro]
• Duración estimada: [TIEMPO]
• Hora de inicio: [HORA]

Recomendamos almacenar agua para uso básico.`,
      type: "warning" as const,
      category: "Servicios Públicos",
    },
    {
      title: "🗑️ Recolección de Basura - DISPONIBLE",
      message: `Ya pueden sacar la basura para recolección.

• Horario: [HORA] hasta [HORA]
• Ubicar en el lugar designado
• Separar residuos según clasificación
• No dejar basura fuera del horario establecido

¡Mantengamos nuestro conjunto limpio!`,
      type: "info" as const,
      category: "Servicios Generales",
    },
    {
      title: "🚫 Recolección de Basura - NO DISPONIBLE",
      message: `Por favor NO sacar basura en este momento.

• El servicio de recolección no está disponible
• Mantener la basura en sus apartamentos
• Próximo horario de recolección: [FECHA Y HORA]

Evitemos acumulación de residuos en áreas comunes.`,
      type: "error" as const,
      category: "Servicios Generales",
    },
    {
      title: "⚡ Personal de CENS en el Conjunto",
      message: `Personal de la empresa eléctrica CENS está realizando trabajos en el conjunto.

• Motivo: [Mantenimiento/Reparación/Lectura]
• Área de trabajo: [UBICACIÓN]
• Tiempo estimado: [DURACIÓN]
• Posibles cortes temporales de energía

Favor facilitar el acceso al personal autorizado.`,
      type: "warning" as const,
      category: "Servicios Públicos",
    },
    {
      title: "🔥 Personal de Gases del Oriente en el Conjunto",
      message: `Personal de Gases del Oriente está en el conjunto realizando trabajos.

• Motivo: [Mantenimiento/Revisión/Reparación]
• Área de trabajo: [UBICACIÓN]
• Tiempo estimado: [DURACIÓN]
• Medidas de seguridad activas

Por seguridad, mantengan ventanas abiertas durante los trabajos.`,
      type: "warning" as const,
      category: "Servicios Públicos",
    },
    {
      title: "💧 Personal de Aqualia en el Conjunto",
      message: `Personal de la empresa de acueducto Aqualia está trabajando en el conjunto.

• Motivo: [Mantenimiento/Reparación/Lectura]
• Área afectada: [UBICACIÓN]
• Tiempo estimado: [DURACIÓN]
• Posible suspensión temporal del agua

Agradecemos su colaboración y paciencia.`,
      type: "info" as const,
      category: "Servicios Públicos",
    },
  ],
  admin: [
    {
      title: "🆕 Nueva Funcionalidad: Control de Privacidad del Perfil",
      message: `Los residentes ahora pueden controlar si su perfil aparece en el directorio público.

• Ve a tu Perfil → Información Personal → Privacidad
• Activa "Público" para aparecer en el directorio
• Por defecto, tu perfil es privado por seguridad

El personal administrativo siempre aparece en el directorio.`,
      type: "info" as const,
      category: "Sistema",
    },
    {
      title: "🔧 Mantenimiento Programado del Sistema",
      message: `Se realizará mantenimiento del sistema el próximo [FECHA] de [HORA] a [HORA].

Durante este tiempo:
• El sistema estará temporalmente no disponible
• Se recomienda guardar cualquier trabajo pendiente
• El servicio se restablecerá automáticamente

Disculpe las molestias ocasionadas.`,
      type: "warning" as const,
      category: "Sistema",
    },
    {
      title: "📊 Actualización del Sistema",
      message: `El sistema ha sido actualizado con nuevas funcionalidades.

Novedades incluidas:
• [FUNCIONALIDAD 1]
• [FUNCIONALIDAD 2]
• Mejoras de rendimiento y seguridad

Para dudas sobre las nuevas funciones, contactar con soporte técnico.`,
      type: "success" as const,
      category: "Sistema",
    },
  ],
  consejero: [
    {
      title: "📋 Convocatoria a Asamblea General",
      message: `Se convoca a todos los propietarios a la Asamblea General Ordinaria.

• Fecha: [FECHA]
• Hora: [HORA]
• Lugar: [UBICACIÓN]
• Orden del día: [TEMAS A TRATAR]

Su participación es fundamental para las decisiones del conjunto.`,
      type: "info" as const,
      category: "Administración",
    },
    {
      title: "💰 Recordatorio de Pago de Administración",
      message: `Recordatorio amigable sobre el pago de administración.

• Fecha límite: [FECHA]
• Valor: $[MONTO]
• Puedes realizar el pago a través de la plataforma
• Para dudas, contacta con administración

¡Gracias por mantener al día tus pagos!`,
      type: "info" as const,
      category: "Administración",
    },
    {
      title: "📝 Nuevas Normas de Convivencia",
      message: `Se han actualizado las normas de convivencia del conjunto.

Principales cambios:
• [NORMA 1]
• [NORMA 2]
• [NORMA 3]

El documento completo está disponible en la administración.`,
      type: "warning" as const,
      category: "Administración",
    },
    {
      title: "🏗️ Trabajos de Mantenimiento en Zonas Comunes",
      message: `Se realizarán trabajos de mantenimiento en las zonas comunes.

• Fechas: Del [FECHA INICIO] al [FECHA FIN]
• Horario: [HORARIO]
• Áreas afectadas: [ZONAS]
• Empresa contratista: [EMPRESA]

Agradecemos su comprensión durante estos trabajos.`,
      type: "warning" as const,
      category: "Mantenimiento",
    },
  ],
  administrador: [
    {
      title: "💰 Estado Financiero del Conjunto",
      message: `Informe financiero del mes de [MES].

• Ingresos: $[MONTO]
• Gastos: $[MONTO]
• Saldo disponible: $[MONTO]
• Cartera morosa: [PORCENTAJE]%

Reporte detallado disponible en administración.`,
      type: "info" as const,
      category: "Finanzas",
    },
    {
      title: "📋 Cambio en Horarios de Administración",
      message: `Informamos cambio en los horarios de atención de la administración.

• Nuevos horarios: [HORARIOS]
• Vigencia: A partir del [FECHA]
• Contacto de emergencias: [TELÉFONO]
• Email: [EMAIL]

Agradecemos su comprensión.`,
      type: "warning" as const,
      category: "Administración",
    },
    {
      title: "🔐 Actualización de Protocolos de Seguridad",
      message: `Se han actualizado los protocolos de seguridad del conjunto.

Nuevas medidas:
• [PROTOCOLO 1]
• [PROTOCOLO 2]
• [PROTOCOLO 3]

Favor acatar las nuevas disposiciones para el bienestar de todos.`,
      type: "warning" as const,
      category: "Seguridad",
    },
  ],
};

export const BulkNotificationModal = ({
  open,
  onClose,
}: BulkNotificationModalProps) => {
  const [form] = Form.useForm<FormValues>();
  const { sendBulkNotification, isLoading } = useSendBulkNotification();
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // Obtener el rol del usuario actual
  const user = useAppSelector((state) => state.user.user);
  const userRole = user?.role?.type?.toLowerCase() || "admin";

  // Mapear roles del sistema a roles de plantillas
  const getRoleForTemplates = (role: string) => {
    switch (role) {
      case "admin":
        return "admin";
      case "vigilante":
        return "vigilante";
      case "consejero":
        return "consejero";
      default:
        return "administrador"; // Para otros roles administrativos
    }
  };

  const templateRole = getRoleForTemplates(userRole);
  const availableTemplates =
    templatesByRole[templateRole] || templatesByRole.admin;

  // Obtener categorías únicas para el rol actual
  const categories = [
    "all",
    ...new Set(availableTemplates.map((t) => t.category)),
  ];

  // Filtrar plantillas por categoría
  const filteredTemplates =
    selectedCategory === "all"
      ? availableTemplates
      : availableTemplates.filter((t) => t.category === selectedCategory);

  const handleSubmit = async (values: FormValues) => {
    const payload = {
      title: values.title,
      message: values.message,
      type: values.type,
      ...(values.targetType === "role" && values.roleFilter
        ? { roleFilter: values.roleFilter }
        : {}),
    };

    sendBulkNotification(payload);
  };

  const handleTemplateSelect = (templateIndex: number) => {
    const template = filteredTemplates[templateIndex];
    setSelectedTemplate(templateIndex);
    form.setFieldsValue({
      title: template.title,
      message: template.message,
      type: template.type,
    });
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSelectedTemplate(null); // Limpiar selección de plantilla al cambiar categoría
  };

  const handleClose = () => {
    form.resetFields();
    setSelectedTemplate(null);
    setSelectedCategory("all");
    onClose();
  };

  return (
    <Modal
      title={
        <Space>
          <NotificationOutlined />
          <span>Enviar Notificación Masiva</span>
        </Space>
      }
      open={open}
      onCancel={handleClose}
      width={700}
      footer={null}
      destroyOnClose
    >
      <Alert
        message="Notificación Masiva"
        description="Esta notificación se enviará a todos los usuarios seleccionados y aparecerá en tiempo real."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* Plantillas Predefinidas */}
      <div style={{ marginBottom: 24 }}>
        <Title level={5}>
          Plantillas Predefinidas para{" "}
          {templateRole === "admin"
            ? "Administrador del Sistema"
            : templateRole === "vigilante"
              ? "Vigilante"
              : templateRole === "consejero"
                ? "Consejero"
                : "Administrador"}
        </Title>

        {/* Selector de Categoría */}
        <div style={{ marginBottom: 16 }}>
          <Text strong>Filtrar por categoría: </Text>
          <Select
            value={selectedCategory}
            onChange={handleCategoryChange}
            style={{ width: 200, marginLeft: 8 }}
            size="small"
          >
            <Select.Option value="all">📋 Todas las categorías</Select.Option>
            {categories.slice(1).map((category) => (
              <Select.Option key={category} value={category}>
                {category === "Servicios Públicos" && "🔧"}
                {category === "Servicios Generales" && "🗑️"}
                {category === "Sistema" && "💻"}
                {category === "Administración" && "📋"}
                {category === "Mantenimiento" && "🏗️"}
                {category === "Finanzas" && "💰"}
                {category === "Seguridad" && "🔐"}
                {" " + category}
              </Select.Option>
            ))}
          </Select>
        </div>

        {/* Plantillas */}
        <Space wrap>
          {filteredTemplates.map((template, index) => (
            <Button
              key={index}
              size="small"
              type={selectedTemplate === index ? "primary" : "default"}
              onClick={() => handleTemplateSelect(index)}
              style={{ marginBottom: 8 }}
            >
              {template.title.length > 35
                ? template.title.substring(0, 35) + "..."
                : template.title}
            </Button>
          ))}
        </Space>

        {filteredTemplates.length === 0 && (
          <Text type="secondary" style={{ fontStyle: "italic" }}>
            No hay plantillas disponibles para esta categoría.
          </Text>
        )}
      </div>

      <Divider />

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          type: "info",
          targetType: "all",
        }}
      >
        <Form.Item
          name="title"
          label="Título de la Notificación"
          rules={[
            { required: true, message: "El título es obligatorio" },
            { max: 100, message: "El título no puede exceder 100 caracteres" },
          ]}
        >
          <Input placeholder="Ej: Nueva funcionalidad disponible" />
        </Form.Item>

        <Form.Item
          name="message"
          label="Mensaje"
          rules={[
            { required: true, message: "El mensaje es obligatorio" },
            {
              max: 1000,
              message: "El mensaje no puede exceder 1000 caracteres",
            },
          ]}
        >
          <TextArea
            rows={6}
            placeholder="Escribe aquí el contenido de la notificación..."
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item
          name="type"
          label="Tipo de Notificación"
          rules={[{ required: true, message: "Selecciona un tipo" }]}
        >
          <Radio.Group>
            <Space direction="vertical">
              {notificationTypes.map((type) => (
                <Radio key={type.value} value={type.value}>
                  <Space>
                    <span style={{ color: type.color }}>{type.icon}</span>
                    <Text>{type.label}</Text>
                  </Space>
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="targetType"
          label="Destinatarios"
          rules={[{ required: true, message: "Selecciona los destinatarios" }]}
        >
          <Radio.Group>
            <Space direction="vertical">
              <Radio value="all">Todos los usuarios</Radio>
              <Radio value="role">Filtrar por rol</Radio>
            </Space>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.targetType !== currentValues.targetType
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("targetType") === "role" ? (
              <Form.Item
                name="roleFilter"
                label="Seleccionar Rol"
                rules={[{ required: true, message: "Selecciona un rol" }]}
              >
                <Select placeholder="Selecciona el rol de los destinatarios">
                  {roleOptions.map((role) => (
                    <Select.Option key={role.value} value={role.value}>
                      {role.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              icon={<SendOutlined />}
            >
              Enviar Notificación
            </Button>
            <Button onClick={handleClose}>Cancelar</Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};
