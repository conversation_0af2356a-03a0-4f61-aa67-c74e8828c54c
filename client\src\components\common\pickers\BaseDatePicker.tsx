import { forwardRef } from "react";
import { DatePickerProps } from "antd";
import * as S from "./BaseDatePicker.styles";

export type BaseDatePickerProps = DatePickerProps;

const DatePicker = forwardRef<any, BaseDatePickerProps>(
  ({ className, ...props }, ref) => (
    <S.DatePicker ref={ref} className={className} {...(props as any)} />
  ),
);

type DatePickerForwardRef = typeof DatePicker;

interface BaseDatePickerInterface extends DatePickerForwardRef {
  RangePicker: typeof S.RangePicker;
  TimePicker: typeof S.TimePicker;
}

export const BaseDatePicker = DatePicker as BaseDatePickerInterface;

BaseDatePicker.RangePicker = S.RangePicker;
BaseDatePicker.TimePicker = S.TimePicker;
