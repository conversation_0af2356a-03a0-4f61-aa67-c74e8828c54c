"use strict";
/**
 * complaint controller · Strapi v5 · con trazas de log
 *
 * – Usa Entity Service con sintaxis `connect` (valor único)
 * – Silencia los falsos positivos de TypeScript cas­teando el payload a `any`
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
/* -------------------------------------------------------------------------- */
/*  Utilidad para limpiar la respuesta antes de enviarla al cliente            */
/* -------------------------------------------------------------------------- */
const sanitize = (row) => {
    var _a, _b, _c;
    const e = row.attributes ? { id: row.id, ...row.attributes } : row;
    if ((_a = e.user) === null || _a === void 0 ? void 0 : _a.id) {
        e.user = {
            id: e.user.id,
            firstName: e.user.firstName,
            lastName: e.user.lastName,
            username: e.user.username,
            address: e.user.address,
            signature: e.user.signature,
            profession: e.user.profession,
        };
    }
    if ((_b = e.respondedBy) === null || _b === void 0 ? void 0 : _b.id) {
        e.respondedBy = {
            id: e.respondedBy.id,
            firstName: e.respondedBy.firstName,
            lastName: e.respondedBy.lastName,
            signature: e.respondedBy.signature,
            profession: e.respondedBy.profession,
        };
    }
    if ((_c = e.template) === null || _c === void 0 ? void 0 : _c.id) {
        e.template = {
            id: e.template.id,
            title: e.template.title,
            category: e.template.category,
            requiredFields: e.template.requiredFields,
            fields: e.template.fields,
        };
    }
    return e;
};
const POP = { user: true, respondedBy: true, template: true };
const legacyCategory = (c) => {
    switch (c) {
        case "MANTENIMIENTO":
            return "maintenance";
        case "SEGURIDAD":
            return "security";
        case "CONVIVENCIA":
            return "common_areas";
        case "OTRO":
            return "other";
        default:
            return "other";
    }
};
exports.default = strapi_1.factories.createCoreController("api::complaint.complaint", ({ strapi }) => ({
    /* ---------------- LIST ---------------- */
    async find(ctx) {
        const rows = await strapi.entityService.findMany("api::complaint.complaint", { populate: POP });
        return { data: rows.map(sanitize) };
    },
    /* ---------------- CREATE --------------- */
    async create(ctx) {
        const { data } = ctx.request.body;
        /* 1. Plantilla ---------------------------------------------------- */
        const tplNumericId = Number(data.template);
        if (!tplNumericId)
            return ctx.badRequest("template is required");
        const tpl = await strapi.entityService.findOne("api::complaint-template.complaint-template", tplNumericId);
        if (!tpl)
            return ctx.badRequest("template not found");
        /* 2. Usuario ------------------------------------------------------ */
        const userRow = await strapi.entityService.findOne("plugin::users-permissions.user", ctx.state.user.id);
        /* 3. Payload ------------------------------------------------------ */
        const payload = {
            data: {
                title: tpl.title,
                description: data.description,
                category: legacyCategory(tpl.category),
                date: data.date,
                time: data.time,
                content: data.content,
                location: data.location,
                template: { connect: tpl.id },
                user: { connect: userRow.id },
                status: "pending",
            },
            populate: POP,
        }; // ← cast a any para que TS no bloquee la llamada
        /* 4. Crear -------------------------------------------------------- */
        try {
            const row = await strapi.entityService.create("api::complaint.complaint", payload);
            strapi.log.info("[complaint] creado id:", row.id);
            // Buscar usuarios con roles Admin y Consejero
            const adminUsers = await strapi.db
                .query("plugin::users-permissions.user")
                .findMany({
                where: {
                    role: {
                        type: {
                            $in: ["admin", "consejero"],
                        },
                    },
                },
                select: ["id"],
            });
            // Notificar a cada admin y consejero
            for (const admin of adminUsers) {
                await strapi.service("api::notification.notification").create({
                    data: {
                        title: "Nueva Solicitud",
                        type: "complaint_created",
                        message: `Se ha registrado una nueva solicitud: ${tpl.title}\nDescripción: ${data.description}`,
                        user: admin.id,
                        data: {
                            complaintId: row.id,
                            category: row.category,
                            date: row.date,
                            time: row.time,
                        },
                    },
                });
            }
            // Notificar al usuario que creó la queja
            await strapi.service("api::notification.notification").create({
                data: {
                    title: "Solicitud Registrada",
                    type: "complaint_created",
                    message: `Tu solicitud ha sido registrada exitosamente y será revisada por el equipo administrativo.\nTítulo: ${tpl.title}`,
                    user: userRow.id,
                    data: {
                        complaintId: row.id,
                        category: row.category,
                        date: row.date,
                        time: row.time,
                    },
                },
            });
            return { data: sanitize(row) };
        }
        catch (error) {
            console.error("[complaint] error al crear:", {
                message: error.message,
                details: error.details,
            });
            return ctx.badRequest(`Error al crear solicitud: ${error.message}`);
        }
    },
    /* ---------------- UPDATE --------------- */
    async update(ctx) {
        const { id } = ctx.params;
        const { data } = ctx.request.body;
        const extras = {};
        if (data.templateId || data.template) {
            const tplNumericId = Number(data.templateId || data.template);
            const tpl = await strapi.entityService.findOne("api::complaint-template.complaint-template", tplNumericId);
            if (!tpl)
                return ctx.badRequest("templateId inválido");
            extras.title = tpl.title;
            extras.category = legacyCategory(tpl.category);
            extras.template = { connect: tpl.id };
        }
        const row = await strapi.entityService.update("api::complaint.complaint", id, { data: { ...data, ...extras }, populate: POP } // cast
        );
        return { data: sanitize(row) };
    },
    /* ---------------- RESPOND -------------- */
    async respond(ctx) {
        var _a;
        const { id } = ctx.params;
        const { response, status } = ctx.request.body;
        // Obtener el usuario que responde
        const userRow = await strapi.entityService.findOne("plugin::users-permissions.user", ctx.state.user.id);
        try {
            // Actualizar la queja
            const row = await strapi.entityService.update("api::complaint.complaint", id, {
                data: {
                    response,
                    status,
                    respondedBy: { connect: userRow.id },
                    respondedAt: new Date().toISOString(),
                },
                populate: POP,
            } // cast
            );
            // Obtener la queja completa con sus relaciones
            const complaint = (await strapi.entityService.findOne("api::complaint.complaint", id, { populate: { user: true, template: true } })); // Necesario para acceder a las relaciones
            if (!((_a = complaint === null || complaint === void 0 ? void 0 : complaint.user) === null || _a === void 0 ? void 0 : _a.id)) {
                throw new Error("No se pudo obtener el usuario de la solicitud");
            }
            // Notificar al usuario que creó la queja
            await strapi.service("api::notification.notification").create({
                data: {
                    title: "Solicitud Actualizada",
                    type: "complaint_responded",
                    message: `Tu solicitud "${complaint.title}" ha sido ${status === "resolved" ? "resuelta" : "actualizada"}.
Respuesta: ${response}`,
                    user: complaint.user.id,
                    data: {
                        complaintId: complaint.id,
                        status,
                        respondedBy: userRow.id,
                        respondedAt: new Date().toISOString(),
                    },
                },
            });
            return { data: sanitize(row) };
        }
        catch (error) {
            console.error("[complaint] error al responder:", {
                message: error.message,
                details: error.details,
            });
            return ctx.badRequest(`Error al responder solicitud: ${error.message}`);
        }
    },
    /* -------- LISTAR POR USUARIO ---------- */
    async findByUser(ctx) {
        const pathUserId = ctx.params.id;
        strapi.log.debug("[complaint] findByUser id:", pathUserId);
        const rows = await strapi.entityService.findMany("api::complaint.complaint", { filters: { user: pathUserId }, populate: POP });
        return { data: rows.map(sanitize) };
    },
}));
