import { InputNumberProps as AntInputNumberProps } from "antd";
import * as S from "./InputNumber.styles";
import { forwardRef } from "react";

export interface InputNumberProps extends AntInputNumberProps {
  block?: boolean;
}

export const InputNumber = forwardRef<HTMLInputElement, InputNumberProps>(
  ({ children, block, ...props }, ref) => (
    <S.InputNumber ref={ref} $block={block} {...props}>
      {children}
    </S.InputNumber>
  ),
);
