'use strict';
/**
 * Configuración de permisos para el módulo de activity-log
 */
module.exports = {
    controllers: {
        'activity-log': {
            findByUser: {
                auth: {
                    scope: ['api::activity-log.findByUser'],
                },
            },
            findMyTenantsActivities: {
                auth: {
                    scope: ['api::activity-log.findMyTenantsActivities'],
                },
            },
            getActivitySummary: {
                auth: {
                    scope: ['api::activity-log.getActivitySummary'],
                },
            },
        },
    },
};
