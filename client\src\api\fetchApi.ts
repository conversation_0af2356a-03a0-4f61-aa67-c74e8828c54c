import { httpApi } from "@app/api/http.api";
import { readToken } from "@app/services/localStorage.service";
import { AxiosRequestConfig } from "axios";

interface FetchOptions extends Omit<RequestInit, 'headers'> {
  token?: string;
  headers?: Record<string, string>;
}

export const fetchApi = async (url: string, options: FetchOptions = {}) => {
  const { token, headers = {}, ...rest } = options;
  
  // Si no se proporciona un token específico, intentamos obtenerlo del localStorage
  const authToken = token || readToken();
  
  // Crear configuración para axios
  const config: AxiosRequestConfig = {
    url,
    method: rest.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };
  
  // Añadir el token de autenticación si existe
  if (authToken) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${authToken}`
    };
  }
  
  // Añadir el body si existe
  if (rest.body) {
    config.data = rest.body;
  }
  
  try {
    // Usar httpApi para mantener consistencia con el resto de la aplicación
    const response = await httpApi.request(config);
    
    return response.data;
  } catch (error) {
    console.error("Error en fetchApi:", error);
    throw error;
  }
};
