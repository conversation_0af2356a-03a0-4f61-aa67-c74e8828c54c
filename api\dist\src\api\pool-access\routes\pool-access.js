"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: "GET",
            path: "/pool-accesses",
            handler: "pool-access.find",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/pool-accesses/current",
            handler: "pool-access.findCurrent",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/pool-accesses",
            handler: "pool-access.create",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/pool-accesses/:id/exit",
            handler: "pool-access.exit",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
    ],
};
