'use strict';
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const axios = require('axios');
// Cargar variables de entorno
dotenv.config();
// Configuración
const API_URL = process.env.API_URL || 'http://localhost:1337';
const TEMPLATES_DIR = path.join(process.cwd(), 'config', 'email-templates');
// Función para leer el contenido de una plantilla HTML
const readTemplateContent = (filePath) => {
    try {
        return fs.readFileSync(filePath, 'utf8');
    }
    catch (error) {
        console.error(`Error al leer la plantilla ${filePath}:`, error);
        return '';
    }
};
// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent) => {
    // Extraer solo el contenido relevante para el mensaje
    const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
        // Simplificar el contenido para la interfaz de Strapi
        let content = bodyMatch[1].trim();
        // Reemplazar las variables de plantilla al formato que espera Strapi
        content = content.replace(/<%=\s*user\.username\s*%>/g, "<%= user.username %>");
        content = content.replace(/<%=\s*URL\s*%>/g, "<%= URL %>");
        content = content.replace(/<%=\s*TOKEN\s*%>/g, "<%= TOKEN %>");
        return content;
    }
    return '';
};
// Función principal
const updateTemplates = async () => {
    try {
        console.log('🔄 INICIANDO: Actualización de plantillas en la interfaz de Strapi');
        // Verificar que las plantillas existan
        const resetPasswordPath = path.join(TEMPLATES_DIR, 'reset-password.html');
        const emailConfirmationPath = path.join(TEMPLATES_DIR, 'email-confirmation.html');
        if (!fs.existsSync(resetPasswordPath) || !fs.existsSync(emailConfirmationPath)) {
            console.error('❌ Error: Las plantillas HTML no existen en el directorio config/email-templates/');
            console.log('📋 Ejecuta primero el script setup-email-templates.js para crear las plantillas');
            return;
        }
        // Leer las plantillas
        const resetPasswordHtml = readTemplateContent(resetPasswordPath);
        const emailConfirmationHtml = readTemplateContent(emailConfirmationPath);
        // Extraer el contenido del mensaje
        const resetPasswordMessage = extractMessageBody(resetPasswordHtml);
        const emailConfirmationMessage = extractMessageBody(emailConfirmationHtml);
        console.log('📝 Contenido extraído de las plantillas HTML:');
        console.log('\nRestablecimiento de contraseña:');
        console.log(resetPasswordMessage);
        console.log('\nConfirmación de correo:');
        console.log(emailConfirmationMessage);
        console.log('\n✅ Para actualizar las plantillas en la interfaz de Strapi:');
        console.log('1. Ve a la sección "Configuración" > "Plantillas de email" en el panel de administración');
        console.log('2. Haz clic en "Resetear contraseña" y copia el contenido del mensaje mostrado arriba');
        console.log('3. Haz clic en "Confirmación de dirección de correo electrónico" y copia el contenido del mensaje mostrado arriba');
        console.log('4. Guarda los cambios en cada plantilla');
    }
    catch (error) {
        console.error('❌ ERROR: Fallo en la extracción de plantillas');
        console.error('🔧 Detalles del error:', error.message);
        console.error('📌 Stack:', error.stack);
    }
};
// Ejecutar la función principal
updateTemplates();
