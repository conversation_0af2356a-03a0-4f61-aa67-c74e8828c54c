export interface Person {
  id: string;
  name: string;
  phone: string;
  avatarUrl: string;
  profession?: string;
}

import V1 from "assets/images/vigilances/VG1.png";
import V2 from "assets/images/vigilances/VG2.png";
import V3 from "assets/images/vigilances/VG3.png";
import V4 from "assets/images/vigilances/VG4.png";
import V5 from "assets/images/vigilances/VG5.png";

export const getOperativePersonsData = (): Promise<Person[]> => {
  return new Promise((res) => {
    setTimeout(() => {
      res([
        {
          id: "1",
          name: "<PERSON>",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: V1,
          profession: "Vigilante",
        },
        {
          id: "2",
          name: "<PERSON>",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: V2,
          profession: "Vigilante",
        },
        {
          id: "3",
          name: "<PERSON>",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: V3,
          profession: "Vigilante",
        },
        {
          id: "4",
          name: "<PERSON>",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: V4,
          profession: "Vigilante",
        },
        {
          id: "5",
          name: "<PERSON> Martínez",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: V5,
          profession: "Vigilante",
        },
        {
          id: "6",
          name: "Miguel Ramírez",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: "https://i.pravatar.cc/600?u=<EMAIL>",
          profession: "Contador",
        },
        {
          id: "7",
          name: "Gabriel Sánchez",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: "https://i.pravatar.cc/600?u=<EMAIL>",
          profession: "Piscinero",
        },
        {
          id: "8",
          name: "Javier Vargas",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: "https://i.pravatar.cc/600?u=<EMAIL>",
          profession: "Jardinero",
        },
        {
          id: "9",
          name: "Luisa Garcia",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: "https://i.pravatar.cc/600?u=<EMAIL>",
          profession: "Salvavidas",
        },
        {
          id: "10",
          name: "Maria Garcia",
          phone: "+57-XXX-XXX-XXXX",
          avatarUrl: "https://i.pravatar.cc/600?u=<EMAIL>",
          profession: "Salvavidas",
        },
      ]);
    }, 0);
  });
};
