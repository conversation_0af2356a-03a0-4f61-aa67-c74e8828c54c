"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: 'GET',
            path: '/warnings/resident/:id',
            handler: 'warning.findByResident',
            config: {
                policies: [],
                description: 'Obtener advertencias de un residente específico',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'findByResident'
                }
            }
        },
        {
            method: 'GET',
            path: '/warnings',
            handler: 'warning.find',
            config: {
                policies: [],
                description: 'Obtener todas las advertencias',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'find'
                }
            }
        },
        {
            method: 'GET',
            path: '/warnings/:id',
            handler: 'warning.findOne',
            config: {
                policies: [],
                description: 'Obtener una advertencia específica',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'findOne'
                }
            }
        },
        {
            method: 'POST',
            path: '/warnings',
            handler: 'warning.create',
            config: {
                policies: ['api::warning.is-admin'],
                description: 'Crear una nueva advertencia',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'create'
                }
            }
        },
        {
            method: 'PUT',
            path: '/warnings/:id',
            handler: 'warning.update',
            config: {
                policies: [],
                description: 'Actualizar una advertencia existente',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'update'
                }
            }
        },
        {
            method: 'DELETE',
            path: '/warnings/:id',
            handler: 'warning.delete',
            config: {
                policies: [],
                description: 'Eliminar una advertencia',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'delete'
                }
            }
        },
        {
            method: 'GET',
            path: '/warnings/resident/:id',
            handler: 'warning.findByResident',
            config: {
                policies: [],
                description: 'Obtener advertencias de un residente específico',
                tag: {
                    plugin: 'warning',
                    name: 'Warning',
                    actionType: 'findByResident'
                }
            }
        }
    ]
};
