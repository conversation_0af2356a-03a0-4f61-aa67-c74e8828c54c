"use strict";
/**
 * setting router
 */
const { createCoreRouter } = require("@strapi/strapi").factories;
module.exports = createCoreRouter("api::setting.setting", {
    config: {
        find: {
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        update: {
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
    },
});
