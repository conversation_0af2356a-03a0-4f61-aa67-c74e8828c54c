import axios, { AxiosError } from "axios";
import { UserModel } from "@app/domain/UserModel";

// Utiliza la variable de entorno
const API_URL = import.meta.env.VITE_BASE_URL;

export interface AuthData {
  email: string;
  password: string;
}

export interface SignUpRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  termOfUse: boolean;
  username: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface SecurityCodePayload {
  code: string;
}

export interface NewPasswordData {
  password: string;
  passwordConfirmation: string;
  code: string | null;
}

export interface LoginRequest {
  identifier: string;
  password: string;
}

export interface LoginResponse {
  jwt: string;
  user: UserModel;
}

// Login
export const login = (loginPayload: LoginRequest): Promise<LoginResponse> =>
  axios
    .post<LoginResponse>(`${API_URL}/api/auth/local`, loginPayload)
    .then(({ data }) => data);

// Sign up
export const signUp = async (
  signUpData: SignUpRequest,
): Promise<LoginResponse> => {
  const { termOfUse, confirmPassword, ...userData } = signUpData;

  try {
    const response = await axios.post<LoginResponse>(
      `${API_URL}/api/auth/local/register`,
      userData,
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError && error.response) {
      console.error("[DEBUG] Registration error:", error.response.data);
    } else {
      console.error("[DEBUG] Unexpected error:", error);
    }
    throw error;
  }
};

// Reset password
export const resetPassword = (
  resetPasswordPayload: ResetPasswordRequest,
): Promise<undefined> =>
  axios
    .post<undefined>(`${API_URL}/api/auth/forgot-password`, {
      email: resetPasswordPayload.email,
    })
    .then(({ data }) => data);

// Verify security code (custom endpoint)
export const verifySecurityCode = (
  securityCodePayload: SecurityCodePayload,
): Promise<undefined> =>
  axios
    .post<undefined>(`${API_URL}/api/auth/verify-code`, {
      code: securityCodePayload.code,
    })
    .then(({ data }) => data);

// Set new password
export const setNewPassword = (
  newPasswordData: NewPasswordData,
): Promise<undefined> =>
  axios
    .post<undefined>(`${API_URL}/api/auth/reset-password`, {
      ...newPasswordData,
    })
    .then(({ data }) => data);
