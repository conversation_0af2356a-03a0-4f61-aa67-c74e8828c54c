import { httpApi } from '@app/api/http.api';
import { Payment, PaymentComment } from '@app/pages/AdminPages/PaymentAdminPage/types/payment.types';

interface PaymentStats {
  totalAmount: number;
  pendingAmount: number;
  verifiedAmount: number;
  rejectedAmount: number;
  totalPayments: number;
  pendingPayments: number;
  verifiedPayments: number;
  rejectedPayments: number;
}

export class PaymentsService {
  static async getPayments(): Promise<Payment[]> {
    return httpApi.get<Payment[]>('/api/payments').then(({ data }) => data);
  }

  static async getPaymentStats(): Promise<PaymentStats> {
    return httpApi.get<PaymentStats>('/api/payments/stats').then(({ data }) => data);
  }

  static async verifyPayment(id: number): Promise<Payment> {
    return httpApi.patch<Payment>(`/api/payments/${id}/verify`).then(({ data }) => data);
  }

  static async rejectPayment(id: number, reason: string): Promise<Payment> {
    return httpApi
      .patch<Payment>(`/api/payments/${id}/reject`, { reason })
      .then(({ data }) => data);
  }

  static async getPaymentsByOwner(ownerId: number): Promise<Payment[]> {
    return httpApi.get<Payment[]>(`/api/payments/owner/${ownerId}`).then(({ data }) => data);
  }

  static async addComment(paymentId: number, content: string): Promise<PaymentComment> {
    return httpApi
      .post<PaymentComment>(`/api/payments/${paymentId}/comments`, { content })
      .then(({ data }) => data);
  }

  static async getComments(paymentId: number): Promise<PaymentComment[]> {
    return httpApi
      .get<PaymentComment[]>(`/api/payments/${paymentId}/comments`)
      .then(({ data }) => data);
  }
}
