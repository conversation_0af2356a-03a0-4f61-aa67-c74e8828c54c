/**
 * Hook para manejar canales de chat con React Query
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Http from "@app/config/http/http";
import type { ChatChannel, ChatChannelsResponse } from "@app/types/chat";

// Query keys para React Query
export const CHAT_QUERY_KEYS = {
  channels: ["chat", "channels"] as const,
  channel: (id: number) => ["chat", "channels", id] as const,
  messages: (channelId: number) => ["chat", "messages", channelId] as const,
  users: ["chat", "users"] as const,
} as const;

/**
 * Hook para obtener todos los canales de chat
 */
export const useChatChannels = () => {
  return useQuery({
    queryKey: CHAT_QUERY_KEYS.channels,
    queryFn: async (): Promise<ChatChannel[]> => {
      const response =
        await Http.get<ChatChannelsResponse>("/api/chat-channels");
      return response.data.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook para obtener un canal específico con sus mensajes
 */
export const useChatChannel = (channelId: number, page = 1, pageSize = 50) => {
  return useQuery({
    queryKey: CHAT_QUERY_KEYS.channel(channelId),
    queryFn: async () => {
      const response = await Http.get(`/api/chat-channels/${channelId}`, {
        params: { page, pageSize },
      });
      return response.data;
    },
    enabled: !!channelId,
    staleTime: 2 * 60 * 1000, // 2 minutos
    retry: 2,
  });
};

/**
 * Hook para crear un nuevo canal
 */
export const useCreateChatChannel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (channelData: Partial<ChatChannel>) => {
      const response = await Http.post("/api/chat-channels", {
        data: channelData,
      });
      return response.data;
    },
    onSuccess: () => {
      // Invalidar la lista de canales para refrescar
      queryClient.invalidateQueries({ queryKey: CHAT_QUERY_KEYS.channels });
    },
    onError: (error) => {
      console.error("Error creando canal:", error);
    },
  });
};

/**
 * Hook para actualizar un canal
 */
export const useUpdateChatChannel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      channelId,
      data,
    }: {
      channelId: number;
      data: Partial<ChatChannel>;
    }) => {
      const response = await Http.put(`/api/chat-channels/${channelId}`, {
        data,
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: CHAT_QUERY_KEYS.channels });
      queryClient.invalidateQueries({
        queryKey: CHAT_QUERY_KEYS.channel(variables.channelId),
      });
    },
  });
};

/**
 * Hook para eliminar un canal
 */
export const useDeleteChatChannel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (channelId: number) => {
      const response = await Http.delete(`/api/chat-channels/${channelId}`);
      return response.data;
    },
    onSuccess: (data, channelId) => {
      // Remover del cache
      queryClient.removeQueries({
        queryKey: CHAT_QUERY_KEYS.channel(channelId),
      });
      queryClient.invalidateQueries({ queryKey: CHAT_QUERY_KEYS.channels });
    },
  });
};

/**
 * Hook para buscar usuarios para menciones
 */
export const useChatUsers = (query: string) => {
  return useQuery({
    queryKey: [...CHAT_QUERY_KEYS.users, query],
    queryFn: async () => {
      if (!query || query.length < 2) return [];

      const response = await Http.get("/api/users", {
        params: {
          "filters[username][$containsi]": query,
          "pagination[limit]": 10,
          populate: "role",
        },
      });

      return (
        response.data.data?.map((user: any) => ({
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          displayName:
            user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.username,
        })) || []
      );
    },
    enabled: query.length >= 2,
    staleTime: 30 * 1000, // 30 segundos
    retry: 1,
  });
};
