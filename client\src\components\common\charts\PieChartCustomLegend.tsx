import { <PERSON><PERSON><PERSON> } from "./PieChart";
import { Legend, LegendItem } from "./Legend/Legend";
import { useCallback, useState } from "react";
import { EChartsOption } from "echarts-for-react";

interface PieChartCustomLegend {
  name: string;
  chartData: any[];
  legendData: LegendItem[];
  height?: string;
  width?: string;
  option?: EChartsOption;
}

export const PieChartCustomLegend: React.FC<PieChartCustomLegend> = ({
  chartData,
  name,
  legendData,
  height,
  width,
  ...rest
}) => {
  const [activeItemIndex, setActiveItemIndex] = useState<number | null>(null);

  const onMouseOver = useCallback(
    ({ dataIndex }: { dataIndex: number | null }) =>
      setActiveItemIndex(dataIndex),
    [setActiveItemIndex],
  );
  const onMouseOut = useCallback(
    () => setActiveItemIndex(null),
    [setActiveItemIndex],
  );

  const onEvents = {
    mouseover: onMouseOver,
    mouseout: onMouseOut,
  };

  return (
    <>
      <PieChart
        data={chartData}
        name={name}
        showLegend={false}
        height={height}
        width={width}
        onEvents={onEvents}
        {...rest}
      />
      <Legend legendItems={legendData} activeItemIndex={activeItemIndex} />
    </>
  );
};
