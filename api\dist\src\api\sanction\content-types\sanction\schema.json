{"kind": "collectionType", "collectionName": "sanctions", "info": {"singularName": "sanction", "pluralName": "sanctions", "displayName": "Sanction", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"reason": {"type": "text", "required": true, "maxLength": 250}, "evidenceImgOne": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "evidenceImgTwo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "sanctionsUser": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "sanctions"}, "reportBy": {"required": true, "type": "integer"}, "petId": {"type": "integer", "required": true}, "ownerPetId": {"type": "integer", "required": true}}}