"use strict";
/**
 * push-subscription service
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreService('api::push-subscription.push-subscription', ({ strapi }) => ({
    async findByEndpoint(endpoint) {
        return await strapi.db.query('api::push-subscription.push-subscription').findOne({
            where: { endpoint }
        });
    },
    async findByUser(userId) {
        return await strapi.db.query('api::push-subscription.push-subscription').findOne({
            where: { user: userId }
        });
    },
    async createOrUpdate(data) {
        try {
            const existingSubscription = await this.findByUser(data.user);
            if (existingSubscription) {
                return await strapi.entityService.update('api::push-subscription.push-subscription', existingSubscription.id, {
                    data: {
                        endpoint: data.endpoint,
                        keys: data.keys,
                        expirationTime: data.expirationTime
                    }
                });
            }
            return await strapi.entityService.create('api::push-subscription.push-subscription', {
                data: {
                    ...data,
                    publishedAt: new Date()
                }
            });
        }
        catch (error) {
            console.error('Error en createOrUpdate:', error);
            throw error;
        }
    }
}));
