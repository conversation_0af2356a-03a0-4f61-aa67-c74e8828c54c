"use strict";
/**
 * pool-sanction controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController('api::pool-sanction.pool-sanction', ({ strapi }) => ({
    async create(ctx) {
        try {
            const { user } = ctx.state;
            const { userId, reason } = ctx.request.body.data;
            // Por defecto, la sanción dura 30 días
            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + 30);
            const entry = await strapi.entityService.create('api::pool-sanction.pool-sanction', {
                data: {
                    user: userId,
                    reason,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString(),
                    status: 'active',
                    createdBy: user.id,
                },
            });
            return ctx.send(entry);
        }
        catch (error) {
            console.error("Error al crear sanción:", error);
            return ctx.badRequest({
                error: error.message + " Error al crear sanción",
            });
        }
    },
    async findActive(ctx) {
        try {
            const entries = await strapi.db.query('api::pool-sanction.pool-sanction').findMany({
                where: {
                    status: 'active',
                    endDate: {
                        $gt: new Date().toISOString(),
                    },
                },
                populate: {
                    user: {
                        fields: ["firstName", "lastName", "id", "address"],
                    },
                    createdBy: {
                        fields: ["firstName", "lastName", "id"],
                    },
                },
            });
            // Transformar los datos al formato esperado por el frontend
            const processedEntries = entries.map((entry) => {
                var _a, _b, _c, _d, _e, _f, _g;
                return ({
                    id: entry.id,
                    reason: entry.reason,
                    startDate: entry.startDate,
                    endDate: entry.endDate,
                    userName: `${(_a = entry.user) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = entry.user) === null || _b === void 0 ? void 0 : _b.lastName}`,
                    userAddress: (_c = entry.user) === null || _c === void 0 ? void 0 : _c.address,
                    userId: (_d = entry.user) === null || _d === void 0 ? void 0 : _d.id,
                    createdByName: `${(_e = entry.createdBy) === null || _e === void 0 ? void 0 : _e.firstName} ${(_f = entry.createdBy) === null || _f === void 0 ? void 0 : _f.lastName}`,
                    createdById: (_g = entry.createdBy) === null || _g === void 0 ? void 0 : _g.id,
                });
            });
            return ctx.send(processedEntries);
        }
        catch (error) {
            console.error("Error en findActive:", error);
            ctx.throw(500, error);
        }
    },
    async complete(ctx) {
        try {
            const { id } = ctx.params;
            const entry = await strapi.entityService.update('api::pool-sanction.pool-sanction', id, {
                data: {
                    status: 'completed',
                    endDate: new Date().toISOString(),
                },
            });
            return ctx.send(entry);
        }
        catch (error) {
            console.error("Error al completar sanción:", error);
            return ctx.badRequest({
                error: error.message + " Error al completar sanción",
            });
        }
    },
}));
