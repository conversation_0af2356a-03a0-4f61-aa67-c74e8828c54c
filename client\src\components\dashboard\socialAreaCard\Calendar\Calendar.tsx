import { Calendar as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { AppDate } from "constants/Dates";
import type { Reservation } from "../types/reservation.types";

const { useBreakpoint } = Grid;

interface CalendarProps {
  reservations: Reservation[];
  date?: AppDate;
  onSelect: (date: AppDate) => void;
}

export const Calendar = ({
  reservations = [],
  date,
  onSelect,
}: CalendarProps) => {
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  const getAreaName = (area: string) => {
    const areaNames: Record<string, string> = {
      communalHall: "Salón comunal",
      bbq: "BBQ",
    };
    return areaNames[area as keyof typeof areaNames] || area || "N/A";
  };

  const eventsFor = (current: AppDate) =>
    reservations.filter(
      (ev) =>
        current.isSame(ev.attributes.date, "date") &&
        (ev.attributes.status === "approved" ||
          ev.attributes.status === "pending"),
    );

  const disabledDate = (current: Dayjs) => current.isBefore(dayjs(), "day");

  const handleSelect = (newDate: Dayjs, info: { source: string }) => {
    if (info.source === "date") onSelect(newDate as AppDate);
  };

  // Solo render de eventos en el interior de la celda
  const dateCellRender = (current: AppDate) => {
    const events = eventsFor(current);
    if (!events.length) return null;

    // Contenedor de badges / títulos
    return (
      <div
        style={{
          marginTop: 4,
          textAlign: "center",
          padding: isMobile ? "0" : "4px",
          backgroundColor: isMobile ? "transparent" : "#f6ffed",
          borderRadius: isMobile ? 0 : 4,
        }}
      >
        {events.map((ev) => {
          const status =
            ev.attributes.status === "approved"
              ? "success"
              : ev.attributes.status === "pending"
                ? "warning"
                : "error";
          return (
            <div
              key={ev.id}
              style={{
                display: "inline-block",
                margin: isMobile ? "0 2px" : "2px 0",
              }}
            >
              <Badge
                status={status}
                text={
                  isMobile ? undefined : getAreaName(ev.attributes.socialArea)
                }
              />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div
      style={{
        width: "100%",
        overflowX: isMobile ? "auto" : "visible",
        padding: isMobile ? "0 8px" : undefined,
      }}
    >
      <AntCalendar
        value={date}
        onSelect={handleSelect}
        fullscreen={!isMobile}
        disabledDate={disabledDate}
        style={isMobile ? { minWidth: 320 } : undefined}
        cellRender={dateCellRender}
      />
    </div>
  );
};
