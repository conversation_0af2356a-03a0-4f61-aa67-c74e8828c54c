import { useQuery } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { Reservation } from "../types/reservation.types";
import dayjs from "dayjs";

export const useListReservations = () => {
  return useQuery<Reservation[]>({
    queryKey: ["all-reservations"],
    queryFn: async () => {
      try {
        const response = await Http.get("/api/reservations/all");
        
        if (!response.data || !response.data.data) {
          console.warn("No se recibieron datos de reservaciones");
          return [];
        }

        // Asegurarnos de que cada reservación tenga el formato correcto y convertir la fecha a dayjs
        return response.data.data.map((item: any) => ({
          id: Number(item.id),
          attributes: {
            ...item.attributes,
            // Convertir la fecha string a objeto dayjs
            date: dayjs(item.attributes.date),
            owner: item.attributes.owner || {
              data: {
                id: 0,
                attributes: {
                  firstName: "",
                  lastName: "",
                  address: "",
                },
              },
            },
          },
        })).sort((a: Reservation, b: Reservation) => {
          return dayjs(b.attributes.date).valueOf() - dayjs(a.attributes.date).valueOf();
        });
      } catch (error) {
        console.error("Error al obtener reservaciones:", error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutos de caché
    refetchInterval: 1000 * 60 * 5, // Refrescar cada 5 minutos
  });
};
