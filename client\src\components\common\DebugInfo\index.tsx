import { useState } from "react";
import { <PERSON>, <PERSON>po<PERSON>, Button, Collapse, Tag } from "antd";
import { BugOutlined, DownOutlined, RightOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface DebugInfoProps {
  data: any;
  title?: string;
}

/**
 * Componente para mostrar información de depuración
 * Solo visible cuando se activa explícitamente
 */
const DebugInfo: React.FC<DebugInfoProps> = ({
  data,
  title = "Información de depuración",
}) => {
  const [expanded, setExpanded] = useState(false);

  // En producción, este componente podría ocultarse mediante
  // configuración en lugar de usar process.env
  // const isDevelopment = process.env.NODE_ENV === 'development';

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  // Función para formatear el objeto JSON
  const formatData = (data: any): JSX.Element => {
    if (data === null || data === undefined) {
      return <Text type="secondary">null</Text>;
    }

    if (typeof data === "object" && !Array.isArray(data)) {
      return (
        <div style={{ marginLeft: 20 }}>
          {Object.entries(data).map(([key, value]) => (
            <div key={key} style={{ marginBottom: 8 }}>
              <Text strong>{key}: </Text>
              {formatData(value)}
            </div>
          ))}
        </div>
      );
    }

    if (Array.isArray(data)) {
      return (
        <div style={{ marginLeft: 20 }}>
          {data.length === 0 ? (
            <Text type="secondary">[]</Text>
          ) : (
            data.map((item, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <Text strong>[{index}]: </Text>
                {formatData(item)}
              </div>
            ))
          )}
        </div>
      );
    }

    // Para valores primitivos
    return <Text>{String(data)}</Text>;
  };

  return (
    <Card
      size="small"
      style={{
        marginBottom: 16,
        border: "1px dashed #d9d9d9",
        backgroundColor: "#fafafa",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div style={{ display: "flex", alignItems: "center" }}>
          <BugOutlined style={{ marginRight: 8, color: "#1890ff" }} />
          <Title level={5} style={{ margin: 0 }}>
            {title}
          </Title>
          <Tag color="blue" style={{ marginLeft: 8 }}>
            DEV
          </Tag>
        </div>
        <Button
          type="text"
          icon={expanded ? <DownOutlined /> : <RightOutlined />}
          onClick={toggleExpand}
          size="small"
        >
          {expanded ? "Ocultar" : "Mostrar"}
        </Button>
      </div>

      {expanded && <div style={{ marginTop: 16 }}>{formatData(data)}</div>}
    </Card>
  );
};

export default DebugInfo;
