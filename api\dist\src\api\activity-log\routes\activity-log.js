"use strict";
/**
 * Rutas personalizadas para el registro de actividades
 * Sigue el patrón estándar de rutas personalizadas del sistema
 *
 * Nota: La verificación de administrador se realiza en los controladores
 * mediante la comprobación de user.role.type === 'administrador'
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        /**
         * @route GET /activity-logs/user/:id
         * @description Obtiene las actividades de un usuario específico
         * @access Autenticado
         */
        {
            method: 'GET',
            path: '/activity-logs/user/:id',
            handler: 'activity-log.findByUser',
            config: {
                policies: ['global::isAuthenticated'],
                description: 'Obtiene las actividades de un usuario específico',
                tags: ['Activity Logs']
            },
        },
        /**
         * @route GET /activity-logs/all
         * @description Obtiene todas las actividades (solo administradores)
         * @access Autenticado
         */
        {
            method: 'GET',
            path: '/activity-logs/all',
            handler: 'activity-log.findAllActivities',
            config: {
                policies: ['global::isAuthenticated'],
                description: 'Obtiene todas las actividades (solo administradores)',
                tags: ['Activity Logs', 'Admin']
            },
        },
        /**
         * @route GET /activity-logs/my-tenants
         * @description Obtiene las actividades de los arrendatarios del usuario actual
         * @access Autenticado
         */
        {
            method: 'GET',
            path: '/activity-logs/my-tenants',
            handler: 'activity-log.findMyTenantsActivities',
            config: {
                policies: ['global::isAuthenticated'],
                description: 'Obtiene las actividades de los arrendatarios del usuario actual',
                tags: ['Activity Logs']
            },
        },
        /**
         * @route GET /activity-logs/summary
         * @description Obtiene un resumen de las actividades
         * @access Autenticado
         */
        {
            method: 'GET',
            path: '/activity-logs/summary',
            handler: 'activity-log.getActivitySummary',
            config: {
                policies: ['global::isAuthenticated'],
                description: 'Obtiene un resumen de las actividades',
                tags: ['Activity Logs']
            },
        },
    ],
};
