"use strict";
/**
 * document router.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        // Obtener todos los documentos
        {
            method: 'GET',
            path: '/documents',
            handler: 'document.find',
            config: {
                auth: false,
            },
        },
        // Obtener un documento específico
        {
            method: 'GET',
            path: '/documents/:id',
            handler: 'document.findOne',
            config: {
                auth: false,
            },
        },
        // Crear un nuevo documento
        {
            method: 'POST',
            path: '/documents',
            handler: 'document.create',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        // Actualizar un documento
        {
            method: 'PUT',
            path: '/documents/:id',
            handler: 'document.update',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
        // Eliminar un documento
        {
            method: 'DELETE',
            path: '/documents/:id',
            handler: 'document.delete',
            config: {
                policies: ['global::isAuthenticated'],
            },
        },
    ],
};
