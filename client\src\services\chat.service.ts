/**
 * Servicio API para el sistema de chat
 */

import { api } from "@app/config/config";
import { readToken } from "@app/services/localStorage.service";
import type {
  ChatChannel,
  ChatMessage,
  ChatChannelsResponse,
  ChatChannelResponse,
  ChatMessagesResponse,
  CreateChatMessageRequest,
  UpdateChatMessageRequest,
  AddReactionRequest,
} from "@app/types/chat";

const API_BASE_URL = `${api}/api`;

class ChatService {
  private getAuthHeaders() {
    const token = readToken();
    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  }

  /**
   * Obtener todos los canales disponibles para el usuario
   */
  async getChannels(): Promise<ChatChannelsResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/chat-channels`, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error obteniendo canales:", error);
      throw error;
    }
  }

  /**
   * Obtener un canal específico con sus mensajes
   */
  async getChannel(
    channelId: number,
    page = 1,
    pageSize = 50,
  ): Promise<ChatChannelResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      const response = await fetch(
        `${API_BASE_URL}/chat-channels/${channelId}?${params}`,
        {
          method: "GET",
          headers: this.getAuthHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error obteniendo canal:", error);
      throw error;
    }
  }

  /**
   * Obtener mensajes de un canal con paginación
   */
  async getMessages(
    channelId: number,
    page = 1,
    pageSize = 50,
    before?: string,
  ): Promise<ChatMessagesResponse> {
    try {
      const params = new URLSearchParams({
        channelId: channelId.toString(),
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (before) {
        params.append("before", before);
      }

      const response = await fetch(`${API_BASE_URL}/chat-messages?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error obteniendo mensajes:", error);
      throw error;
    }
  }

  /**
   * Enviar un nuevo mensaje
   */
  async sendMessage(
    messageData: CreateChatMessageRequest,
  ): Promise<{ data: ChatMessage }> {
    try {
      const response = await fetch(`${API_BASE_URL}/chat-messages`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ data: messageData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error enviando mensaje:", error);
      throw error;
    }
  }

  /**
   * Editar un mensaje existente
   */
  async editMessage(
    messageId: number,
    updateData: UpdateChatMessageRequest,
  ): Promise<{ data: ChatMessage }> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/chat-messages/${messageId}`,
        {
          method: "PUT",
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ data: updateData }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error editando mensaje:", error);
      throw error;
    }
  }

  /**
   * Eliminar un mensaje
   */
  async deleteMessage(
    messageId: number,
  ): Promise<{ data: { id: number; deleted: boolean } }> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/chat-messages/${messageId}`,
        {
          method: "DELETE",
          headers: this.getAuthHeaders(),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error eliminando mensaje:", error);
      throw error;
    }
  }

  /**
   * Agregar o quitar reacción a un mensaje
   */
  async addReaction(
    messageId: number,
    reactionData: AddReactionRequest,
  ): Promise<any> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/chat-messages/${messageId}/reaction`,
        {
          method: "POST",
          headers: this.getAuthHeaders(),
          body: JSON.stringify(reactionData),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error procesando reacción:", error);
      throw error;
    }
  }

  /**
   * Buscar mensajes
   */
  async searchMessages(
    query: string,
    channelId?: number,
    limit = 20,
  ): Promise<{
    data: { results: ChatMessage[]; total: number; query: string };
  }> {
    try {
      const params = new URLSearchParams({
        query,
        limit: limit.toString(),
      });

      if (channelId) {
        params.append("channelId", channelId.toString());
      }

      const response = await fetch(
        `${API_BASE_URL}/chat-messages/search?${params}`,
        {
          method: "GET",
          headers: this.getAuthHeaders(),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error buscando mensajes:", error);
      throw error;
    }
  }

  /**
   * Crear un nuevo canal (solo administradores)
   */
  async createChannel(
    channelData: Partial<ChatChannel>,
  ): Promise<{ data: ChatChannel }> {
    try {
      const response = await fetch(`${API_BASE_URL}/chat-channels`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ data: channelData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error creando canal:", error);
      throw error;
    }
  }

  /**
   * Actualizar un canal existente (solo administradores)
   */
  async updateChannel(
    channelId: number,
    channelData: Partial<ChatChannel>,
  ): Promise<{ data: ChatChannel }> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/chat-channels/${channelId}`,
        {
          method: "PUT",
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ data: channelData }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message ||
            `Error ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error actualizando canal:", error);
      throw error;
    }
  }

  /**
   * Obtener usuarios para menciones
   */
  async searchUsers(query: string): Promise<
    Array<{
      id: number;
      username: string;
      firstName?: string;
      lastName?: string;
      displayName: string;
    }>
  > {
    try {
      // Usar el endpoint de usuarios existente para buscar usuarios
      const params = new URLSearchParams({
        "filters[username][$containsi]": query,
        "pagination[limit]": "10",
        populate: "role",
      });

      const response = await fetch(`${API_BASE_URL}/users?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Transformar la respuesta para el formato esperado
      return (
        data.data?.map((user: any) => ({
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          displayName:
            user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.username,
        })) || []
      );
    } catch (error) {
      console.error("Error buscando usuarios:", error);
      return [];
    }
  }
}

// Exportar instancia singleton
export const chatService = new ChatService();
export default chatService;
