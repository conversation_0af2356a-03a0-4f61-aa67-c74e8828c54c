import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { BaseForm } from "@app/components/common/forms/BaseForm/BaseForm";
import * as S from "./ForgotPasswordForm.styles";
import * as Auth from "@app/components/layouts/AuthLayout/AuthLayout.styles";
import { useAppDispatch } from "@app/hooks/reduxHooks";
import { doResetPassword } from "@app/store/slices/authSlice";
import { useFeedback } from "@app/hooks/useFeedback";

interface ForgotPasswordFormData {
  email: string;
}

export const ForgotPasswordForm = () => {
  const { t } = useTranslation();
  const { notification } = useFeedback();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [isLoading, setLoading] = useState(false);

  const handleSubmit = (values: ForgotPasswordFormData) => {
    setLoading(true);
    dispatch(doResetPassword(values))
      .unwrap()
      .then(() => {
        setLoading(false);
        navigate("/auth/login");
        notification.info({
          message: values.email,
          description:
            "Revisa tu bandeja y sigue las instrucciones para restablecer la contraseña",
        });
      })
      .catch((err) => {
        notification.error({ message: err.message });
        setLoading(false);
      });
  };

  return (
    <Auth.FormWrapper>
      <BaseForm
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark="optional"
      >
        <Auth.BackWrapper onClick={() => navigate(-1)}>
          <Auth.BackIcon />
          {t("common.back")}
        </Auth.BackWrapper>
        <Auth.FormTitle>{t("common.resetPassword")}</Auth.FormTitle>
        <S.Description>{t("forgotPassword.description")}</S.Description>
        <Auth.FormItem
          name="email"
          label={t("common.email")}
          rules={[{ required: true, message: t("common.emailError") }]}
        >
          <Auth.FormInput placeholder={t("common.email")} />
        </Auth.FormItem>
        <BaseForm.Item noStyle>
          <S.SubmitButton type="primary" htmlType="submit" loading={isLoading}>
            {t("forgotPassword.sendInstructions")}
          </S.SubmitButton>
        </BaseForm.Item>
      </BaseForm>
    </Auth.FormWrapper>
  );
};
