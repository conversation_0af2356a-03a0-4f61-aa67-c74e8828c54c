"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * ledger controller
 */
const strapi_1 = require("@strapi/strapi");
module.exports = strapi_1.factories.createCoreController('api::ledger.ledger', ({ strapi }) => ({
    async find(ctx) {
        try {
            const { data, meta } = await super.find(ctx);
            return { data, meta };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async create(ctx) {
        try {
            const { data } = await super.create(ctx);
            return { data };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async update(ctx) {
        try {
            const { data } = await super.update(ctx);
            return { data };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async findAll(ctx) {
        try {
            const entries = await strapi.entityService.findMany('api::ledger.ledger', {
                populate: ['owner'],
                sort: { fecha: 'desc' },
            });
            return {
                data: entries,
            };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async findByOwner(ctx) {
        try {
            const { ownerId } = ctx.params;
            if (!ownerId) {
                ctx.throw(400, 'Owner ID is required');
                return;
            }
            const entries = await strapi.entityService.findMany('api::ledger.ledger', {
                filters: {
                    owner: {
                        id: {
                            $eq: ownerId
                        }
                    }
                },
                populate: ['owner'],
                sort: { fecha: 'desc' },
            });
            return {
                data: entries
            };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async findGroupedByAddress(ctx) {
        try {
            const entries = await strapi.entityService.findMany('api::ledger.ledger', {
                populate: ['owner'],
                sort: { fecha: 'desc' },
            });
            const groupedEntries = entries.reduce((acc, entry) => {
                var _a;
                const address = ((_a = entry.owner) === null || _a === void 0 ? void 0 : _a.address) || entry.apartmentAddress;
                if (!address)
                    return acc;
                if (!acc[address]) {
                    acc[address] = {
                        id: address,
                        propietario: entry.owner ? `${entry.owner.firstName} ${entry.owner.lastName}`.trim() : 'Sin propietario',
                        ledger: []
                    };
                }
                acc[address].ledger.push({
                    month: entry.month,
                    expensa: entry.expensa,
                    honorarios: entry.honorarios,
                    interes: entry.interes,
                    pagos: entry.pagos,
                    saldo: entry.saldo
                });
                return acc;
            }, {});
            return {
                data: Object.values(groupedEntries)
            };
        }
        catch (err) {
            ctx.throw(500, err);
        }
    },
    async bulkUpload(ctx) {
        try {
            const { accounts } = ctx.request.body;
            // 1. Primero eliminamos todos los registros existentes
            await strapi.db.query('api::ledger.ledger').deleteMany({});
            // 2. Función mejorada para ordenar IDs de apartamentos
            const sortApartmentIds = (a, b) => {
                // Convertir a mayúsculas para evitar problemas de case sensitivity
                const strA = a.toUpperCase();
                const strB = b.toUpperCase();
                // Extraer la letra y el número usando una expresión regular mejorada
                const regex = /^([A-Za-z]*)(\d*)$/;
                const matchA = strA.match(regex);
                const matchB = strB.match(regex);
                // Si alguno no cumple el formato, usar ordenamiento por defecto
                if (!matchA || !matchB)
                    return strA.localeCompare(strB, 'es', { numeric: true });
                const [, prefixA, numA] = matchA;
                const [, prefixB, numB] = matchB;
                // Ordenar primero por prefijo (A, B, C, etc.)
                if (prefixA !== prefixB) {
                    return prefixA.localeCompare(prefixB, 'es');
                }
                // Si el prefijo es igual, ordenar por número
                const numAInt = parseInt(numA, 10) || 0;
                const numBInt = parseInt(numB, 10) || 0;
                if (numAInt !== numBInt) {
                    return numAInt - numBInt;
                }
                // Si los números son iguales, ordenar por el string completo
                return strA.localeCompare(strB, 'es', { numeric: true });
            };
            // Ordenar las cuentas usando la función de ordenamiento mejorada
            const sortedAccounts = [...accounts].sort((a, b) => sortApartmentIds(a.id, b.id));
            if (!accounts || !Array.isArray(accounts)) {
                ctx.throw(400, 'Invalid request body');
                return {
                    success: false,
                    count: 0,
                    data: []
                };
            }
            let totalCreated = 0;
            const groupedEntries = {};
            // 3. Procesamos las cuentas ordenadas
            for (const account of sortedAccounts) {
                const user = await strapi.db.query('plugin::users-permissions.user').findOne({
                    where: { address: account.id },
                    select: ['id', 'username', 'firstName', 'lastName', 'address']
                });
                for (const entry of account.ledger) {
                    const ledgerData = {
                        data: {
                            month: entry.month,
                            expensa: Number(entry.expensa),
                            honorarios: Number(entry.honorarios),
                            interes: Number(entry.interes),
                            pagos: Number(entry.pagos),
                            saldo: Number(entry.saldo),
                            fecha: new Date(entry.month + '-01'),
                            estado: 'pendiente',
                            owner: (user === null || user === void 0 ? void 0 : user.id) ? { id: user.id } : null,
                            apartmentAddress: user ? null : account.id
                        }
                    };
                    try {
                        await strapi.entityService.create('api::ledger.ledger', ledgerData);
                        totalCreated++;
                        const address = (user === null || user === void 0 ? void 0 : user.address) || account.id;
                        if (!groupedEntries[address]) {
                            groupedEntries[address] = {
                                id: address,
                                propietario: (user === null || user === void 0 ? void 0 : user.name) || 'Sin propietario',
                                ledger: []
                            };
                        }
                        groupedEntries[address].ledger.push({
                            month: entry.month,
                            expensa: entry.expensa,
                            honorarios: entry.honorarios,
                            interes: entry.interes,
                            pagos: entry.pagos,
                            saldo: entry.saldo
                        });
                    }
                    catch (err) {
                        console.error('Error creating ledger entry:', err);
                    }
                }
            }
            return {
                success: true,
                count: totalCreated,
                data: Object.values(groupedEntries)
            };
        }
        catch (err) {
            ctx.throw(500, err);
            return {
                success: false,
                count: 0,
                data: []
            };
        }
    }
}));
