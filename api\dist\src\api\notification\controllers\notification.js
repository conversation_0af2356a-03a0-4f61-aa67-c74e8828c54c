"use strict";
/**
 * notification controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::notification.notification", ({ strapi }) => ({
    async findByUser(ctx) {
        try {
            const { userId } = ctx.params;
            if (!userId) {
                return ctx.badRequest("Se requiere el ID del usuario");
            }
            const notifications = await strapi.entityService.findMany("api::notification.notification", {
                filters: {
                    user: {
                        id: {
                            $eq: userId,
                        },
                    },
                },
                sort: { createdAt: "desc" },
                populate: {
                    user: {
                        fields: ["id", "firstName", "lastName", "address"], // Solo incluir campos no sensibles
                    },
                },
            });
            return this.transformResponse(notifications);
        }
        catch (err) {
            strapi.log.error("Error en findByUser:", err);
            return ctx.badRequest("Error al buscar notificaciones del usuario");
        }
    },
    async markAsRead(ctx) {
        try {
            const { id } = ctx.params;
            if (!id) {
                return ctx.badRequest("Se requiere el ID de la notificación");
            }
            const notification = await strapi.entityService.update("api::notification.notification", id, {
                data: { read: true },
            });
            return this.transformResponse(notification);
        }
        catch (err) {
            strapi.log.error("Error en markAsRead:", err);
            return ctx.badRequest("Error al marcar la notificación como leída");
        }
    },
    async markAllAsRead(ctx) {
        try {
            const { userId } = ctx.params;
            if (!userId) {
                return ctx.badRequest("Se requiere el ID del usuario");
            }
            // Encontrar todas las notificaciones no leídas del usuario
            const unreadNotifications = await strapi.entityService.findMany("api::notification.notification", {
                filters: {
                    user: {
                        id: {
                            $eq: userId,
                        },
                    },
                    read: false,
                },
            });
            if (unreadNotifications.length === 0) {
                return this.transformResponse({
                    message: "No hay notificaciones sin leer para este usuario.",
                });
            }
            // Actualizar todas las notificaciones encontradas a leídas
            const updatePromises = unreadNotifications.map((notification) => strapi.entityService.update("api::notification.notification", notification.id, {
                data: { read: true },
            }));
            await Promise.all(updatePromises);
            return this.transformResponse({
                message: `Se marcaron ${unreadNotifications.length} notificaciones como leídas.`,
            });
        }
        catch (err) {
            strapi.log.error("Error en markAllAsRead:", err);
            return ctx.badRequest("Error al marcar todas las notificaciones como leídas");
        }
    },
    async sendToUser(ctx) {
        try {
            const { userId } = ctx.params;
            const { title, message } = ctx.request.body;
            if (!userId || !title || !message) {
                return ctx.badRequest("Se requieren ID de usuario, título y mensaje");
            }
            // Verificar si el usuario existe
            const userExists = await strapi.entityService.findOne("plugin::users-permissions.user", userId, {
                fields: ["id", "firstName", "lastName", "address"], // Solo incluir campos no sensibles
            });
            if (!userExists) {
                return ctx.notFound("Usuario no encontrado");
            }
            const newNotification = await strapi.entityService.create("api::notification.notification", {
                data: {
                    title,
                    message,
                    read: false,
                    user: userId,
                    publishedAt: new Date(),
                },
            });
            // Intentar enviar la notificación en tiempo real a través de WebSocket
            try {
                // Obtenemos el servicio de notificaciones
                const notificationService = strapi.service("api::notification.notification");
                if (notificationService &&
                    typeof notificationService.notifyUser === "function") {
                    await notificationService.notifyUser(userId, {
                        type: "notification",
                        data: {
                            id: newNotification.id,
                            title,
                            message,
                        },
                    });
                }
            }
            catch (error) {
                strapi.log.error("Error al enviar notificación en tiempo real:", error);
            }
            return this.transformResponse(newNotification);
        }
        catch (err) {
            strapi.log.error("Error en sendToUser:", err);
            return ctx.badRequest("Error al enviar la notificación al usuario");
        }
    },
    async deleteNotification(ctx) {
        try {
            const { id } = ctx.params;
            if (!id) {
                return ctx.badRequest("Se requiere el ID de la notificación");
            }
            // Verificar que la notificación existe
            const notification = await strapi.entityService.findOne("api::notification.notification", id);
            if (!notification) {
                return ctx.notFound("Notificación no encontrada");
            }
            // Eliminar la notificación
            await strapi.entityService.delete("api::notification.notification", id);
            return this.transformResponse({
                message: "Notificación eliminada correctamente",
            });
        }
        catch (err) {
            strapi.log.error("Error en deleteNotification:", err);
            return ctx.badRequest("Error al eliminar la notificación");
        }
    },
    // Enviar notificación masiva a múltiples usuarios
    async sendBulkNotification(ctx) {
        var _a, _b;
        try {
            const { title, message, type = "info", userIds, roleFilter, } = ctx.request.body;
            if (!title || !message) {
                return ctx.badRequest("Se requieren título y mensaje");
            }
            // Verificar permisos del usuario
            const currentUser = ctx.state.user;
            if (!currentUser) {
                return ctx.unauthorized("Usuario no autenticado");
            }
            // Verificar que el usuario tenga permisos (admin, consejero, vigilante)
            const userWithRole = await strapi.entityService.findOne("plugin::users-permissions.user", currentUser.id, { populate: ["role"] });
            const allowedRoles = ["admin", "consejero", "vigilante"];
            const userRole = (_b = (_a = userWithRole === null || userWithRole === void 0 ? void 0 : userWithRole.role) === null || _a === void 0 ? void 0 : _a.type) === null || _b === void 0 ? void 0 : _b.toLowerCase();
            if (!allowedRoles.includes(userRole)) {
                return ctx.forbidden("No tienes permisos para enviar notificaciones masivas");
            }
            let targetUsers = [];
            // Si se especifican IDs de usuarios específicos
            if (userIds && Array.isArray(userIds) && userIds.length > 0) {
                targetUsers = await strapi.entityService.findMany("plugin::users-permissions.user", {
                    filters: { id: { $in: userIds } },
                    fields: ["id", "firstName", "lastName", "email"],
                });
            }
            // Si se especifica filtro por rol
            else if (roleFilter) {
                const role = await strapi.entityService.findMany("plugin::users-permissions.role", {
                    filters: { type: roleFilter },
                });
                if (role.length > 0) {
                    targetUsers = await strapi.entityService.findMany("plugin::users-permissions.user", {
                        filters: { role: { id: role[0].id } },
                        fields: ["id", "firstName", "lastName", "email"],
                    });
                }
            }
            // Si no se especifica nada, enviar a todos los usuarios
            else {
                targetUsers = await strapi.entityService.findMany("plugin::users-permissions.user", {
                    fields: ["id", "firstName", "lastName", "email"],
                });
            }
            if (targetUsers.length === 0) {
                return ctx.badRequest("No se encontraron usuarios para enviar la notificación");
            }
            // Crear notificaciones para todos los usuarios objetivo
            const notifications = [];
            const errors = [];
            for (const user of targetUsers) {
                try {
                    const notification = await strapi.entityService.create("api::notification.notification", {
                        data: {
                            title,
                            message,
                            type,
                            read: false,
                            user: user.id,
                            publishedAt: new Date(),
                        },
                    });
                    notifications.push(notification);
                    // Enviar notificación en tiempo real si WebSocket está disponible
                    if (strapi.websocket) {
                        strapi.websocket.sendNotificationToUser(user.id, {
                            id: notification.id,
                            title,
                            message,
                            type,
                            read: false,
                            createdAt: notification.createdAt,
                        });
                    }
                }
                catch (error) {
                    console.error(`Error enviando notificación a usuario ${user.id}:`, error);
                    errors.push({ userId: user.id, error: error.message });
                }
            }
            return {
                success: true,
                message: `Notificación enviada a ${notifications.length} usuarios`,
                sent: notifications.length,
                total: targetUsers.length,
                errors: errors.length > 0 ? errors : undefined,
            };
        }
        catch (err) {
            strapi.log.error("Error en sendBulkNotification:", err);
            return ctx.badRequest("Error al enviar notificación masiva");
        }
    },
}));
