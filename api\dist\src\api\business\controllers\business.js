"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
function generateDocumentId() {
    const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 24; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
exports.default = strapi_1.factories.createCoreController("api::business.business", ({ strapi }) => ({
    async create(ctx) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        try {
            const data = JSON.parse(ctx.request.body.data || "{}");
            const logo = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a["files.logo"];
            const photoOne = (_b = ctx.request.files) === null || _b === void 0 ? void 0 : _b["files.photoOne"];
            const photoTwo = (_c = ctx.request.files) === null || _c === void 0 ? void 0 : _c["files.photoTwo"];
            const photoThree = (_d = ctx.request.files) === null || _d === void 0 ? void 0 : _d["files.photoThree"];
            const photoFour = (_e = ctx.request.files) === null || _e === void 0 ? void 0 : _e["files.photoFour"];
            const entity = await strapi.entityService.create("api::business.business", {
                data: {
                    ...data,
                    documentId: generateDocumentId(),
                    businessUser: (_f = ctx.state.user) === null || _f === void 0 ? void 0 : _f.id,
                },
            });
            const filesToUpload = [
                { field: "logo", file: logo },
                { field: "photoOne", file: photoOne },
                { field: "photoTwo", file: photoTwo },
                { field: "photoThree", file: photoThree },
                { field: "photoFour", file: photoFour },
            ];
            const uploadPromises = filesToUpload
                .filter(({ file }) => !!file)
                .map(({ field, file }) => strapi.plugins.upload.service("upload").upload({
                data: { ref: "api::business.business", refId: entity.id, field },
                files: file,
            }));
            await Promise.all(uploadPromises);
            const updatedEntity = (await strapi.db
                .query("api::business.business")
                .findOne({
                where: { id: entity.id },
                populate: {
                    logo: true,
                    photoOne: true,
                    photoTwo: true,
                    photoThree: true,
                    photoFour: true,
                    businessUser: true,
                },
            }));
            return {
                ...updatedEntity,
                logo: ((_g = updatedEntity.logo) === null || _g === void 0 ? void 0 : _g.url) || null,
                photoOne: ((_h = updatedEntity.photoOne) === null || _h === void 0 ? void 0 : _h.url) || null,
                photoTwo: ((_j = updatedEntity.photoTwo) === null || _j === void 0 ? void 0 : _j.url) || null,
                photoThree: ((_k = updatedEntity.photoThree) === null || _k === void 0 ? void 0 : _k.url) || null,
                photoFour: ((_l = updatedEntity.photoFour) === null || _l === void 0 ? void 0 : _l.url) || null,
                photosCount: [
                    updatedEntity.photoOne,
                    updatedEntity.photoTwo,
                    updatedEntity.photoThree,
                    updatedEntity.photoFour,
                ].filter(Boolean).length,
                userName: ((_m = updatedEntity.businessUser) === null || _m === void 0 ? void 0 : _m.username) || null,
            };
        }
        catch (error) {
            console.error("Error al crear emprendimiento:", error);
            ctx.throw(500, error);
        }
    },
    async update(ctx) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        try {
            const { id } = ctx.params;
            const { data } = ctx.request.body;
            const parsedData = typeof data === "string" ? JSON.parse(data) : data;
            const logo = (_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a["files.logo"];
            const photoOne = (_b = ctx.request.files) === null || _b === void 0 ? void 0 : _b["files.photoOne"];
            const photoTwo = (_c = ctx.request.files) === null || _c === void 0 ? void 0 : _c["files.photoTwo"];
            const photoThree = (_d = ctx.request.files) === null || _d === void 0 ? void 0 : _d["files.photoThree"];
            const photoFour = (_e = ctx.request.files) === null || _e === void 0 ? void 0 : _e["files.photoFour"];
            await strapi.entityService.update("api::business.business", id, {
                data: parsedData,
            });
            const filesToUpload = [
                { field: "logo", file: logo },
                { field: "photoOne", file: photoOne },
                { field: "photoTwo", file: photoTwo },
                { field: "photoThree", file: photoThree },
                { field: "photoFour", file: photoFour },
            ];
            const uploadPromises = filesToUpload
                .filter(({ file }) => !!file)
                .map(({ field, file }) => strapi.plugins.upload.service("upload").upload({
                data: { ref: "api::business.business", refId: id, field },
                files: file,
            }));
            await Promise.all(uploadPromises);
            const finalEntity = (await strapi.db
                .query("api::business.business")
                .findOne({
                where: { id },
                populate: {
                    logo: true,
                    photoOne: true,
                    photoTwo: true,
                    photoThree: true,
                    photoFour: true,
                    businessUser: true,
                },
            }));
            return {
                ...finalEntity,
                logo: ((_f = finalEntity.logo) === null || _f === void 0 ? void 0 : _f.url) || null,
                photoOne: ((_g = finalEntity.photoOne) === null || _g === void 0 ? void 0 : _g.url) || null,
                photoTwo: ((_h = finalEntity.photoTwo) === null || _h === void 0 ? void 0 : _h.url) || null,
                photoThree: ((_j = finalEntity.photoThree) === null || _j === void 0 ? void 0 : _j.url) || null,
                photoFour: ((_k = finalEntity.photoFour) === null || _k === void 0 ? void 0 : _k.url) || null,
                photosCount: [
                    finalEntity.photoOne,
                    finalEntity.photoTwo,
                    finalEntity.photoThree,
                    finalEntity.photoFour,
                ].filter(Boolean).length,
                userName: ((_l = finalEntity.businessUser) === null || _l === void 0 ? void 0 : _l.username) || null,
            };
        }
        catch (error) {
            console.error("Error al actualizar negocio:", error);
            return ctx.badRequest(`Error al actualizar negocio: ${error.message}`);
        }
    },
    async findById(ctx) {
        try {
            const { ownerId } = ctx.params;
            if (!ownerId) {
                return ctx.badRequest("ID de propietario no proporcionado");
            }
            const entities = await strapi.entityService.findMany("api::business.business", {
                filters: { businessUser: ownerId },
                populate: {
                    logo: true,
                    photoOne: true,
                    photoTwo: true,
                    photoThree: true,
                    photoFour: true,
                    businessUser: true,
                },
            });
            const results = entities.map((entity) => {
                var _a, _b, _c, _d, _e, _f;
                return ({
                    id: entity.id,
                    documentId: entity.documentId,
                    name: entity.name,
                    category: entity.category,
                    description: entity.description,
                    phone: entity.phone,
                    email: entity.email,
                    address: entity.address,
                    schedule: entity.schedule,
                    website: entity.website,
                    facebook: entity.facebook,
                    instagram: entity.instagram,
                    featured: entity.featured,
                    createdAt: entity.createdAt,
                    updatedAt: entity.updatedAt,
                    publishedAt: entity.publishedAt,
                    logo: ((_a = entity.logo) === null || _a === void 0 ? void 0 : _a.url) || null,
                    photoOne: ((_b = entity.photoOne) === null || _b === void 0 ? void 0 : _b.url) || null,
                    photoTwo: ((_c = entity.photoTwo) === null || _c === void 0 ? void 0 : _c.url) || null,
                    photoThree: ((_d = entity.photoThree) === null || _d === void 0 ? void 0 : _d.url) || null,
                    photoFour: ((_e = entity.photoFour) === null || _e === void 0 ? void 0 : _e.url) || null,
                    photosCount: [
                        entity.photoOne,
                        entity.photoTwo,
                        entity.photoThree,
                        entity.photoFour,
                    ].filter(Boolean).length,
                    userName: ((_f = entity.businessUser) === null || _f === void 0 ? void 0 : _f.username) || null,
                    businessUser: entity.businessUser
                        ? {
                            id: entity.businessUser.id,
                            username: entity.businessUser.username,
                            email: entity.businessUser.email,
                            firstName: entity.businessUser.firstName,
                            lastName: entity.businessUser.lastName,
                            address: entity.businessUser.address,
                            phone: entity.businessUser.phone,
                        }
                        : null,
                });
            });
            return {
                data: results,
                meta: { count: results.length },
            };
        }
        catch (error) {
            console.error("Error en findById:", error);
            return ctx.badRequest(`Error en findById: ${error.message}`);
        }
    },
    async find(ctx) {
        try {
            const { _q, _sort, _limit, _start, ...filters } = ctx.query;
            const limit = parseInt(_limit, 10) || 25;
            const start = parseInt(_start, 10) || 0;
            const [entities, count] = await Promise.all([
                strapi.entityService.findMany("api::business.business", {
                    filters,
                    sort: _sort !== null && _sort !== void 0 ? _sort : undefined,
                    limit,
                    start,
                    populate: [
                        "logo",
                        "photoOne",
                        "photoTwo",
                        "photoThree",
                        "photoFour",
                        "businessUser",
                    ],
                }),
                strapi.entityService.count("api::business.business", {
                    filters,
                }),
            ]);
            const results = entities.map((entity) => {
                var _a, _b, _c, _d, _e, _f;
                return ({
                    id: entity.id,
                    documentId: entity.documentId,
                    name: entity.name,
                    category: entity.category,
                    description: entity.description,
                    phone: entity.phone,
                    email: entity.email,
                    address: entity.address,
                    schedule: entity.schedule,
                    website: entity.website,
                    facebook: entity.facebook,
                    instagram: entity.instagram,
                    featured: entity.featured,
                    createdAt: entity.createdAt,
                    updatedAt: entity.updatedAt,
                    publishedAt: entity.publishedAt,
                    logo: ((_a = entity.logo) === null || _a === void 0 ? void 0 : _a.url) || null,
                    photoOne: ((_b = entity.photoOne) === null || _b === void 0 ? void 0 : _b.url) || null,
                    photoTwo: ((_c = entity.photoTwo) === null || _c === void 0 ? void 0 : _c.url) || null,
                    photoThree: ((_d = entity.photoThree) === null || _d === void 0 ? void 0 : _d.url) || null,
                    photoFour: ((_e = entity.photoFour) === null || _e === void 0 ? void 0 : _e.url) || null,
                    photosCount: [
                        entity.photoOne,
                        entity.photoTwo,
                        entity.photoThree,
                        entity.photoFour,
                    ].filter(Boolean).length,
                    userName: ((_f = entity.businessUser) === null || _f === void 0 ? void 0 : _f.username) || null,
                    businessUser: entity.businessUser
                        ? {
                            id: entity.businessUser.id,
                            username: entity.businessUser.username,
                            email: entity.businessUser.email,
                            firstName: entity.businessUser.firstName,
                            lastName: entity.businessUser.lastName,
                            address: entity.businessUser.address,
                            phone: entity.businessUser.phone,
                        }
                        : null,
                });
            });
            return {
                data: results,
                meta: {
                    pagination: {
                        page: Math.floor(start / limit) + 1,
                        pageSize: limit,
                        pageCount: Math.ceil(count / limit),
                        total: count,
                    },
                },
            };
        }
        catch (error) {
            console.error("Error al obtener emprendimientos:", error);
            ctx.throw(500, error);
        }
    },
    async findOne(ctx) {
        var _a, _b, _c, _d, _e, _f;
        try {
            const { id } = ctx.params;
            const entity = await strapi.entityService.findOne("api::business.business", id, {
                populate: [
                    "logo",
                    "photoOne",
                    "photoTwo",
                    "photoThree",
                    "photoFour",
                    "businessUser",
                ],
            });
            if (!entity)
                return ctx.notFound("Emprendimiento no encontrado");
            const e = entity;
            const processedEntity = {
                id: e.id,
                documentId: e.documentId,
                name: e.name,
                category: e.category,
                description: e.description,
                phone: e.phone,
                email: e.email,
                address: e.address,
                schedule: e.schedule,
                website: e.website,
                facebook: e.facebook,
                instagram: e.instagram,
                featured: e.featured,
                createdAt: e.createdAt,
                updatedAt: e.updatedAt,
                publishedAt: e.publishedAt,
                logo: ((_a = e.logo) === null || _a === void 0 ? void 0 : _a.url) || null,
                photoOne: ((_b = e.photoOne) === null || _b === void 0 ? void 0 : _b.url) || null,
                photoTwo: ((_c = e.photoTwo) === null || _c === void 0 ? void 0 : _c.url) || null,
                photoThree: ((_d = e.photoThree) === null || _d === void 0 ? void 0 : _d.url) || null,
                photoFour: ((_e = e.photoFour) === null || _e === void 0 ? void 0 : _e.url) || null,
                photosCount: [
                    e.photoOne,
                    e.photoTwo,
                    e.photoThree,
                    e.photoFour,
                ].filter(Boolean).length,
                userName: ((_f = e.businessUser) === null || _f === void 0 ? void 0 : _f.username) || null,
                businessUser: e.businessUser
                    ? {
                        id: e.businessUser.id,
                        username: e.businessUser.username,
                        email: e.businessUser.email,
                        firstName: e.businessUser.firstName,
                        lastName: e.businessUser.lastName,
                        address: e.businessUser.address,
                        phone: e.businessUser.phone,
                    }
                    : null,
            };
            return processedEntity;
        }
        catch (error) {
            console.error("Error al obtener emprendimiento:", error);
            ctx.throw(500, error);
        }
    },
    async delete(ctx) {
        try {
            const { id } = ctx.params;
            if (!id)
                return ctx.badRequest("Se requiere un ID de negocio para eliminar");
            const business = await strapi.entityService.findOne("api::business.business", id);
            if (!business)
                return ctx.notFound("Negocio no encontrado");
            const deleted = await strapi.entityService.delete("api::business.business", id);
            return {
                data: deleted,
                meta: { message: "Negocio eliminado exitosamente" },
            };
        }
        catch (error) {
            console.error("Error al eliminar negocio:", error);
            return ctx.badRequest(`Error al eliminar negocio: ${error.message}`);
        }
    },
}));
