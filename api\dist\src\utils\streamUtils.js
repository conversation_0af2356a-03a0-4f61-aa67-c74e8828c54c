"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.streamUtils = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
exports.streamUtils = {
    getTemplatePath(type) {
        return (0, path_1.join)(process.cwd(), "config", "email-templates", `reservation-${type}.html`);
    },
    async readTemplate(templatePath, options = {}) {
        return new Promise((resolve, reject) => {
            let content = "";
            const stream = (0, fs_1.createReadStream)(templatePath, {
                ...options,
                encoding: "utf8",
            });
            stream.on("data", (chunk) => {
                content += chunk;
            });
            stream.on("error", (error) => reject(error));
            stream.on("end", () => resolve(content));
        });
    },
    async processTemplate(content, data) {
        let processedContent = content;
        // Procesar variables simples
        Object.entries(data).forEach(([key, value]) => {
            if (typeof value === "string" || typeof value === "number") {
                const regex = new RegExp(`{{\\s*${key}\\s*}}`, "g");
                processedContent = processedContent.replace(regex, String(value));
            }
        });
        // Procesar objetos anidados
        const flattenObject = (obj, prefix = "") => {
            return Object.entries(obj).reduce((acc, [key, value]) => {
                const newKey = prefix ? `${prefix}.${key}` : key;
                if (value && typeof value === "object" && !Array.isArray(value)) {
                    Object.assign(acc, flattenObject(value, newKey));
                }
                else if (typeof value === "string" ||
                    typeof value === "number" ||
                    typeof value === "boolean") {
                    acc[newKey] = String(value);
                }
                return acc;
            }, {});
        };
        const flatData = flattenObject(data);
        Object.entries(flatData).forEach(([key, value]) => {
            const regex = new RegExp(`{{\\s*${key}\\s*}}`, "g");
            processedContent = processedContent.replace(regex, value);
        });
        return processedContent;
    },
};
