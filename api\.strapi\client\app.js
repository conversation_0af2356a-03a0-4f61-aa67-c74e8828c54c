/**
 * This file was automatically generated by Strap<PERSON>.
 * Any modifications made will be discarded.
 */
import email from "@strapi/email/strapi-admin";
import strapiCloud from "@strapi/plugin-cloud/strapi-admin";
import documentation from "@strapi/plugin-documentation/strapi-admin";
import usersPermissions from "@strapi/plugin-users-permissions/strapi-admin";
import { renderAdmin } from "@strapi/strapi/admin";

import customisations from "../../src/admin/app.ts";

renderAdmin(document.getElementById("strapi"), {
  customisations,

  plugins: {
    email: email,
    "strapi-cloud": strapiCloud,
    documentation: documentation,
    "users-permissions": usersPermissions,
  },
});
