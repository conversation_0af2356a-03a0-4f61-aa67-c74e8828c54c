import ReactECharts, {
  EChartsOption,
  EChartsReactProps,
} from "echarts-for-react";
import { CSSProperties } from "react";
import { DefaultTheme, useTheme } from "styled-components";
import { Loading } from "../Loading/Loading";

export interface BaseChartProps extends Omit<EChartsReactProps, "option"> {
  option: EChartsOption;
  onEvents?: Record<string, (e: any) => void>;
  width?: string | number;
  height?: string | number;
  style?: CSSProperties;
  className?: string;
  loading?: boolean;
}

interface DefaultTooltipStyles {
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  textStyle: {
    fontWeight: number;
    fontSize: number;
    color: string;
  };
}

export const getChartColors = (theme: DefaultTheme): string[] => [
  theme.chartColor1,
  theme.chartColor2,
  theme.chartColor3,
  theme.chartColor4,
  theme.chartColor5,
];

export const getDefaultTooltipStyles = (
  theme: DefaultTheme,
): DefaultTooltipStyles => ({
  borderColor: theme.chartColor1,
  borderWidth: 2,
  borderRadius: parseInt(theme.borderRadius),
  textStyle: {
    fontWeight: 600,
    fontSize: 16,
    color: theme.chartColor1,
  },
});

export const BaseChart: React.FC<BaseChartProps> = ({
  option,
  width,
  height,
  onEvents,
  style,
  loading = false,
  ...props
}) => {
  const theme = useTheme();
  const chartHeight = height || "400px";

  const defaultOption = {
    color: getChartColors(theme),
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <ReactECharts
      {...props}
      option={{ ...defaultOption, ...option }}
      style={{
        ...style,
        height: chartHeight,
        minHeight: height === "100%" ? 400 : "unset",
        width,
        zIndex: 0,
      }}
      onEvents={onEvents}
    />
  );
};
