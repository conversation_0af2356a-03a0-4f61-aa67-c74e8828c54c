/**
 * Seed para complaint_templates – compatible con Strapi v5 (MySQL)
 *
 * 1) Desactiva FKs, vacía tablas
 * 2) Comprueba / crea el usuario admin
 * 3) Inserta cada plantilla con:
 *      • document_id     → crypto.randomUUID()
 *      • required_fields → JSON
 *      • fields          → JSON
 */
require("dotenv").config();
const mysql = require("mysql2/promise");
const crypto = require("crypto");
const templates = require("./templates"); // ← array de plantillas
/* ───── Configuración de DB ───── */
const db = {
    host: process.env.DATABASE_HOST || "localhost",
    port: Number(process.env.DATABASE_PORT || 3306),
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "strapi_palmas",
};
(async () => {
    var _a;
    let conn;
    try {
        console.log(`⏳ Conectando a ${db.user}@${db.host}/${db.database}…`);
        conn = await mysql.createConnection(db);
        console.log("✅ Conectado");
        /* 1. Desactivar FKs y vaciar tablas */
        await conn.execute("SET FOREIGN_KEY_CHECKS = 0");
        await conn.execute("TRUNCATE TABLE complaints_template_lnk"); // tabla pivote
        await conn.execute("TRUNCATE TABLE complaint_templates");
        await conn.execute("SET FOREIGN_KEY_CHECKS = 1");
        console.log("🗑️  Tablas vaciadas");
        /* 2. Obtener el ID del admin */
        const [[admin]] = await conn.execute("SELECT id FROM admin_users WHERE email = '<EMAIL>' LIMIT 1");
        let adminId = admin === null || admin === void 0 ? void 0 : admin.id;
        if (!adminId) {
            console.log("⚠️ No se encontró el usuario administrador. Ejecute primero el script seed-users-db.js");
            console.log("💡 Usando ID 1 como valor predeterminado para el administrador");
            adminId = 1; // Usar 1 como valor predeterminado si no se encuentra el admin
        }
        console.log(`👤 adminId = ${adminId}`);
        /* 3. Insertar plantillas */
        console.log("🚀 Insertando plantillas…");
        const now = new Date();
        let ok = 0;
        for (const tpl of templates) {
            try {
                await conn.execute(`INSERT INTO complaint_templates
             (document_id,title,category,subcategory,content,
              required_fields,fields,is_active,
              published_at,created_at,updated_at,
              created_by_id,updated_by_id)
           VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)`, [
                    crypto.randomUUID(), // ← UUID v4
                    tpl.title,
                    tpl.category,
                    (_a = tpl.subcategory) !== null && _a !== void 0 ? _a : null,
                    tpl.content,
                    JSON.stringify(tpl.requiredFields), // JSON
                    JSON.stringify(tpl.fields), // JSON
                    true,
                    now,
                    now,
                    now,
                    adminId,
                    adminId,
                ]);
                console.log(`  ✔ ${tpl.title}`);
                ok++;
            }
            catch (e) {
                console.error(`  ✖ ${tpl.title}: ${e.message}`);
            }
        }
        console.log(`\n✅ Plantillas insertadas: ${ok}/${templates.length}`);
    }
    catch (err) {
        console.error("❌ Error en el seed:", err);
    }
    finally {
        await (conn === null || conn === void 0 ? void 0 : conn.end());
        console.log("🔌 Conexión cerrada");
    }
})();
