import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { ReservationData } from "../types/reservation.types";
import { notification } from "antd";

export const useUpdateReservation = (id: string) => {
  const queryClient = useQueryClient();

  const { mutate: updateReservation, isPending: isLoading } = useMutation({
    mutationFn: async (data: ReservationData) => {
      const response = await Http.put(`/api/reservations/${id}`, data);
      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "Reserva actualizada",
        description: "La reserva ha sido actualizada exitosamente.",
      });
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
    onError: (error: any) => {
      notification.error({
        message: "Error",
        description: error.message || "Hubo un error al actualizar la reserva.",
      });
    },
  });

  return {
    updateReservation,
    isLoading,
  };
};
