module.exports = {
  async findPublic(ctx) {
    try {
      const { user } = ctx.state;

      // Verificar si el usuario está autenticado
      if (!user) {
        return ctx.unauthorized("No autorizado");
      }

      // Obtener el usuario actual con su rol
      const currentUser = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        user.id,
        { populate: ["role"] }
      );

      // Si es admin, devolver todos los usuarios
      if (currentUser?.role?.type === "admin") {
        const allUsers = await strapi.entityService.findMany(
          "plugin::users-permissions.user",
          {
            populate: ["role", "imgUrl"],
            sort: { createdAt: "desc" },
          }
        );
        return allUsers;
      }

      // Si no es admin, devolver solo usuarios públicos
      const publicUsers = await strapi.entityService.findMany(
        "plugin::users-permissions.user",
        {
          filters: {
            isPublic: true, // Solo usuarios que explícitamente han marcado su perfil como público
          },
          populate: ["role", "imgUrl"],
          sort: { createdAt: "desc" },
        }
      );

      return publicUsers;
    } catch (error) {
      console.error("Error fetching public users:", error);
      return ctx.internalServerError("Error interno del servidor");
    }
  },
};
