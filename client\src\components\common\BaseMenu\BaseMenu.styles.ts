import styled from "styled-components";
import { Menu as AntMenu } from "antd";

export const Menu = styled(AntMenu)`
  &.ant-menu .ant-menu-item-icon {
    font-size: ${({ theme }) => theme.fontSizes.xl};
    width: 1.25rem;
  }

  .ant-menu-item,
  .ant-menu-submenu {
    font-size: ${({ theme }) => theme.fontSizes.xs};
    border-radius: 0;
  }

  .ant-menu-item {
    fill: currentColor;
  }

  .ant-menu-inline,
  .ant-menu-vertical {
    border-right: 0;
  }

  /* Enhanced selected submenu item visibility */
  .ant-menu-submenu-selected > .ant-menu-submenu-title,
  &.ant-menu-light
    > .ant-menu
    .ant-menu-submenu-selected
    > .ant-menu-submenu-title {
    color: currentColor;
    font-weight: 800;
    background-color: transparent;

    &:hover {
      color: #fafafa;
      background-color: rgba(
        30,
        92,
        56,
        0.1
      ); /* Light background for better visibility */
    }
  }
`;
