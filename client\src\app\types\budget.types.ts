export interface BudgetCalculation {
  coefficient: number;
  yearlyAmount: number;
  monthlyAmount: number;
  roundedAmount: number;
}

export interface AnnualBudget {
  year: number;
  totalAmount: number;
  calculations: Record<string, BudgetCalculation>;
}

export interface ResidentBudget {
  residentId: string;
  name: string;
  coefficient: number;
  address: string;
  calculation?: BudgetCalculation;
}
