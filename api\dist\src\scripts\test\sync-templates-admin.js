'use strict';
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
// Cargar variables de entorno
dotenv.config();
// Configuración
const TEMPLATES_DIR = path.join(process.cwd(), 'config', 'email-templates');
// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent) => {
    // Extraer el contenido del div.container dentro del body
    const containerMatch = htmlContent.match(/<div class="container">([\s\S]*?)<\/div>\s*<\/body>/i);
    if (containerMatch && containerMatch[1]) {
        return containerMatch[1].trim();
    }
    // Si no encuentra el container, intenta extraer todo el body
    const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
        return bodyMatch[1].trim();
    }
    return '';
};
// Función principal
const syncTemplates = async () => {
    try {
        console.log('🔄 INICIANDO: Extracción de contenido de plantillas para la interfaz de Strapi');
        // Verificar que las plantillas existan
        const resetPasswordPath = path.join(TEMPLATES_DIR, 'reset-password.html');
        const emailConfirmationPath = path.join(TEMPLATES_DIR, 'email-confirmation.html');
        if (!fs.existsSync(resetPasswordPath) || !fs.existsSync(emailConfirmationPath)) {
            console.error('❌ Error: Las plantillas HTML no existen en el directorio config/email-templates/');
            console.log('📋 Ejecuta primero el script setup-email-templates.js para crear las plantillas');
            return;
        }
        // Leer las plantillas
        const resetPasswordHtml = fs.readFileSync(resetPasswordPath, 'utf8');
        const emailConfirmationHtml = fs.readFileSync(emailConfirmationPath, 'utf8');
        // Extraer el contenido del mensaje
        const resetPasswordContent = extractMessageBody(resetPasswordHtml);
        const emailConfirmationContent = extractMessageBody(emailConfirmationHtml);
        console.log('\n📋 INSTRUCCIONES PARA ACTUALIZAR LAS PLANTILLAS EN STRAPI:');
        console.log('1. Inicia sesión en el panel de administración de Strapi');
        console.log('2. Ve a Configuración > Email > Plantillas de email');
        console.log('\n📝 PLANTILLA DE RESTABLECIMIENTO DE CONTRASEÑA:');
        console.log('1. Haz clic en "Resetear contraseña"');
        console.log('2. Cambia el asunto a: "Restablecer contraseña"');
        console.log('3. Copia y pega el siguiente contenido en el campo de mensaje:');
        console.log('---INICIO DEL CONTENIDO---');
        console.log(resetPasswordContent);
        console.log('---FIN DEL CONTENIDO---');
        console.log('\n📝 PLANTILLA DE CONFIRMACIÓN DE CORREO:');
        console.log('1. Haz clic en "Confirmación de dirección de correo electrónico"');
        console.log('2. Cambia el asunto a: "Confirma tu correo electrónico"');
        console.log('3. Copia y pega el siguiente contenido en el campo de mensaje:');
        console.log('---INICIO DEL CONTENIDO---');
        console.log(emailConfirmationContent);
        console.log('---FIN DEL CONTENIDO---');
        console.log('\n✅ Después de actualizar ambas plantillas, haz clic en "Guardar" en cada una');
        console.log('🔍 Recuerda refrescar la página si no ves los cambios inmediatamente');
    }
    catch (error) {
        console.error('❌ ERROR: Fallo en la extracción de plantillas');
        console.error('🔧 Detalles del error:', error.message);
        console.error('📌 Stack:', error.stack);
    }
};
// Ejecutar la función principal
syncTemplates();
