import { SearchOutlined } from "@ant-design/icons";
import { BaseInputProps, BaseInputRef } from "../BaseInput/BaseInput";
import * as S from "./SearchInput.styles";
import { BaseSpin } from "../../BaseSpin/BaseSpin";
import {
  ChangeEvent,
  forwardRef,
  KeyboardEvent,
  MouseEvent,
  ReactNode,
} from "react";

interface SearchInputProps extends BaseInputProps {
  loading?: boolean;
  filter?: ReactNode;
  onSearch?: (
    value: string,
    event?:
      | ChangeEvent<HTMLInputElement>
      | MouseEvent<HTMLElement>
      | KeyboardEvent<HTMLInputElement>,
  ) => void;
  enterButton?: ReactNode;
  inputPrefixCls?: string;
}

export const SearchInput = forwardRef<BaseInputRef, SearchInputProps>(
  ({ loading, filter, ...props }, ref) => {
    return (
      <S.SearchInput
        ref={ref}
        prefix={<SearchOutlined />}
        {...(filter && {
          suffix: (
            <S.Space align="center">
              {loading && <BaseSpin size="small" />}
              {filter}
            </S.Space>
          ),
        })}
        {...props}
      />
    );
  },
);
