"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * activity-log controller
 */
const strapi_1 = require("@strapi/strapi");
// Función auxiliar para sanitizar datos de actividad
const sanitizeActivityData = (activity) => ({
    id: activity.id,
    action: activity.action,
    method: activity.method,
    route: activity.route,
    description: activity.description,
    ip: activity.ip,
    createdAt: activity.createdAt,
    user: activity.user
        ? {
            id: activity.user.id,
            username: activity.user.username,
            firstName: activity.user.firstName,
            lastName: activity.user.lastName,
            role: activity.user.role,
        }
        : null,
    metadata: activity.metadata,
});
exports.default = strapi_1.factories.createCoreController("api::activity-log.activity-log", ({ strapi }) => ({
    /**
     * Obtener registros de actividad para un usuario específico
     * @param {StrapiContext} ctx - Contexto de la solicitud
     */
    async findByUser(ctx) {
        var _a, _b, _c;
        const { id } = ctx.params;
        const { user } = ctx.state;
        // Verificar que el usuario actual está autenticado
        if (!user) {
            return ctx.unauthorized("No autorizado");
        }
        try {
            // Obtener el usuario actual con su rol y arrendatarios
            const currentUser = await strapi.entityService.findOne("plugin::users-permissions.user", user.id, {
                populate: ["role", "arrendatarios"],
            });
            const currentUserExtended = currentUser;
            // Verificar si el usuario es administrador
            const isAdmin = ((_a = currentUserExtended.role) === null || _a === void 0 ? void 0 : _a.type) === "administrador";
            // Si no es admin, verificar permisos
            if (!isAdmin) {
                // Verificar si el usuario solicitado es el usuario actual o uno de sus arrendatarios
                const isAuthorized = user.id === parseInt(id, 10) ||
                    (((_b = currentUserExtended.role) === null || _b === void 0 ? void 0 : _b.type) === "residente" &&
                        ((_c = currentUserExtended.arrendatarios) === null || _c === void 0 ? void 0 : _c.some((a) => a.id === parseInt(id, 10))));
                if (!isAuthorized) {
                    return ctx.forbidden("No tiene permiso para ver estos registros de actividad");
                }
            }
            // Buscar los registros de actividad del usuario solicitado
            const activities = await strapi.entityService.findMany("api::activity-log.activity-log", {
                filters: {
                    user: { id: parseInt(id, 10) },
                },
                sort: { createdAt: "desc" },
                populate: ["user"],
            });
            // Sanitizar los datos para la respuesta
            const sanitizedActivities = activities.map(sanitizeActivityData);
            return { data: sanitizedActivities };
        }
        catch (error) {
            console.error("Error al obtener registros de actividad:", error);
            return ctx.badRequest("Error al obtener los registros de actividad");
        }
    },
    /**
     * Obtener todos los registros de actividad (solo para administradores)
     * @param {StrapiContext} ctx - Contexto de la solicitud
     */
    async findAllActivities(ctx) {
        var _a;
        const { user } = ctx.state;
        // Verificar que el usuario actual está autenticado
        if (!user) {
            return ctx.unauthorized("No autorizado");
        }
        try {
            // Obtener el usuario actual con su rol
            const currentUser = await strapi.entityService.findOne("plugin::users-permissions.user", user.id, { populate: ["role"] });
            const currentUserExtended = currentUser;
            // Verificar si el usuario es administrador
            if (((_a = currentUserExtended.role) === null || _a === void 0 ? void 0 : _a.type) !== "administrador") {
                return ctx.forbidden("Solo los administradores pueden ver todos los registros de actividad");
            }
            // Buscar todos los registros de actividad
            const activities = await strapi.entityService.findMany("api::activity-log.activity-log", {
                sort: { createdAt: "desc" },
                populate: ["user"],
            });
            // Sanitizar los datos para la respuesta
            const sanitizedActivities = activities.map(sanitizeActivityData);
            return { data: sanitizedActivities };
        }
        catch (error) {
            console.error("Error al obtener todos los registros de actividad:", error);
            return ctx.badRequest("Error al obtener los registros de actividad");
        }
    },
    /**
     * Obtener registros de actividad de los arrendatarios del usuario actual
     * - Los administradores ven las actividades de los arrendatarios que crearon
     * - Los residentes ven las actividades de sus arrendatarios asignados
     * @param {StrapiContext} ctx - Contexto de la solicitud
     */
    async findMyTenantsActivities(ctx) {
        var _a, _b, _c;
        const { user } = ctx.state;
        // Verificar que el usuario actual está autenticado
        if (!user) {
            return ctx.unauthorized("No autorizado");
        }
        try {
            // Obtener el usuario actual con su rol
            const currentUser = (await strapi.entityService.findOne("plugin::users-permissions.user", user.id, {
                populate: {
                    arrendatarios: true,
                    role: {
                        fields: ["id", "name", "type"],
                    },
                },
            }));
            const isAdmin = ((_a = currentUser.role) === null || _a === void 0 ? void 0 : _a.type) === "administrador";
            const isResidente = ((_b = currentUser.role) === null || _b === void 0 ? void 0 : _b.type) === "residente";
            // Si no es ni administrador ni residente, denegar acceso
            if (!isAdmin && !isResidente) {
                return ctx.forbidden("Acceso denegado. Se requiere rol de administrador o residente");
            }
            // Definir los filtros de búsqueda
            let userFilters = {};
            if (isAdmin) {
                // Para administradores, obtener los arrendatarios que crearon
                const createdTenants = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findMany({
                    select: ["id"],
                    where: {
                        $and: [
                            {
                                $or: [
                                    { role: { type: "arrendatario" } },
                                    { role: { name: "Arrendatario" } },
                                ],
                            },
                            { usuarioPrincipal: user.id },
                        ],
                    },
                });
                const tenantIds = createdTenants.map((t) => t.id);
                if (tenantIds.length > 0) {
                    userFilters = { id: { $in: tenantIds } };
                }
                else {
                    return { data: [] }; // No hay arrendatarios creados por este admin
                }
            }
            else if (isResidente) {
                // Para residentes, obtener sus arrendatarios asignados
                const tenantIds = ((_c = currentUser.arrendatarios) === null || _c === void 0 ? void 0 : _c.map((t) => t.id)) || [];
                if (tenantIds.length === 0) {
                    return { data: [] };
                }
                userFilters = { id: { $in: tenantIds } };
            }
            // Buscar los registros de actividad de los arrendatarios correspondientes
            const activities = await strapi.db
                .query("api::activity-log.activity-log")
                .findMany({
                where: {
                    user: userFilters,
                },
                orderBy: { createdAt: "desc" },
                populate: {
                    user: {
                        populate: ["role"],
                    },
                },
            });
            // Sanitizar los datos para la respuesta
            const sanitizedActivities = activities.map(sanitizeActivityData);
            return { data: sanitizedActivities };
        }
        catch (error) {
            console.error("Error al obtener registros de actividad de arrendatarios:", error);
            return ctx.badRequest("Error al obtener los registros de actividad de arrendatarios");
        }
    },
    /**
     * Obtener un resumen de actividades de los arrendatarios del usuario actual
     * @param {StrapiContext} ctx - Contexto de la solicitud
     */
    async getActivitySummary(ctx) {
        var _a, _b, _c;
        const { user } = ctx.state;
        // Verificar que el usuario actual está autenticado
        if (!user) {
            return ctx.unauthorized("No autorizado");
        }
        try {
            // Obtener el usuario actual con su rol y arrendatarios
            const currentUser = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: user.id },
                populate: {
                    role: true,
                    arrendatarios: true,
                },
            });
            const isAdmin = ((_a = currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === null || _a === void 0 ? void 0 : _a.type) === "administrador";
            const isResidente = ((_b = currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === null || _b === void 0 ? void 0 : _b.type) === "residente";
            if (!isAdmin && !isResidente) {
                return ctx.forbidden("No tienes permisos para ver este resumen. Se requiere rol de administrador o residente");
            }
            // Si es administrador, obtener los arrendatarios que creó
            // Si es residente, obtener sus arrendatarios asignados
            let tenantIds = [];
            if (isAdmin) {
                // Para admin, obtener los arrendatarios que creó
                const createdTenants = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findMany({
                    select: ["id"],
                    where: {
                        $and: [
                            {
                                $or: [
                                    { role: { type: "arrendatario" } },
                                    { role: { name: "Arrendatario" } },
                                ],
                            },
                            { usuarioPrincipal: user.id },
                        ],
                    },
                });
                tenantIds = createdTenants.map((t) => t.id);
            }
            else {
                // Para residente, obtener solo sus arrendatarios asignados
                tenantIds = ((_c = currentUser.arrendatarios) === null || _c === void 0 ? void 0 : _c.map((t) => t.id)) || [];
            }
            if (tenantIds.length === 0) {
                return {
                    data: {
                        tenants: [],
                        summary: [],
                        totalActivities: 0,
                        activitiesByType: {},
                        activitiesByTenant: [],
                        recentActivities: [],
                    },
                };
            }
            // Obtener todas las actividades de los arrendatarios
            const activities = (await strapi.db
                .query("api::activity-log.activity-log")
                .findMany({
                where: {
                    user: { id: { $in: tenantIds } },
                },
                orderBy: { createdAt: "desc" },
                populate: {
                    user: {
                        populate: ["role"],
                    },
                },
            }));
            // Procesar los datos para el resumen
            const activitiesByType = {};
            const activitiesByTenant = {};
            let totalActivities = 0;
            // Procesar cada actividad
            activities.forEach((activity) => {
                var _a, _b;
                totalActivities++;
                // Contar por tipo de acción
                const actionType = ((_a = activity.action) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || "desconocido";
                activitiesByType[actionType] =
                    (activitiesByType[actionType] || 0) + 1;
                // Agrupar por arrendatario
                const user = activity.user;
                if (user) {
                    const tenantId = user.id;
                    if (!activitiesByTenant[tenantId]) {
                        activitiesByTenant[tenantId] = {
                            tenantId,
                            tenantName: `${user.firstName || ""} ${user.lastName || ""}`.trim() ||
                                user.username ||
                                "Usuario sin nombre",
                            totalActivities: 0,
                            lastActivity: null,
                            role: ((_b = user.role) === null || _b === void 0 ? void 0 : _b.name) || "Sin rol",
                        };
                    }
                    activitiesByTenant[tenantId].totalActivities++;
                    // Actualizar última actividad si es más reciente
                    if (!activitiesByTenant[tenantId].lastActivity ||
                        new Date(activity.createdAt) >
                            new Date(activitiesByTenant[tenantId].lastActivity)) {
                        activitiesByTenant[tenantId].lastActivity = activity.createdAt;
                    }
                }
            });
            // Ordenar actividades recientes (últimas 5)
            const recentActivities = activities
                .slice(0, 5)
                .map((activity) => {
                // Extraer los datos del usuario de manera segura
                let userData = null;
                if (activity.user) {
                    userData = {
                        id: activity.user.id,
                        username: activity.user.username,
                        firstName: activity.user.firstName || null,
                        lastName: activity.user.lastName || null,
                        role: activity.user.role
                            ? { name: activity.user.role.name }
                            : null,
                    };
                }
                // Devolver el objeto de actividad con el usuario formateado
                return {
                    id: activity.id,
                    action: activity.action || "",
                    method: activity.method || "",
                    route: activity.route || "",
                    description: activity.description || "",
                    ip: activity.ip || "",
                    createdAt: activity.createdAt,
                    user: userData,
                };
            });
            // Preparar respuesta
            const response = {
                totalActivities,
                activitiesByType,
                activitiesByTenant: Object.values(activitiesByTenant),
                recentActivities,
            };
            return { data: response };
        }
        catch (error) {
            console.error("Error al obtener resumen de actividades:", error);
            return ctx.badRequest("Error al obtener el resumen de actividades");
        }
    },
}));
