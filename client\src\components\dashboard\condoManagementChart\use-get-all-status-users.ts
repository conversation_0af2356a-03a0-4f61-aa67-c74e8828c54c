import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  address: string | null;
  phone: {
    countryCode: number;
    areaCode: string;
    phoneNumber: string;
  } | null;
  documentId: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
  coefficient: number | null;
  status: boolean | null; // true = al día, false = en mora
  profession: string | null;
  isPublic?: boolean;
  role?: {
    id: number;
    name: string;
    type: string;
    description: string;
  };
  imgUrl?: {
    url: string;
    formats?: {
      thumbnail?: {
        url: string;
      };
      small?: {
        url: string;
      };
    };
  };
}

interface UseGetAllStatusUsersResult {
  users: User[];
  loading: boolean;
  error: Error | null;
  invalidateUsers: () => void;
}

/**
 * Hook para obtener todos los usuarios del sistema
 * @returns Objeto con los usuarios, estado de carga, error y función para invalidar la caché
 */
export const useGetAllStatusUsers = (): UseGetAllStatusUsersResult => {
  const queryClient = useQueryClient();

  const fetchUsers = async (): Promise<User[]> => {
    const response = await Http.get<{ data: User[] }>("/api/users");
    return response.data?.data || [];
  };

  const invalidateUsers = () => {
    queryClient.invalidateQueries({ queryKey: ["users"] });
  };

  const {
    data = [],
    isLoading,
    error,
  } = useQuery<User[], Error>({
    queryKey: ["users"],
    queryFn: fetchUsers,
  });

  return {
    users: data,
    loading: isLoading,
    error,
    invalidateUsers,
  };
};
