'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Rutas personalizadas para la gestión de arrendatarios
 */
exports.default = {
    routes: [
        // Crear un nuevo arrendatario
        {
            method: 'POST',
            path: '/users/arrendatarios',
            handler: 'arrendatario.createArrendatario',
            config: {
                prefix: 'api',
                policies: ['global::isAuthenticated'],
            },
        },
        // Obtener todos los arrendatarios del usuario actual
        {
            method: 'GET',
            path: '/users/arrendatarios',
            handler: 'arrendatario.getMyArrendatarios',
            config: {
                prefix: 'api',
                policies: ['global::isAuthenticated'],
            },
        },
        // Actualizar un arrendatario específico
        {
            method: 'PUT',
            path: '/users/arrendatarios/:id',
            handler: 'arrendatario.updateArrendatario',
            config: {
                prefix: 'api',
                policies: ['global::isAuthenticated'],
            },
        },
        // Eliminar un arrendatario específico
        {
            method: 'DELETE',
            path: '/users/arrendatarios/:id',
            handler: 'arrendatario.deleteArrendatario',
            config: {
                prefix: 'api',
                policies: ['global::isAuthenticated'],
            },
        },
    ],
};
