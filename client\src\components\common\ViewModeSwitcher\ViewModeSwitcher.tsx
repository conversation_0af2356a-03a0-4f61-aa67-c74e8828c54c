import { TableOutlined, AppstoreOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { ViewMode } from "@app/hooks/useViewMode";

interface ViewModeSwitcherProps {
  viewMode: ViewMode;
  onToggleViewMode: () => void;
}

export const ViewModeSwitcher = ({
  viewMode,
  onToggleViewMode,
}: ViewModeSwitcherProps) => {
  return (
    <Button
      type="text"
      icon={viewMode === "table" ? <AppstoreOutlined /> : <TableOutlined />}
      onClick={onToggleViewMode}
      style={{ marginLeft: 16 }}
    />
  );
};
