import { PictureOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps } from "antd";
import { Button, message, Typography } from "antd";
import { useState } from "react";
import { BaseUpload } from "../BaseUpload/BaseUpload";

const { Text } = Typography;

interface UploadPictureProps {
  value?: UploadFile[] | string;
  onChange?: (fileList: UploadFile[]) => void;
  maxCount?: number;
  defaultImageUrl?: string;
}

export const UploadPicture: React.FC<UploadPictureProps> = ({
  defaultImageUrl,
  value = [],
  onChange,
  maxCount = 1,
}) => {
  const [imageUrl, setImageUrl] = useState<string>(
    typeof defaultImageUrl === "string" ? defaultImageUrl : "",
  );

  const fileList = Array.isArray(value) ? value : [];

  const validateFileType = (file: File) => {
    const isImage = file.type.startsWith("image/");
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!isImage) {
      message.error("Solo se permiten archivos de imagen");
      return false;
    }

    if (file.size > maxSize) {
      message.error("El archivo es demasiado grande. El tamaño máximo es 5MB");
      return false;
    }

    return true;
  };

  const handleChange: UploadProps["onChange"] = async ({
    fileList: newFileList,
  }) => {
    const file = newFileList[0];

    if (file && file.originFileObj && validateFileType(file.originFileObj)) {
      // Crear URL para previsualización
      if (!file.url && !file.preview) {
        file.preview = URL.createObjectURL(file.originFileObj);
      }
      setImageUrl(file.url || file.preview || "");

      if (onChange) {
        onChange(newFileList);
      }
    }
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
      }}
    >
      <BaseUpload
        name="picture"
        listType="picture-card"
        fileList={fileList}
        onChange={handleChange}
        maxCount={maxCount}
        showUploadList={false}
        beforeUpload={() => false} // Prevenir la carga automática
      >
        {imageUrl ? (
          <div style={{ position: "relative", width: "100%", height: "100%" }}>
            <img
              src={imageUrl}
              alt="Imagen"
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <PictureOutlined style={{ fontSize: 24 }} />
            <Text style={{ marginTop: 8 }}>Subir imagen</Text>
          </div>
        )}
      </BaseUpload>
      {imageUrl && (
        <Button
          type="link"
          onClick={() => {
            setImageUrl("");
            if (onChange) {
              onChange([]);
            }
          }}
          style={{ padding: 0, marginTop: 8 }}
        >
          Eliminar
        </Button>
      )}
    </div>
  );
};
