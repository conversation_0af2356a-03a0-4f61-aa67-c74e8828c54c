"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::budget.budget", ({ strapi }) => ({
    async create(ctx) {
        // Solo permitir un presupuesto activo a la vez
        if (ctx.request.body.data.active) {
            await strapi.db.query("api::budget.budget").updateMany({
                where: { active: true },
                data: { active: false },
            });
        }
        return super.create(ctx);
    },
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const { data } = ctx.request.body;
            // Verificar si el presupuesto existe
            const existingBudget = await strapi.db
                .query("api::budget.budget")
                .findOne({
                where: { id },
            });
            if (!existingBudget) {
                console.error("Update Budget - Error: Budget not found");
                ctx.throw(404, "Budget not found");
                return;
            }
            // Solo permitir un presupuesto activo a la vez
            if (data.active) {
                await strapi.db.query("api::budget.budget").updateMany({
                    where: { id: { $ne: id }, active: true },
                    data: { active: false },
                });
            }
            // Actualizar el presupuesto directamente
            const updatedBudget = await strapi.db
                .query("api::budget.budget")
                .update({
                where: { id },
                data: {
                    annualBudget: data.annualBudget,
                    year: data.year,
                    active: existingBudget.active, // Mantener el estado active actual
                },
            });
            if (!updatedBudget) {
                ctx.throw(500, "Failed to update budget");
                return;
            }
            return { data: updatedBudget };
        }
        catch (error) {
            console.error("Update Budget - Error:", error);
            throw error;
        }
    },
    async getActive(ctx) {
        const activeBudget = await strapi.db.query("api::budget.budget").findOne({
            where: { active: true },
            select: [
                "id",
                "annualBudget",
                "year",
                "active",
                "createdAt",
                "updatedAt",
            ],
        });
        // Si no hay presupuesto activo, devolver un objeto con valores por defecto
        if (!activeBudget) {
            return {
                data: {
                    id: null,
                    annualBudget: 0,
                    year: new Date().getFullYear(),
                    active: false,
                },
            };
        }
        return { data: activeBudget };
    },
    async getCalculations(ctx) {
        const activeBudget = await strapi.db.query("api::budget.budget").findOne({
            where: { active: true },
            select: ["id", "annualBudget", "year", "active"],
        });
        // Si no hay presupuesto activo, devolver cÃ¡lculos con valores en 0
        const users = (await strapi.db
            .query("plugin::users-permissions.user")
            .findMany({
            select: ["id", "username", "address", "coefficient"],
        }));
        // FunciÃ³n que implementa REDONDEAR.MAS de Excel (siempre redondea hacia arriba al mÃºltiplo de 100 mÃ¡s cercano)
        const roundUpToHundred = (amount) => {
            return Math.ceil(amount / 100) * 100;
        };
        const calculations = users.map((user) => {
            const annualFee = activeBudget
                ? activeBudget.annualBudget * (user.coefficient / 100)
                : 0;
            const monthlyFee = annualFee / 12;
            return {
                userId: user.id,
                username: user.username,
                address: user.address,
                coefficient: user.coefficient,
                annualFee: roundUpToHundred(annualFee),
                monthlyFee: roundUpToHundred(monthlyFee),
            };
        });
        return {
            data: calculations,
            meta: {
                budgetExists: !!activeBudget,
                message: activeBudget
                    ? "CÃ¡lculos basados en el presupuesto activo"
                    : "No hay presupuesto activo. Los valores mostrados son 0.",
            },
        };
    },
    async getUserCalculation(ctx) {
        const { id } = ctx.params;
        // Validar que se proporcione un ID
        if (!id) {
            ctx.throw(400, "Se requiere el ID del usuario");
            return;
        }
        const activeBudget = await strapi.db.query("api::budget.budget").findOne({
            where: { active: true },
            select: ["id", "annualBudget", "year", "active"],
        });
        // Obtener el usuario especÃ­fico
        const user = (await strapi.db
            .query("plugin::users-permissions.user")
            .findOne({
            where: { id },
            select: ["id", "username", "address", "coefficient"],
        }));
        // Si el usuario no existe, devolver error
        if (!user) {
            ctx.throw(404, "Usuario no encontrado");
            return;
        }
        // FunciÃ³n que implementa REDONDEAR.MAS de Excel (siempre redondea hacia arriba al mÃºltiplo de 100 mÃ¡s cercano)
        const roundUpToHundred = (amount) => {
            return Math.ceil(amount / 100) * 100;
        };
        // Calcular las cuotas
        const annualFee = activeBudget
            ? activeBudget.annualBudget * (user.coefficient / 100)
            : 0;
        const monthlyFee = annualFee / 12;
        const calculation = {
            userId: user.id,
            username: user.username,
            address: user.address,
            coefficient: user.coefficient,
            annualFee: roundUpToHundred(annualFee),
            monthlyFee: roundUpToHundred(monthlyFee),
        };
        return {
            data: calculation,
            meta: {
                budgetExists: !!activeBudget,
                message: activeBudget
                    ? "CÃ¡lculo basado en el presupuesto activo"
                    : "No hay presupuesto activo. Los valores mostrados son 0.",
            },
        };
    },
}));
