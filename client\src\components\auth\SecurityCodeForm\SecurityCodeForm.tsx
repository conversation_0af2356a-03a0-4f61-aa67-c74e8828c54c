import { useState, useEffect, useCallback } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { BaseForm } from "@app/components/common/forms/BaseForm/BaseForm";
import { VerificationCodeInput } from "@app/components/common/inputs/VerificationCodeInput/VerificationCodeInput";
import { useAppDispatch } from "@app/hooks/reduxHooks";
import { doVerifySecurityCode } from "@app/store/slices/authSlice";
import { useFeedback } from "@app/hooks/useFeedback";
import VerifyEmailImage from "@app/assets/images/verify-email.webp";
import * as Auth from "@app/components/layouts/AuthLayout/AuthLayout.styles";
import * as S from "./SecurityCodeForm.styles";
import { BaseImage } from "@app/components/common/BaseImage/BaseImage";
import { BaseSpin } from "@app/components/common/BaseSpin/BaseSpin";

interface SecurityCodeFormProps {
  onBack?: () => void;
  onFinish?: () => void;
}

export const SecurityCodeForm: React.FC<SecurityCodeFormProps> = ({
  onBack,
  onFinish,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { notification } = useFeedback();
  const dispatch = useAppDispatch();

  const navigateBack = useCallback(() => navigate(-1), [navigate]);
  const navigateForward = useCallback(
    () => navigate("/auth/update-password"),
    [navigate]
  );

  const [securityCode, setSecurityCode] = useState("");
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    if (securityCode.length === 6) {
      setLoading(true);
      dispatch(doVerifySecurityCode({ code: securityCode }))
        .unwrap()
        .then(onFinish || navigateForward)
        .catch((err) => {
          notification.error({ message: err.message });
          setLoading(false);
        });
    }
  }, [securityCode, navigateForward, onFinish, dispatch, notification]);

  return (
    <Auth.FormWrapper>
      <BaseForm layout="vertical" requiredMark="optional">
        <Auth.BackWrapper onClick={onBack || navigateBack}>
          <Auth.BackIcon />
          {t("common.back")}
        </Auth.BackWrapper>
        <S.ContentWrapper>
          <S.ImageWrapper>
            <BaseImage src={VerifyEmailImage} alt="Not found" preview={false} />
          </S.ImageWrapper>
          <Auth.FormTitle>{t("securityCodeForm.title")}</Auth.FormTitle>
          <S.VerifyEmailDescription>
            {t("common.verifCodeSent")}
          </S.VerifyEmailDescription>
          {isLoading ? (
            <BaseSpin />
          ) : (
            <VerificationCodeInput autoFocus onChange={setSecurityCode} />
          )}
          <Link to="/" target="_blank">
            <S.NoCodeText>{t("securityCodeForm.noCode")}</S.NoCodeText>
          </Link>
        </S.ContentWrapper>
      </BaseForm>
    </Auth.FormWrapper>
  );
};


