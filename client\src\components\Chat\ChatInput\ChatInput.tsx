/**
 * Componente de input para escribir mensajes en el chat
 */

import React, { useState, useRef, useCallback, useEffect } from "react";
import {
  Input,
  Button,
  Space,
  Typography,
  Tooltip,
  Upload,
  Popover,
  message,
  Tag,
} from "antd";
import {
  SendOutlined,
  SmileOutlined,
  PaperClipOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { EmojiPicker } from "../ChatMessage/EmojiPicker";
import { FileDownloadService } from "@app/services/fileDownload.service";
import type { ChatInputProps } from "@app/types/chat";
import * as S from "./ChatInput.styles";

const { TextArea } = Input;
const { Text } = Typography;

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  replyingTo,
  onCancelReply,
  disabled = false,
  placeholder = "Escribe un mensaje...",
  channelId,
}) => {
  const [messageText, setMessageText] = useState("");
  const [isComposing, setIsComposing] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const textAreaRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Focus automático cuando se monta el componente
  useEffect(() => {
    if (textAreaRef.current && !disabled) {
      textAreaRef.current.focus();
    }
  }, [disabled, channelId]);

  // Limpiar mensaje cuando cambia el canal
  useEffect(() => {
    setMessageText("");
  }, [channelId]);

  // Manejar envío de mensaje
  const handleSend = useCallback(() => {
    const trimmedMessage = messageText.trim();
    if ((!trimmedMessage && attachedFiles.length === 0) || disabled) return;

    onSendMessage(
      trimmedMessage,
      attachedFiles.length > 0 ? attachedFiles : undefined,
    );
    setMessageText("");
    setAttachedFiles([]);

    // Mantener focus en el input
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [messageText, attachedFiles, disabled, onSendMessage]);

  // Manejar selección de archivos
  const handleFileSelect = useCallback((files: File[]) => {
    const validFiles = files.filter((file) => {
      // Validar tamaño (máximo 10MB)
      if (file.size > 10 * 1024 * 1024) {
        message.error(
          `El archivo ${file.name} es demasiado grande (máximo 10MB)`,
        );
        return false;
      }
      return true;
    });

    setAttachedFiles((prev) => [...prev, ...validFiles]);
  }, []);

  // Remover archivo adjunto
  const handleRemoveFile = useCallback((index: number) => {
    setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Formatear tamaño de archivo usando el servicio
  const formatFileSize = (bytes: number) => {
    return FileDownloadService.formatFileSize(bytes);
  };

  // Manejar drag & drop
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Solo quitar el estado si realmente salimos del contenedor
    if (!containerRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files);
      }
    },
    [handleFileSelect],
  );

  // Manejar teclas especiales
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey && !isComposing) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend, isComposing],
  );

  // Manejar cambio de texto
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setMessageText(newValue);

      // TODO: Implementar indicador de "escribiendo" aquí
      // if (newValue.trim() && !disabled) {
      //   sendTypingIndicator(channelId, true);
      // }
    },
    [channelId, disabled],
  );

  // Manejar composición de texto (para IME)
  const handleCompositionStart = useCallback(() => {
    setIsComposing(true);
  }, []);

  const handleCompositionEnd = useCallback(() => {
    setIsComposing(false);
  }, []);

  // Manejar selección de emoji
  const handleEmojiSelect = useCallback(
    (emoji: string) => {
      const textarea = textAreaRef.current?.resizableTextArea?.textArea;
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newMessage =
          messageText.slice(0, start) + emoji + messageText.slice(end);
        setMessageText(newMessage);

        // Restaurar posición del cursor después del emoji
        setTimeout(() => {
          textarea.setSelectionRange(
            start + emoji.length,
            start + emoji.length,
          );
          textarea.focus();
        }, 0);
      } else {
        setMessageText((prev) => prev + emoji);
      }
      setShowEmojiPicker(false);
    },
    [messageText],
  );

  // Calcular si se puede enviar
  const canSend =
    (messageText.trim().length > 0 || attachedFiles.length > 0) && !disabled;

  return (
    <S.InputContainer
      ref={containerRef}
      style={{
        position: "relative",
        border: isDragOver ? "2px dashed #1890ff" : undefined,
        backgroundColor: isDragOver ? "#f0f8ff" : undefined,
        borderRadius: isDragOver ? "8px" : undefined,
      }}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Indicador de respuesta */}
      {replyingTo && (
        <S.ReplyIndicator>
          <div className="reply-content">
            <Text type="secondary" style={{ fontSize: "12px" }}>
              Respondiendo a{" "}
              {replyingTo.author.displayName || replyingTo.author.username}
            </Text>
            <Text ellipsis style={{ fontSize: "12px", maxWidth: "300px" }}>
              {replyingTo.content}
            </Text>
          </div>
          {onCancelReply && (
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={onCancelReply}
              style={{ opacity: 0.7 }}
            />
          )}
        </S.ReplyIndicator>
      )}

      {/* Archivos adjuntos */}
      {attachedFiles.length > 0 && (
        <div style={{ padding: "8px 12px", borderBottom: "1px solid #f0f0f0" }}>
          <Space wrap>
            {attachedFiles.map((file, index) => (
              <Tag
                key={index}
                closable
                onClose={() => handleRemoveFile(index)}
                style={{ marginBottom: "4px" }}
              >
                📎 {file.name} ({formatFileSize(file.size)})
              </Tag>
            ))}
          </Space>
        </div>
      )}

      {/* Overlay de drag & drop */}
      {isDragOver && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(24, 144, 255, 0.1)",
            border: "2px dashed #1890ff",
            borderRadius: "8px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 10,
            pointerEvents: "none",
          }}
        >
          <div style={{ textAlign: "center" }}>
            <div style={{ fontSize: "48px", marginBottom: "8px" }}>📎</div>
            <Text strong style={{ color: "#1890ff" }}>
              Suelta los archivos aquí
            </Text>
          </div>
        </div>
      )}

      {/* Input principal */}
      <S.MainInputContainer>
        <S.InputWrapper>
          {/* Área de texto */}
          <TextArea
            ref={textAreaRef}
            value={messageText}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            placeholder={placeholder}
            disabled={disabled}
            autoSize={{ minRows: 1, maxRows: 5 }}
            style={{
              border: "none",
              boxShadow: "none",
              resize: "none",
              padding: "8px 12px",
            }}
          />

          {/* Botones de acción */}
          <S.ActionButtons>
            <Space size="small">
              {/* Botón de emoji */}
              <Popover
                content={
                  <EmojiPicker
                    onEmojiSelect={handleEmojiSelect}
                    onVisibleChange={setShowEmojiPicker}
                  />
                }
                trigger="click"
                open={showEmojiPicker}
                onOpenChange={setShowEmojiPicker}
                placement="topLeft"
                styles={{ body: { padding: 0 } }}
              >
                <Tooltip title="Agregar emoji">
                  <Button
                    type="text"
                    size="small"
                    icon={<SmileOutlined />}
                    disabled={disabled}
                    style={{ opacity: 0.7 }}
                  />
                </Tooltip>
              </Popover>

              {/* Botón de archivos */}
              <Tooltip title="Adjuntar archivo">
                <Upload
                  beforeUpload={(_, fileList) => {
                    handleFileSelect(fileList);
                    return false; // Prevenir upload automático
                  }}
                  showUploadList={false}
                  disabled={disabled}
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mp3"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<PaperClipOutlined />}
                    disabled={disabled}
                    style={{ opacity: 0.7 }}
                  />
                </Upload>
              </Tooltip>

              {/* Botón de enviar */}
              <Tooltip
                title={
                  canSend ? "Enviar mensaje (Enter)" : "Escribe un mensaje"
                }
              >
                <Button
                  type="primary"
                  size="small"
                  icon={<SendOutlined />}
                  onClick={handleSend}
                  disabled={!canSend}
                  style={{
                    borderRadius: "50%",
                    width: "32px",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                />
              </Tooltip>
            </Space>
          </S.ActionButtons>
        </S.InputWrapper>

        {/* Información adicional */}
        <S.InputFooter>
          <Text type="secondary" style={{ fontSize: "11px" }}>
            {disabled
              ? "Enviando mensaje..."
              : "Presiona Enter para enviar, Shift+Enter para nueva línea"}
          </Text>
        </S.InputFooter>
      </S.MainInputContainer>
    </S.InputContainer>
  );
};
