/**
 * Script para depurar la creación de arrendatarios
 *
 * Este script simula la creación de un arrendatario y muestra los errores detallados
 * Ejecutar con: node scripts/debug-arrendatario.js
 */
const Strapi = require('@strapi/strapi');
const fs = require('fs');
const path = require('path');
// Datos de prueba para crear un arrendatario
const testData = {
    username: "arrendatario_test",
    email: "<EMAIL>",
    password: "Password123!",
    firstName: "Nombre",
    lastName: "Apellido",
    phone: "3193172727",
    document: "1234567890",
    address: "A01"
};
async function debugArrendatario() {
    try {
        // Iniciar Strapi
        const appDir = path.resolve(process.cwd());
        const strapi = await Strapi({ appDir, autoReload: false }).load();
        console.log('Strapi iniciado correctamente');
        // Buscar un usuario residente para usar como usuario principal
        const residente = await strapi.db.query('plugin::users-permissions.user').findOne({
            where: {
                role: {
                    $or: [
                        { name: 'Residente' },
                        { name: 'Administrador' },
                        { name: 'Admin' }
                    ]
                }
            },
            populate: ['role']
        });
        if (!residente) {
            console.error('No se encontró un usuario residente o administrador para usar como usuario principal');
            process.exit(1);
        }
        console.log(`\nUsando usuario principal: ${residente.username} (ID: ${residente.id})`);
        // Buscar el rol de arrendatario
        const arrendatarioRole = await strapi.db.query('plugin::users-permissions.role').findOne({
            where: { name: 'Arrendatario' }
        });
        if (!arrendatarioRole) {
            console.error('No se encontró el rol de Arrendatario');
            process.exit(1);
        }
        console.log(`\nUsando rol: ${arrendatarioRole.name} (ID: ${arrendatarioRole.id})`);
        // Preparar los datos para crear el arrendatario
        const userData = {
            ...testData,
            address: residente.address || testData.address,
            role: arrendatarioRole.id,
            usuarioPrincipal: residente.id,
            confirmed: true,
            blocked: false
        };
        console.log('\n=== DATOS PREPARADOS PARA CREAR ARRENDATARIO ===');
        console.log(JSON.stringify(userData, null, 2));
        // Intentar crear el arrendatario
        try {
            console.log('\nIntentando crear arrendatario...');
            const arrendatario = await strapi.entityService.create('plugin::users-permissions.user', {
                data: userData
            });
            console.log('\n=== ARRENDATARIO CREADO EXITOSAMENTE ===');
            console.log(`ID: ${arrendatario.id}`);
            console.log(`Username: ${arrendatario.username}`);
            console.log(`Email: ${arrendatario.email}`);
        }
        catch (error) {
            console.error('\n=== ERROR AL CREAR ARRENDATARIO ===');
            console.error(`Tipo de error: ${error.name}`);
            console.error(`Mensaje: ${error.message}`);
            // Mostrar detalles completos del error de validación
            if (error.name === 'YupValidationError' && error.details && error.details.errors) {
                console.error('\n=== DETALLES DE ERRORES DE VALIDACIÓN ===');
                error.details.errors.forEach((err, index) => {
                    console.error(`\nError ${index + 1}:`);
                    console.error(` - Path: ${err.path.join('.')}`);
                    console.error(` - Message: ${err.message}`);
                    if (err.value !== undefined) {
                        console.error(` - Value: ${JSON.stringify(err.value)}`);
                    }
                });
            }
            else {
                // Intentar mostrar más detalles del error
                console.error('\n=== DETALLES ADICIONALES DEL ERROR ===');
                for (const key in error) {
                    if (typeof error[key] !== 'function') {
                        try {
                            console.error(`${key}: ${JSON.stringify(error[key])}`);
                        }
                        catch (e) {
                            console.error(`${key}: [No se puede serializar]`);
                        }
                    }
                }
            }
        }
        // Cerrar Strapi
        await strapi.destroy();
        console.log('\nStrapi cerrado correctamente');
        process.exit(0);
    }
    catch (error) {
        console.error('Error general:', error);
        process.exit(1);
    }
}
// Ejecutar la función principal
debugArrendatario();
