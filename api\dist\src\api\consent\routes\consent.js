"use strict";
/**
 * consent router
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        // Obtener todos los consentimientos (solo admin)
        {
            method: "GET",
            path: "/consents",
            handler: "consent.find",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        // Obtener un consentimiento específico
        {
            method: "GET",
            path: "/consents/:id",
            handler: "consent.findOne",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        // Crear un nuevo consentimiento
        {
            method: "POST",
            path: "/consents",
            handler: "consent.create",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        // Obtener consentimientos del usuario actual
        {
            method: "GET",
            path: "/consents/me",
            handler: "consent.findMine",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        // Aceptar términos y condiciones (crear consentimiento)
        {
            method: "POST",
            path: "/consents/accept",
            handler: "consent.acceptTerms",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
        // Verificar si el usuario tiene consentimiento activo
        {
            method: "GET",
            path: "/consents/check",
            handler: "consent.checkConsent",
            config: {
                policies: ["global::isAuthenticated"],
            },
        },
    ],
};
