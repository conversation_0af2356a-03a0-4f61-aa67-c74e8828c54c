"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Configuración de middlewares para la aplicación
 * Versión consolidada que incluye todos los middlewares necesarios
 */
exports.default = [
    "strapi::logger",
    "strapi::errors",
    {
        name: "strapi::security",
        config: {
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    "connect-src": [
                        "'self'",
                        "https:",
                        "wss:",
                        "http://localhost:3000", // desarrollo frontend
                        "http://localhost:1337", // desarrollo backend
                        "https://ccpalcan.com", // producción frontend
                        "https://ccpc-back.up.railway.app", // producción backend
                    ],
                    "img-src": ["'self'", "data:", "blob:", "res.cloudinary.com"],
                    "media-src": ["'self'", "data:", "blob:", "res.cloudinary.com"],
                    upgradeInsecureRequests: null,
                },
            },
        },
    },
    {
        name: "strapi::cors",
        config: {
            headers: ["*"],
            origin: [
                "http://localhost:3000", // Desarrollo
                "https://ccpalcan.com", // Producción frontend
                "https://ccpc-back.up.railway.app", // Producción backend (opcional si lo usas directo)
            ],
            methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"],
            keepHeaderOnError: true,
            credentials: true,
        },
    },
    "strapi::poweredBy",
    "strapi::query",
    "strapi::session",
    "strapi::favicon",
    "strapi::public",
    {
        name: "strapi::body",
        config: {
            jsonLimit: "10mb",
            formLimit: "10mb",
            textLimit: "10mb",
            formidable: {
                maxFileSize: 200 * 1024 * 1024,
            },
        },
    },
    // Middlewares personalizados
    "global::validate-unique-block",
    "global::activity-logger",
];
