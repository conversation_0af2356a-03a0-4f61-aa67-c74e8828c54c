import { useState } from "react";

function ImageInput() {
  const [file, setFile] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  return (
    <div>
      <form
        onSubmit={async (e) => {
          e.preventDefault();

          const formData = new FormData();
          if (file) {
            formData.append("image", file);
          }

          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });
          const data = await response.json();
          setImageUrl(data.url);
        }}
      >
        <input
          type="file"
          onChange={(e) => {
            if (e.target.files && e.target.files[0]) {
              setFile(e.target.files[0]);
            }
          }}
        />
        <button>Enviar</button>
      </form>
      {imageUrl && <img src={imageUrl} alt="" />}
    </div>
  );
}

export default ImageInput;
