import { AxiosResponse } from "axios";
import { PaymentComment } from "@app/pages/AdminPages/PaymentAdminPage/types/payment.types";
import { api } from "./api";

interface StrapiData<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface StrapiResponse<T> {
  data: {
    id: number;
    attributes: T;
  }[];
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface StrapiSingleResponse<T> {
  data: {
    id: number;
    attributes: T;
  };
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export const CommentsService = {
  getComments: (paymentId: number): Promise<StrapiResponse<Omit<PaymentComment, "id">>> => {
    return api.get(`/payment-comments?filters[payment][id][$eq]=${paymentId}&sort=createdAt:desc`);
  },

  addComment: (paymentId: number, content: string): Promise<StrapiSingleResponse<Omit<PaymentComment, "id">>> => {
    return api.post("/payment-comments", {
      data: {
        content,
        payment: paymentId,
      },
    });
  },

  updateComment: (commentId: number, content: string): Promise<StrapiSingleResponse<Omit<PaymentComment, "id">>> => {
    return api.put(`/payment-comments/${commentId}`, {
      data: {
        content,
      },
    });
  },

  deleteComment: (commentId: number): Promise<AxiosResponse> => {
    return api.delete(`/payment-comments/${commentId}`);
  },
};
