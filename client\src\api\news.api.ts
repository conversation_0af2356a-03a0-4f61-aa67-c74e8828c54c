import { newsTags } from "constants/newsTags";
import { IHashTag } from "@app/pages/news-feed-page/components/BaseHashTag/BaseHashTag";

const avatar1 = import.meta.env.VITE_ASSETS_BUCKET + "/avatars/avatar1.webp";
const avatar2 = import.meta.env.VITE_ASSETS_BUCKET + "/avatars/avatar2.webp";
const avatar3 = import.meta.env.VITE_ASSETS_BUCKET + "/avatars/avatar3.webp";
const avatar4 = import.meta.env.VITE_ASSETS_BUCKET + "/avatars/avatar4.webp";

export interface Post {
  avatarUrl: string;
  author: string;
  title: string;
  date: number;
  text: string;
  img: string;
  tags: Array<IHashTag>;
}

const { announcements, events, maintenance, community } = newsTags;

export const getNews = (): Promise<Post[]> => {
  return new Promise((res) => {
    setTimeout(() => {
      res([
        {
          avatarUrl: avatar1,
          author: "<PERSON><PERSON> <PERSON>",
          title: "Reunión de la Asamblea General",
          date: 1576789200000,
          text: "Se llevará a cabo una reunión de la Asamblea General para discutir asuntos importantes relacionados con el condominio. ¡Todos los residentes están invitados a participar!",
          img: "https://cdn.pixabay.com/photo/2023/02/26/11/50/leadership-7815553_640.jpg",
          tags: [announcements, community],
        },
        {
          avatarUrl: avatar2,
          author: "Jordan Howard",
          title: "Evento de Networking en la Piscina",
          date: 1575925200000,
          text: "Ven y únete a nosotros en un evento de networking junto a la piscina. Conoce a tus vecinos, comparte ideas y disfruta de un buen momento juntos.",
          img: "https://cdn.pixabay.com/photo/2016/11/22/21/43/woman-1850709_640.jpg",
          tags: [announcements, maintenance, community],
        },
        {
          avatarUrl: avatar3,
          author: "Jack Hannah",
          title: "Recordatorio de Mantenimiento",
          date: 1575147600000,
          text: "Se realizará un mantenimiento programado en todas las áreas comunes del condominio. Agradecemos tu cooperación y comprensión durante este periodo.",
          img: "https://media.istockphoto.com/id/1324242582/es/foto/jardiner%C3%ADa-sin-residuos.jpg?s=612x612&w=0&k=20&c=8JsoNLxmmc3Ghfi2Un0do-Vg2DeIdu1O6IOUlMO2b1s=",
          tags: [maintenance],
        },
        {
          avatarUrl: avatar4,
          title: "Celebremos el Día de la Comunidad",
          author: "Colin Falls",
          date: 1572555600000,
          text: "Unámonos para celebrar el Día de la Comunidad. Habrá actividades para toda la familia, comida, música y diversión garantizada. ¡Te esperamos!",
          img: "https://www.w3schools.com/howto/img_nature_wide.jpg",
          tags: [events, maintenance, community],
        },
      ]);
    }, 1000);
  });
};
