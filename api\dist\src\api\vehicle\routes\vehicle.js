"use strict";
/**
 * vehicle router
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: "POST",
            path: "/vehicles",
            handler: "api::vehicle.vehicle.create",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/vehicles/:id",
            handler: "api::vehicle.vehicle.update",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "DELETE",
            path: "/vehicles/:id",
            handler: "api::vehicle.vehicle.delete",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/vehicles/owner/:ownerId",
            handler: "api::vehicle.vehicle.findByOwner",
            config: {
                auth: false,
            },
        },
        {
            method: "GET",
            path: "/vehicles",
            handler: "api::vehicle.vehicle.find",
            config: {
                auth: false,
            },
        },
    ],
};
