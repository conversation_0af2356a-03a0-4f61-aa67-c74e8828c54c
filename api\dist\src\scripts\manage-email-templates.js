"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// Variable para controlar si el script se ejecuta automáticamente al iniciar Strapi
const SKIP_AUTO_EXECUTION = true; // Cambia a false si quieres que se ejecute siempre
// Función para forzar la salida de logs
const log = (...args) => {
    const message = args
        .map((arg) => typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg))
        .join(" ");
    process.stdout.write(`[${new Date().toISOString()}] ${message}\n`);
};
// Rutas de los archivos de plantilla
const TEMPLATES_DIR = path_1.default.join(process.cwd(), "config", "email-templates");
const RESET_PASSWORD_PATH = path_1.default.join(TEMPLATES_DIR, "reset-password.html");
const EMAIL_CONFIRMATION_PATH = path_1.default.join(TEMPLATES_DIR, "email-confirmation.html");
// Plantilla de restablecimiento de contraseña
const RESET_PASSWORD_HTML = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Restablecer contraseña</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .header {
      text-align: center;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
      margin-bottom: 20px;
    }
    .header img {
      max-width: 150px;
      margin-bottom: 10px;
    }
    .header h2 {
      color: #2c3e50;
      margin: 0;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #3498db;
      color: #ffffff !important;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
  <div class="header">
    <img src="https://res.cloudinary.com/dhcpss5pg/image/upload/v1748288029/ydeferxz12snop3qu5zu.png" alt="CCPALCAN Logo">
    <h2>Restablecer contraseña</h2>
  </div>

  <p>Hola <%= user.username %>,</p>
  <p>Hemos recibido una solicitud para restablecer la contraseña de tu cuenta en <strong>CCPALCAN</strong>.</p>
  <p>Haz clic en el siguiente botón para crear una nueva contraseña de manera segura:</p>

  <div style="text-align: center; margin: 25px 0;">
    <a href="<%= URL %>?code=<%= TOKEN %>" class="button">Restablecer contraseña</a>
  </div>

  <p>Si no solicitaste este cambio, puedes ignorar este mensaje. Tu contraseña actual permanecerá sin modificaciones.</p>

  <p>Si tienes dudas o necesitas asistencia, por favor comunícate con nuestro equipo de soporte.</p>

  <div class="footer">
    <p>Este es un correo automático, por favor no respondas a este mensaje.</p> 
    <p>© 2025 CCPALCAN - Todos los derechos reservados</p>
  </div>
  </div>
</body>
</html>`;
// Plantilla de confirmación de correo electrónico
const EMAIL_CONFIRMATION_HTML = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Confirmación de correo electrónico</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .header {
      text-align: center;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
      margin-bottom: 20px;
    }
    .header img {
      max-width: 150px;
      margin-bottom: 10px;
    }
    .header h2 {
      color: #2c3e50;
      margin: 0;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #3498db;
      color: #ffffff !important;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
  <div class="header">
    <img src="https://res.cloudinary.com/dhcpss5pg/image/upload/v1748288029/ydeferxz12snop3qu5zu.png" alt="CCPALCAN Logo">
    <h2>Confirma tu correo electrónico</h2>
  </div>

  <p>Hola <%= user.username %>,</p>
  <p>¡Gracias por registrarte en <strong>CCPALCAN</strong>! Para completar tu registro y activar tu cuenta, por favor confirma tu dirección de correo electrónico haciendo clic en el siguiente botón:</p>

  <div style="text-align: center; margin: 25px 0;">
    <a href="<%= URL %>?confirmation=<%= CODE %>" class="button">Confirmar mi correo</a>
  </div>

  <p>Si no creaste esta cuenta, puedes ignorar este mensaje o contactar a nuestro equipo de soporte si crees que se trata de un error.</p>

  <p>¡Esperamos que disfrutes de todos los beneficios de ser parte de nuestra comunidad!</p>

  <div class="footer">
    <p>Este es un correo automático, por favor no respondas a este mensaje.</p> 
    <p>© 2025 CCPALCAN - Todos los derechos reservados</p>
  </div>
  </div>
</body>
</html>`;
// Función para leer el contenido de una plantilla HTML
const readTemplateContent = (filePath) => {
    try {
        return fs_1.default.readFileSync(filePath, "utf8");
    }
    catch (error) {
        log(`❌ Error al leer la plantilla ${filePath}:`, error);
        return "";
    }
};
// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent) => {
    // Extraer solo el contenido relevante para el mensaje
    // En este caso, vamos a extraer todo lo que está dentro del body
    const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
        // Simplificar el contenido para la interfaz de Strapi
        let content = bodyMatch[1].trim();
        // Reemplazar las variables de plantilla al formato que espera Strapi
        content = content.replace(/<%=\s*user\.username\s*%>/g, "<%= user.username %>");
        content = content.replace(/<%=\s*URL\s*%>/g, "<%= URL %>");
        content = content.replace(/<%=\s*TOKEN\s*%>/g, "<%= TOKEN %>");
        content = content.replace(/<%=\s*CODE\s*%>/g, "<%= CODE %>");
        return content;
    }
    return "";
};
// Función principal para configurar las plantillas
const setupEmailTemplates = async () => {
    try {
        log("🚀 INICIANDO: Configuración de plantillas de correo");
        // Crear directorio de plantillas si no existe
        if (!fs_1.default.existsSync(TEMPLATES_DIR)) {
            log("📂 Creando directorio de plantillas...");
            fs_1.default.mkdirSync(TEMPLATES_DIR, { recursive: true });
        }
        // Crear plantilla de restablecimiento de contraseña
        log("\n📝 Creando plantilla de restablecimiento de contraseña...");
        fs_1.default.writeFileSync(RESET_PASSWORD_PATH, RESET_PASSWORD_HTML);
        log(`✅ Plantilla creada en: ${RESET_PASSWORD_PATH}`);
        // Crear plantilla de confirmación de correo
        log("\n📝 Creando plantilla de confirmación de correo...");
        fs_1.default.writeFileSync(EMAIL_CONFIRMATION_PATH, EMAIL_CONFIRMATION_HTML);
        log(`✅ Plantilla creada en: ${EMAIL_CONFIRMATION_PATH}`);
        log("\n✨ Todas las plantillas se han creado correctamente");
    }
    catch (error) {
        log("❌ ERROR: Fallo en la configuración de plantillas");
        log("🔧 Detalles del error:", error.message);
        log("📌 Stack:", error.stack);
        throw error;
    }
};
// Función para actualizar las plantillas en la interfaz de Strapi
const updateEmailTemplatesUI = async ({ strapi }) => {
    try {
        log("🔄 INICIANDO: Actualización de plantillas en la interfaz de Strapi");
        // Verificar que las plantillas existan
        if (!fs_1.default.existsSync(RESET_PASSWORD_PATH) || !fs_1.default.existsSync(EMAIL_CONFIRMATION_PATH)) {
            log("❌ Error: Las plantillas HTML no existen en el directorio config/email-templates/");
            log("📋 Ejecutando primero la creación de plantillas...");
            await setupEmailTemplates();
        }
        // Leer las plantillas
        const resetPasswordHtml = readTemplateContent(RESET_PASSWORD_PATH);
        const emailConfirmationHtml = readTemplateContent(EMAIL_CONFIRMATION_PATH);
        // Extraer el contenido del mensaje
        const resetPasswordMessage = extractMessageBody(resetPasswordHtml);
        const emailConfirmationMessage = extractMessageBody(emailConfirmationHtml);
        // Actualizar las plantillas en la base de datos de Strapi
        log("📝 Actualizando plantilla de restablecimiento de contraseña en Strapi...");
        try {
            // En Strapi v5, usamos el servicio de email directamente
            await strapi.plugin("email").service("email").updateTemplate("reset-password", {
                subject: "Restablecer contraseña",
                message: resetPasswordMessage,
            });
            log("✅ Plantilla de restablecimiento de contraseña actualizada correctamente");
        }
        catch (error) {
            log("❌ Error al actualizar plantilla de restablecimiento de contraseña:", error.message);
        }
        log("📝 Actualizando plantilla de confirmación de correo en Strapi...");
        try {
            // En Strapi v5, usamos el servicio de email directamente
            await strapi.plugin("email").service("email").updateTemplate("email-confirmation", {
                subject: "Confirmación de cuenta",
                message: emailConfirmationMessage,
            });
            log("✅ Plantilla de confirmación de correo actualizada correctamente");
        }
        catch (error) {
            log("❌ Error al actualizar plantilla de confirmación de correo:", error.message);
        }
        log("✅ Plantillas actualizadas correctamente en la interfaz de Strapi");
        log("🔍 Ahora deberías poder ver las plantillas actualizadas en la interfaz de administración");
        log("📋 Nota: Es posible que necesites refrescar la página de administración para ver los cambios");
    }
    catch (error) {
        log("❌ ERROR: Fallo en la actualización de plantillas");
        log("🔧 Detalles del error:", error.message);
        log("📌 Stack:", error.stack);
    }
};
// Determinar si el script se está ejecutando como parte del proceso de seed
const isRunningAsSeed = () => {
    // Verificar si se está ejecutando como script independiente
    if (require.main === module) {
        return true;
    }
    // Verificar si se está ejecutando como parte del proceso de seed
    const stackTrace = new Error().stack || "";
    return (stackTrace.includes("seed") ||
        process.argv.some((arg) => arg.includes("manage-email-templates")));
};
// Exportar la función para ser usada por Strapi
exports.default = async ({ strapi }) => {
    // Solo ejecutar cuando se llama explícitamente como script o durante el seed
    // o cuando SKIP_AUTO_EXECUTION está configurado como false
    if (!SKIP_AUTO_EXECUTION || isRunningAsSeed()) {
        // Primero configuramos las plantillas HTML en disco
        await setupEmailTemplates();
        // Luego actualizamos las plantillas en la interfaz de Strapi
        await updateEmailTemplatesUI({ strapi });
        log("\n📋 Proceso completo de gestión de plantillas finalizado con éxito");
    }
    else {
        // Si se está ejecutando como parte del ciclo de vida de Strapi, no hacer nada
        console.log("[Email Templates] Omitiendo gestión automática de plantillas durante el inicio del servidor");
    }
};
