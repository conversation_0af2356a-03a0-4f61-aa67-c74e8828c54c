/**
 * Script para depurar errores de validación en Strapi
 *
 * Este script intenta validar manualmente un objeto de usuario para identificar
 * exactamente qué campos están causando errores de validación.
 *
 * Ejecutar con: node scripts/debug-validation.js
 */
const Strapi = require('@strapi/strapi');
const path = require('path');
const { yup } = require('@strapi/utils');
// Datos de prueba para validar (usar los mismos datos que estás enviando desde el frontend)
const testData = {
    username: "nyrynucu",
    email: "<EMAIL>",
    password: "Pa$$w0rd!",
    address: "A01",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    phone: "+****************",
    document: "Laborum Ullamco ten",
    role: 3, // Este ID se actualizará automáticamente con el ID real del rol
    confirmed: true,
    blocked: false
};
async function debugValidation() {
    try {
        // Iniciar Strapi
        const appDir = path.resolve(process.cwd());
        const strapi = await <PERSON>rap<PERSON>({ appDir, autoReload: false }).load();
        console.log('Strapi iniciado correctamente');
        // Obtener el esquema de validación para usuarios
        const userModel = strapi.getModel('plugin::users-permissions.user');
        console.log('\n=== ESQUEMA DEL MODELO DE USUARIO ===');
        console.log('Atributos disponibles:');
        Object.keys(userModel.attributes).forEach(attr => {
            console.log(` - ${attr}: ${userModel.attributes[attr].type}`);
        });
        // Buscar el rol de arrendatario
        const arrendatarioRole = await strapi.db.query('plugin::users-permissions.role').findOne({
            where: { name: 'Arrendatario' }
        });
        if (!arrendatarioRole) {
            console.error('No se encontró el rol de Arrendatario');
            process.exit(1);
        }
        console.log(`\nUsando rol: ${arrendatarioRole.name} (ID: ${arrendatarioRole.id})`);
        testData.role = arrendatarioRole.id;
        // Buscar un usuario residente para usar como usuario principal
        const residente = await strapi.db.query('plugin::users-permissions.user').findOne({
            where: {
                role: {
                    $or: [
                        { name: 'Residente' },
                        { name: 'Administrador' },
                        { name: 'Admin' }
                    ]
                }
            }
        });
        if (residente) {
            console.log(`\nUsando usuario principal: ${residente.username} (ID: ${residente.id})`);
            testData.usuarioPrincipal = residente.id;
        }
        // Intentar validar manualmente los datos
        console.log('\n=== VALIDACIÓN MANUAL DE CAMPOS ===');
        // Validar username
        try {
            await yup.string().required().min(3).validate(testData.username);
            console.log('✅ username: Válido');
        }
        catch (error) {
            console.error('❌ username:', error.message);
        }
        // Validar email
        try {
            await yup.string().email().required().validate(testData.email);
            console.log('✅ email: Válido');
        }
        catch (error) {
            console.error('❌ email:', error.message);
        }
        // Validar password
        try {
            await yup.string().min(6).required().validate(testData.password);
            console.log('✅ password: Válido');
        }
        catch (error) {
            console.error('❌ password:', error.message);
        }
        // Validar firstName
        try {
            await yup.string().required().validate(testData.firstName);
            console.log('✅ firstName: Válido');
        }
        catch (error) {
            console.error('❌ firstName:', error.message);
        }
        // Validar lastName
        try {
            await yup.string().required().validate(testData.lastName);
            console.log('✅ lastName: Válido');
        }
        catch (error) {
            console.error('❌ lastName:', error.message);
        }
        // Intentar crear el usuario directamente con la API de Strapi
        console.log('\n=== INTENTANDO CREAR USUARIO DIRECTAMENTE ===');
        try {
            // Primero, verificar si el usuario ya existe
            const existingUser = await strapi.db.query('plugin::users-permissions.user').findOne({
                where: {
                    $or: [
                        { username: testData.username },
                        { email: testData.email }
                    ]
                }
            });
            if (existingUser) {
                console.error(`⚠️ Ya existe un usuario con username "${testData.username}" o email "${testData.email}"`);
            }
            else {
                console.log('Datos a enviar:', testData);
                // Intentar crear el usuario con entityService
                try {
                    const user = await strapi.entityService.create('plugin::users-permissions.user', {
                        data: testData
                    });
                    console.log('✅ Usuario creado exitosamente:', user.id);
                }
                catch (createError) {
                    console.error('❌ Error al crear usuario con entityService:');
                    console.error(createError);
                    // Mostrar detalles específicos de errores de validación
                    if (createError.name === 'ValidationError' && createError.details && createError.details.errors) {
                        console.log('\n=== ERRORES DE VALIDACIÓN DETALLADOS ===');
                        createError.details.errors.forEach((err, index) => {
                            console.error(`Error ${index + 1}:`);
                            console.error(` - Path: ${Array.isArray(err.path) ? err.path.join('.') : err.path}`);
                            console.error(` - Message: ${err.message}`);
                            if (err.value !== undefined) {
                                console.error(` - Value:`, err.value);
                            }
                        });
                    }
                }
            }
        }
        catch (error) {
            console.error('Error general:', error);
        }
        // Cerrar Strapi
        await strapi.destroy();
        console.log('\nStrapi cerrado correctamente');
        process.exit(0);
    }
    catch (error) {
        console.error('Error general:', error);
        process.exit(1);
    }
}
// Ejecutar la función principal
debugValidation();
