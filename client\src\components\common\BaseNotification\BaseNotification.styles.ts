import styled from "styled-components";
import { Button } from "antd";
import type { NotificationType } from "./BaseNotification";

const getColorByType = (type: NotificationType) => {
  switch (type) {
    case "success":
      return "var(--success-color)";
    case "warning":
      return "var(--warning-color)";
    case "error":
      return "var(--error-color)";
    case "info":
    default:
      return "var(--primary-color)";
  }
};

export const Wrapper = styled.div<{ $type: NotificationType; $read: boolean }>`
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: ${({ $read }) =>
    $read ? "transparent" : "rgba(0, 0, 0, 0.02)"};

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
`;

export const NotificationContent = styled.div`
  flex: 1;
  margin-right: 16px;
`;

export const Title = styled.div<{ $type: NotificationType }>`
  font-weight: 500;
  color: ${({ $type }) => getColorByType($type)};
  margin-bottom: 4px;
`;

export const Description = styled.div`
  color: var(--text-main-color);
  font-size: 14px;
`;

export const Timestamp = styled.div`
  color: var(--text-light-color);
  font-size: 12px;
  margin-top: 4px;
`;

export const DeleteButton = styled(Button)`
  color: var(--text-light-color);
  
  &:hover {
    color: var(--text-main-color);
  }
`;