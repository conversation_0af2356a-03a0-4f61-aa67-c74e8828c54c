import { Card, CardProps } from "antd";
import { useResponsive } from "@app/hooks/useResponsive";
import * as S from "./BaseCard.styles";
import type { WidthCategories } from "@app/styles/themes/types";
import { ReactNode } from "react";

export interface BaseCardProps {
  className?: string;
  padding?: string | number | readonly [number, number];
  autoHeight?: boolean;
  children?: ReactNode;
  size?: CardProps["size"];
  bordered?: boolean;
  title?: string | ReactNode;
  actions?: ReactNode[];
  extra?: ReactNode;
  onClick?: () => void;
  hoverable?: boolean;
  cover?: ReactNode;
  style?: React.CSSProperties;
  loading?: boolean;
}

export const defaultPaddings = {
  xs: [30, 16],
  md: [40, 30],
  xl: [50, 60],
} as const satisfies WidthCategories;

export const BaseCard: React.FC<BaseCardProps> = ({
  className,
  padding,
  size,
  autoHeight = true,
  children,
  bordered,
  title,
  actions,
  extra,
  onClick,
  hoverable,
  cover,
  style,
  loading,
  ...props
}) => {
  const { isTablet, breakpoint } = useResponsive();

  return (
    <S.Card
      size={size ?? (isTablet ? "default" : "small")}
      className={className}
      variant={bordered ? "outlined" : "borderless"}
      title={title}
      actions={actions}
      extra={extra}
      $padding={
        padding || padding === 0 ? padding : defaultPaddings[breakpoint]
      }
      $autoHeight={autoHeight}
      hoverable={hoverable}
      onClick={onClick}
      style={style}
      loading={loading}
      {...props}
    >
      {cover}
      {children}
    </S.Card>
  );
};
