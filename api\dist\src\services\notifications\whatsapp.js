"use strict";
/**
 * WhatsApp notification service using SendGrid
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mail_1 = __importDefault(require("@sendgrid/mail"));
exports.default = ({ strapi }) => ({
    initialize() {
        const config = strapi.config.get('sendgrid');
        if (!(config === null || config === void 0 ? void 0 : config.apiKey)) {
            console.warn('SendGrid configuration is missing. WhatsApp notifications will be disabled.');
            return;
        }
        mail_1.default.setApiKey(config.apiKey);
    },
    async send(to, message) {
        const config = strapi.config.get('sendgrid');
        try {
            const msg = {
                to,
                from: config.fromNumber,
                subject: 'WhatsApp Message', // Required by SendGrid API
                text: message,
                channel: 'whatsapp' // Type assertion to match MailDataRequired
            };
            const result = await mail_1.default.send(msg);
            return result;
        }
        catch (error) {
            console.error('Error sending WhatsApp message:', error);
            throw error;
        }
    }
});
