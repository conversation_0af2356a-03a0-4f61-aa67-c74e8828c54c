'use strict';
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const axios = require('axios');
// Cargar variables de entorno
dotenv.config();
// Configuración
const ADMIN_JWT = process.env.STRAPI_ADMIN_TOKEN;
const API_URL = process.env.API_URL || 'http://localhost:1337';
const TEMPLATES_DIR = path.join(process.cwd(), 'config', 'email-templates');
// Función para leer el contenido de una plantilla HTML
const readTemplateContent = (filePath) => {
    try {
        return fs.readFileSync(filePath, 'utf8');
    }
    catch (error) {
        console.error(`Error al leer la plantilla ${filePath}:`, error);
        return '';
    }
};
// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent) => {
    // Extraer solo el contenido relevante para el mensaje
    const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
        // Simplificar el contenido para la interfaz de Strapi
        let content = bodyMatch[1].trim();
        // Reemplazar las variables de plantilla al formato que espera Strapi
        content = content.replace(/<%=\s*user\.username\s*%>/g, "<%= user.username %>");
        content = content.replace(/<%=\s*URL\s*%>/g, "<%= URL %>");
        content = content.replace(/<%=\s*TOKEN\s*%>/g, "<%= TOKEN %>");
        content = content.replace(/<%=\s*CODE\s*%>/g, "<%= CODE %>");
        return content;
    }
    return '';
};
// Función para obtener el token de administrador
const getAdminToken = async () => {
    if (ADMIN_JWT) {
        return ADMIN_JWT;
    }
    try {
        console.log('Obteniendo token de administrador...');
        const response = await axios.post(`${API_URL}/admin/login`, {
            email: process.env.ADMIN_EMAIL || '<EMAIL>',
            password: process.env.ADMIN_PASSWORD || 'Password123',
        });
        return response.data.data.token;
    }
    catch (error) {
        console.error('Error al obtener token de administrador:', error.message);
        process.exit(1);
    }
};
// Función para actualizar una plantilla en la interfaz de Strapi
const updateTemplate = async (token, templateKey, subject, message) => {
    try {
        console.log(`Actualizando plantilla ${templateKey}...`);
        // Obtener la configuración actual de la plantilla
        const getResponse = await axios.get(`${API_URL}/admin/email-templates/${templateKey}`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        // Actualizar la plantilla
        await axios.put(`${API_URL}/admin/email-templates/${templateKey}`, {
            subject,
            message,
        }, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        console.log(`✅ Plantilla ${templateKey} actualizada correctamente`);
    }
    catch (error) {
        console.error(`Error al actualizar plantilla ${templateKey}:`, error.message);
        if (error.response) {
            console.error('Detalles:', error.response.data);
        }
    }
};
// Función principal
const syncTemplates = async () => {
    try {
        console.log('🔄 INICIANDO: Sincronización de plantillas con la interfaz de Strapi');
        // Verificar que las plantillas existan
        const resetPasswordPath = path.join(TEMPLATES_DIR, 'reset-password.html');
        const emailConfirmationPath = path.join(TEMPLATES_DIR, 'email-confirmation.html');
        if (!fs.existsSync(resetPasswordPath) || !fs.existsSync(emailConfirmationPath)) {
            console.error('❌ Error: Las plantillas HTML no existen en el directorio config/email-templates/');
            console.log('📋 Ejecuta primero el script setup-email-templates.js para crear las plantillas');
            return;
        }
        // Leer las plantillas
        const resetPasswordHtml = readTemplateContent(resetPasswordPath);
        const emailConfirmationHtml = readTemplateContent(emailConfirmationPath);
        // Extraer el contenido del mensaje
        const resetPasswordMessage = extractMessageBody(resetPasswordHtml);
        const emailConfirmationMessage = extractMessageBody(emailConfirmationHtml);
        // Obtener token de administrador
        const token = await getAdminToken();
        // Actualizar las plantillas en la interfaz de Strapi
        await updateTemplate(token, 'reset-password', 'Reset password', resetPasswordMessage);
        await updateTemplate(token, 'email-confirmation', 'Account confirmation', emailConfirmationMessage);
        console.log('✅ Plantillas sincronizadas correctamente con la interfaz de Strapi');
        console.log('🔍 Ahora deberías poder ver las plantillas actualizadas en la interfaz de administración');
        console.log('📋 Nota: Es posible que necesites refrescar la página de administración para ver los cambios');
    }
    catch (error) {
        console.error('❌ ERROR: Fallo en la sincronización de plantillas');
        console.error('🔧 Detalles del error:', error.message);
        console.error('📌 Stack:', error.stack);
    }
};
// Ejecutar la función principal
syncTemplates();
