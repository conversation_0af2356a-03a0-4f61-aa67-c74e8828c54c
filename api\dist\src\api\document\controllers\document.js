"use strict";
/**
 *  document controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::document.document", ({ strapi }) => ({
    /**
     * Find documents with pagination
     * @param {StrapiContext} ctx - The Koa context
     */
    async find(ctx) {
        try {
            // Get the query parameters
            const { query } = ctx;
            // Execute the query
            const { results, pagination } = await strapi
                .service("api::document.document")
                .find({
                ...query,
                populate: ["file", "uploadedBy"],
            });
            // Return the sanitized data
            const sanitizedResults = await this.sanitizeOutput(results, ctx);
            return {
                data: sanitizedResults,
                meta: { pagination },
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    /**
     * Find a specific document
     * @param {StrapiContext} ctx - The Koa context
     */
    async findOne(ctx) {
        try {
            const { id } = ctx.params;
            const { query } = ctx;
            const document = await strapi
                .service("api::document.document")
                .findOne(id, {
                ...query,
                populate: ["file", "uploadedBy"],
            });
            if (!document) {
                return ctx.notFound("Document not found");
            }
            const sanitizedDocument = await this.sanitizeOutput(document, ctx);
            return {
                data: sanitizedDocument,
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    /**
     * Create a new document
     * @param {StrapiContext} ctx - The Koa context
     */
    async create(ctx) {
        var _a, _b;
        try {
            const { user } = ctx.state;
            // Intentar parsear los datos
            let parsedData;
            try {
                const { data } = ctx.request.body;
                parsedData = typeof data === "string" ? JSON.parse(data) : data;
            }
            catch (parseError) {
                console.error("Error al parsear datos:", parseError);
                return ctx.badRequest("Error al parsear los datos del documento");
            }
            // Validar datos requeridos
            if (!parsedData || !parsedData.name || !parsedData.type) {
                console.error("Datos incompletos:", parsedData);
                return ctx.badRequest("Faltan campos requeridos: name y type son obligatorios");
            }
            // Procesar archivos
            const files = ((_a = ctx.request.files) === null || _a === void 0 ? void 0 : _a["files.file"]) || ((_b = ctx.request.files) === null || _b === void 0 ? void 0 : _b.files);
            if (!files) {
                console.error("No se encontraron archivos");
                return ctx.badRequest("No se ha proporcionado ningún archivo");
            }
            // Add the current user as the uploader
            const documentData = {
                ...parsedData,
                uploadedBy: user === null || user === void 0 ? void 0 : user.id,
            };
            // Create the document
            const document = await strapi.service("api::document.document").create({
                data: documentData,
            });
            // Procesar el archivo con el servicio de upload
            try {
                const uploadedFiles = await strapi.plugins.upload
                    .service("upload")
                    .upload({
                    data: {
                        ref: "api::document.document",
                        refId: document.id,
                        field: "file",
                    },
                    files,
                });
            }
            catch (uploadError) {
                console.error("Error al subir archivo:", uploadError);
                // No retornamos error aquí para no interrumpir el flujo
            }
            // Fetch the created document with populated relations
            try {
                const createdDocument = await strapi
                    .service("api::document.document")
                    .findOne(document.id, {
                    populate: ["file", "uploadedBy"],
                });
                // Si el documento no se encuentra, devolvemos el documento original
                if (!createdDocument) {
                    console.warn("No se pudo obtener el documento con relaciones, usando documento original");
                    const sanitizedOriginal = await this.sanitizeOutput(document, ctx);
                    return {
                        data: sanitizedOriginal,
                    };
                }
                const sanitizedDocument = await this.sanitizeOutput(createdDocument, ctx);
                return {
                    data: sanitizedDocument,
                };
            }
            catch (findError) {
                console.error("Error al obtener documento con relaciones:", findError);
                // En caso de error, devolvemos el documento original
                const sanitizedOriginal = await this.sanitizeOutput(document, ctx);
                return {
                    data: sanitizedOriginal,
                };
            }
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    /**
     * Update a document
     * @param {StrapiContext} ctx - The Koa context
     */
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const { data } = ctx.request.body;
            // Update the document
            const document = await strapi
                .service("api::document.document")
                .update(id, {
                data,
            });
            // Fetch the updated document with populated relations
            const updatedDocument = await strapi
                .service("api::document.document")
                .findOne(id, {
                populate: ["file", "uploadedBy"],
            });
            const sanitizedDocument = await this.sanitizeOutput(updatedDocument, ctx);
            return {
                data: sanitizedDocument,
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    /**
     * Delete a document
     * @param {StrapiContext} ctx - The Koa context
     */
    async delete(ctx) {
        try {
            // Obtener el ID y asegurarse de que sea un número
            let { id } = ctx.params;
            // Convertir a número si es posible
            if (typeof id === "string") {
                id = parseInt(id, 10);
                if (isNaN(id)) {
                    return ctx.badRequest("ID inválido");
                }
            }
            // Buscar todos los documentos para depuración
            const allDocs = await strapi.entityService.findMany("api::document.document", {
                fields: ["id", "name", "type"],
            });
            // Intentar obtener el documento usando entityService directamente
            const document = (await strapi.entityService.findOne("api::document.document", id, {
                populate: ["file"],
            })); // Usar 'as any' para evitar problemas de tipado
            if (!document) {
                return ctx.notFound("Document not found");
            }
            // Obtener el ID del archivo si existe
            let fileId = null;
            if (document.file) {
                fileId = document.file.id;
            }
            // Eliminar el documento usando entityService
            const deletedDocument = await strapi.entityService.delete("api::document.document", id);
            // Eliminar el archivo asociado si existe
            if (fileId) {
                await strapi.plugins.upload.services.upload.remove({
                    id: fileId,
                });
            }
            else {
            }
            const sanitizedDocument = await this.sanitizeOutput(deletedDocument, ctx);
            return {
                data: sanitizedDocument,
            };
        }
        catch (error) {
            console.error("Error al eliminar documento:", error);
            ctx.throw(500, error);
        }
    },
}));
