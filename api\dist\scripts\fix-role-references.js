/**
 * Script para corregir referencias al modelo de roles en Strapi
 *
 * Este script busca y reemplaza todas las referencias incorrectas al modelo de roles
 * en los archivos de la aplicación.
 */
const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
// Configuración
const rootDir = path.resolve(__dirname, '..');
const searchPattern = 'plugin::users-permissions.role';
const replacement = 'plugin::users-permissions.role';
// Función para buscar archivos recursivamente
async function findFiles(dir, pattern) {
    let results = [];
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
            if (!entry.name.startsWith('node_modules') && !entry.name.startsWith('.git')) {
                const subResults = await findFiles(fullPath, pattern);
                results = results.concat(subResults);
            }
        }
        else if (entry.isFile() && (entry.name.endsWith('.js') ||
            entry.name.endsWith('.json') ||
            entry.name.endsWith('.ts'))) {
            try {
                const content = await fs.readFile(fullPath, 'utf8');
                if (content.includes(pattern)) {
                    results.push(fullPath);
                }
            }
            catch (error) {
                console.error(`Error al leer el archivo ${fullPath}:`, error.message);
            }
        }
    }
    return results;
}
// Función para reemplazar el texto en un archivo
async function replaceInFile(filePath, searchText, replacementText) {
    try {
        const content = await fs.readFile(filePath, 'utf8');
        const newContent = content.replace(new RegExp(searchText, 'g'), replacementText);
        if (content !== newContent) {
            await fs.writeFile(filePath, newContent, 'utf8');
            return true;
        }
        return false;
    }
    catch (error) {
        console.error(`Error al modificar el archivo ${filePath}:`, error.message);
        return false;
    }
}
// Función principal
async function main() {
    console.log('🔍 Buscando archivos con referencias incorrectas al modelo de roles...');
    try {
        const files = await findFiles(rootDir, searchPattern);
        if (files.length === 0) {
            console.log('✅ No se encontraron archivos con referencias incorrectas.');
            // Buscar referencias al modelo de roles para verificar
            console.log('🔍 Verificando referencias al modelo de roles...');
            // Ejecutar comando grep para buscar referencias
            exec(`cd "${rootDir}" && grep -r "users-permissions-role" --include="*.js" --include="*.json" --include="*.ts" .`, (error, stdout, stderr) => {
                if (error) {
                    if (error.code === 1) {
                        // grep devuelve código 1 cuando no encuentra coincidencias
                        console.log('✅ No se encontraron referencias al modelo de roles con formato incorrecto.');
                    }
                    else {
                        console.error('❌ Error al ejecutar grep:', error.message);
                    }
                    return;
                }
                if (stderr) {
                    console.error('❌ Error en grep:', stderr);
                    return;
                }
                console.log('⚠️ Se encontraron posibles referencias al modelo de roles:');
                console.log(stdout);
            });
            return;
        }
        console.log(`🔧 Se encontraron ${files.length} archivos con referencias incorrectas:`);
        files.forEach(file => console.log(`  - ${path.relative(rootDir, file)}`));
        let modifiedCount = 0;
        for (const file of files) {
            const modified = await replaceInFile(file, searchPattern, replacement);
            if (modified) {
                modifiedCount++;
                console.log(`✅ Corregido: ${path.relative(rootDir, file)}`);
            }
        }
        console.log(`\n✨ Proceso completado. Se modificaron ${modifiedCount} archivos.`);
        console.log('🔄 Reinicia el servidor Strapi para aplicar los cambios.');
    }
    catch (error) {
        console.error('❌ Error:', error.message);
    }
}
// Ejecutar el script
main();
