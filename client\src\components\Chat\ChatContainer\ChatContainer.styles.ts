/**
 * Estilos para el componente ChatContainer
 */

import styled, { css } from 'styled-components';

interface ConnectionStatusProps {
  $isConnected: boolean;
}

export const ChatContainer = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.background};
  overflow: hidden;
`;

export const ChatMainHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  background-color: ${({ theme }) => theme.background};
  z-index: 10;
  
  .ant-typography {
    margin: 0;
    color: ${({ theme }) => theme.textMain};
  }
`;

export const ConnectionStatus = styled.div<ConnectionStatusProps>`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  ${({ $isConnected, theme }) => $isConnected ? css`
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  ` : css`
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
    animation: pulse 2s infinite;
  `}

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
`;

export const EmptyState = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  
  .ant-typography {
    color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  }
  
  p {
    color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
    margin-top: 8px;
  }
`;

export const ChatLayout = styled.div`
  flex: 1;
  display: flex;
  min-height: 0;
`;

export const ChannelSidebar = styled.div`
  width: 280px;
  border-right: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#fafafa' : '#141414'};
  display: flex;
  flex-direction: column;
`;

export const ChatMain = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background-color: ${({ theme }) => theme.background};
`;

export const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.background}80;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

export const QuickActions = styled.div`
  display: flex;
  gap: 8px;
  
  .ant-btn {
    border: none;
    box-shadow: none;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#2a2a2a'};
    }
  }
`;

export const SearchResults = styled.div`
  max-height: 300px;
  overflow-y: auto;
  margin-top: 16px;
  
  .search-result-item {
    padding: 8px 12px;
    border: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f5f5f5' : '#1f1f1f'};
      border-color: ${({ theme }) => theme.primary};
    }
    
    .result-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
      
      .result-author {
        font-weight: 500;
        color: ${({ theme }) => theme.textMain};
      }
      
      .result-time {
        font-size: 11px;
        color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
      }
    }
    
    .result-content {
      font-size: 13px;
      color: ${({ theme }) => theme.textMain};
      line-height: 1.4;
      
      .highlight {
        background-color: ${({ theme }) => theme.primary}30;
        color: ${({ theme }) => theme.primary};
        padding: 1px 2px;
        border-radius: 2px;
      }
    }
    
    .result-channel {
      font-size: 11px;
      color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
      margin-top: 4px;
      
      &::before {
        content: '# ';
      }
    }
  }
`;

export const NotificationBanner = styled.div<{ $type: 'info' | 'warning' | 'error' }>`
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  
  ${({ $type, theme }) => {
    switch ($type) {
      case 'info':
        return css`
          background-color: #e6f7ff;
          color: #1890ff;
          border-bottom: 1px solid #91d5ff;
        `;
      case 'warning':
        return css`
          background-color: #fffbe6;
          color: #faad14;
          border-bottom: 1px solid #ffe58f;
        `;
      case 'error':
        return css`
          background-color: #fff2f0;
          color: #ff4d4f;
          border-bottom: 1px solid #ffccc7;
        `;
      default:
        return '';
    }
  }}
`;

export const UserTyping = styled.div`
  padding: 4px 16px;
  font-size: 11px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-style: italic;
  border-top: 1px solid ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
  background-color: ${({ theme }) => theme.background === '#ffffff' ? '#fafafa' : '#141414'};
  
  .typing-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    
    .dots {
      display: flex;
      gap: 1px;
      
      span {
        width: 3px;
        height: 3px;
        background-color: currentColor;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
        
        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
      }
    }
  }

  @keyframes typing {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export const FloatingButton = styled.button`
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background-color: ${({ theme }) => theme.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  z-index: 1000;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
`;

// Responsive styles
export const ResponsiveContainer = styled.div`
  @media (max-width: 768px) {
    ${ChannelSidebar} {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      z-index: 100;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &.open {
        transform: translateX(0);
      }
    }
    
    ${ChatMain} {
      width: 100%;
    }
  }
`;

export const MobileOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  
  @media (min-width: 769px) {
    display: none;
  }
`;
