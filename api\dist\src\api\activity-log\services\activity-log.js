'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * activity-log service
 */
const strapi_1 = require("@strapi/strapi");
// No necesitamos definir un tipo personalizado para las opciones
// ya que Strapi proporciona sus propios tipos
exports.default = strapi_1.factories.createCoreService('api::activity-log.activity-log', ({ strapi }) => ({
    /**
     * Registrar una actividad de usuario
     * @param {ActivityLogData} data - Datos de la actividad
     * @param {number} userId - ID del usuario
     */
    async logActivity(data, userId) {
        try {
            // Verificar si el usuario existe
            const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId);
            if (!user) {
                throw new Error(`Usuario con ID ${userId} no encontrado`);
            }
            // Crear el registro de actividad
            const activity = await strapi.entityService.create('api::activity-log.activity-log', {
                data: {
                    action: data.action,
                    method: (data.method || 'GET'),
                    route: data.route || '',
                    description: data.description || `El usuario realizó la acción: ${data.action}`,
                    ip: data.ip || '0.0.0.0',
                    user: userId,
                    metadata: data.metadata || {},
                    createdAt: new Date()
                }
            });
            return activity;
        }
        catch (error) {
            console.error('Error al registrar actividad:', error);
            return null;
        }
    },
    /**
     * Obtener las actividades recientes de un usuario
     * @param {number} userId - ID del usuario
     * @param {number} limit - Número máximo de actividades a retornar
     */
    async getRecentActivities(userId, limit = 5) {
        // Usar un casting de tipo para evitar errores de TypeScript
        // Esto permite que TypeScript acepte nuestras opciones sin errores
        const defaultOptions = {
            filters: {
                user: { id: userId }
            },
            sort: ['createdAt:desc'],
            populate: { user: true }
        };
        try {
            const activities = await strapi.entityService.findMany('api::activity-log.activity-log', defaultOptions);
            return activities.slice(0, limit);
        }
        catch (error) {
            console.error('Error al obtener actividades recientes:', error);
            return [];
        }
    },
    /**
     * Obtener las actividades de los arrendatarios de un usuario
     * @param {number} userId - ID del usuario principal
     */
    async getTenantsActivities(userId) {
        try {
            // Obtener el usuario con sus arrendatarios
            const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId, { populate: ['arrendatarios'] });
            const userExtended = user;
            if (!userExtended || !userExtended.arrendatarios || userExtended.arrendatarios.length === 0) {
                return [];
            }
            // Obtener los IDs de los arrendatarios
            const tenantIds = userExtended.arrendatarios.map((tenant) => tenant.id);
            // Buscar las actividades de todos los arrendatarios
            // Usar el mismo enfoque de casting para evitar errores de TypeScript
            const activities = await strapi.entityService.findMany('api::activity-log.activity-log', {
                filters: {
                    user: {
                        id: {
                            $in: tenantIds
                        }
                    }
                },
                sort: ['createdAt:desc'],
                populate: { user: true }
            });
            return activities;
        }
        catch (error) {
            console.error('Error al obtener actividades de arrendatarios:', error);
            return [];
        }
    }
}));
