import { Card, Space, Typography, Divider } from "antd";
import { NotificationOutlined, TeamOutlined, UserOutlined } from "@ant-design/icons";
import { BulkNotificationButton } from "../BulkNotificationButton";
import { useAppSelector } from "@app/hooks/reduxHooks";

const { Title, Text } = Typography;

export const AdminNotificationPanel = () => {
  const user = useAppSelector((state) => state.user.user);

  // Verificar si el usuario tiene permisos
  const hasPermission = () => {
    if (!user?.role?.type) return false;
    const allowedRoles = ["admin", "consejero", "vigilante"];
    return allowedRoles.includes(user.role.type.toLowerCase());
  };

  if (!hasPermission()) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <NotificationOutlined />
          <span>Panel de Notificaciones</span>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <div>
          <Title level={5} style={{ margin: 0 }}>
            Comunicación Masiva
          </Title>
          <Text type="secondary">
            Envía notificaciones importantes a todos los usuarios o grupos específicos
          </Text>
        </div>

        <Divider style={{ margin: "12px 0" }} />

        <Space wrap>
          <BulkNotificationButton type="primary" />
          
          <Space>
            <TeamOutlined />
            <Text type="secondary">
              Disponible para: Administradores, Consejeros y Vigilantes
            </Text>
          </Space>
        </Space>

        <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
          <UserOutlined style={{ marginRight: 4 }} />
          Las notificaciones aparecerán en tiempo real para todos los usuarios conectados
        </div>
      </Space>
    </Card>
  );
};
