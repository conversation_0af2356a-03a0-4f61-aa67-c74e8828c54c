/**
 * Script para crear usuarios con roles en Strapi
 *
 * <PERSON>ste script ejecuta un archivo SQL que contiene las instrucciones para crear
 * usuarios y asignar roles en la base de datos.
 */
const mysql = require("mysql2/promise");
const fs = require("fs").promises;
const path = require("path");
require("dotenv").config();
// Configuración de la base de datos desde variables de entorno
const dbConfig = {
    host: process.env.DATABASE_HOST || "localhost",
    port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    multipleStatements: true // Necesario para ejecutar múltiples statements SQL
};
// Función para mostrar el banner
function mostrarBanner() {
    console.log('\n===========================================================');
    console.log('🌟 INICIALIZADOR DE USUARIOS - PALMAS PRODUCCIÓN 🌟');
    console.log('===========================================================\n');
    console.log('Este script ejecutará las instrucciones SQL para crear usuarios');
    console.log('y asignar los roles correspondientes.\n');
    console.log('===========================================================\n');
}
/**
 * Función principal para ejecutar el seeding de usuarios
 */
async function seedUsers() {
    let connection;
    try {
        // Leer el archivo SQL
        const sqlFilePath = path.join(__dirname, 'seed-users.sql');
        const sqlContent = await fs.readFile(sqlFilePath, 'utf8');
        // Conectar a la base de datos
        console.log('\n🔌 Conectando a la base de datos...');
        connection = await mysql.createConnection(dbConfig);
        // Ejecutar las instrucciones SQL
        console.log('\n📝 Ejecutando instrucciones SQL...');
        await connection.query(sqlContent);
        console.log('\n✅ Usuarios creados exitosamente!');
        console.log('\n🎉 El proceso de seeding ha finalizado correctamente.\n');
    }
    catch (error) {
        console.error('\n❌ Error durante el proceso de seeding:', error);
        process.exit(1);
    }
    finally {
        // Cerrar la conexión si existe
        if (connection) {
            await connection.end();
        }
    }
}
// Ejecutar el script
mostrarBanner();
seedUsers();
