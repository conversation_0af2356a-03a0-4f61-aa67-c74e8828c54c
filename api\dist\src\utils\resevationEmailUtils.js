"use strict";
// ✅ emailUtils.ts actualizado con EJS y sin createReadStream
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resevationEmailUtils = void 0;
const ejs_1 = __importDefault(require("ejs"));
const path_1 = require("path");
exports.resevationEmailUtils = {
    getConfig() {
        return {
            from: process.env.SMTP_DEFAULT_FROM,
            fromName: process.env.SMTP_FROM_NAME || "Administración CCPALCAN",
            replyTo: process.env.SMTP_REPLY_TO || process.env.SMTP_DEFAULT_FROM,
            baseUrl: process.env.FRONTEND_URL || "https://ccpalcan.com",
        };
    },
    validateConfig() {
        const errors = [];
        const config = this.getConfig();
        if (!config.from)
            errors.push("Falta SMTP_DEFAULT_FROM");
        return { isValid: errors.length === 0, errors };
    },
    getTemplatePath(type) {
        const map = {
            created: "reservation-created.html",
            approved: "reservation-approved.html",
            rejected: "reservation-rejected.html",
        };
        return (0, path_1.join)(process.cwd(), "config", "email-templates", map[type]);
    },
    async processTemplateEJS(templatePath, data) {
        return new Promise((resolve, reject) => {
            ejs_1.default.renderFile(templatePath, data, { async: true }, (err, str) => {
                if (err) {
                    console.error("Error procesando plantilla EJS:", err);
                    reject(err);
                }
                else {
                    resolve(str);
                }
            });
        });
    },
    async sendEmail(type, data, recipientEmail, maxRetries = 3) {
        const { isValid, errors } = this.validateConfig();
        if (!isValid) {
            console.error("Configuración de email inválida:", errors.join(", "));
            if (process.env.NODE_ENV === "development") {
                console.log("[DEV] Simulando envío de email:", {
                    type,
                    to: recipientEmail,
                    data,
                });
                return;
            }
            throw new Error(`Configuración SMTP incompleta: ${errors.join(", ")}`);
        }
        const config = this.getConfig();
        const subjectMap = {
            created: "Nueva solicitud de reservación - CCPALCAN",
            approved: "Reservación aprobada - CCPALCAN",
            rejected: "Reservación rechazada - CCPALCAN",
        };
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const templatePath = this.getTemplatePath(type);
                const htmlContent = await this.processTemplateEJS(templatePath, {
                    ...data,
                    URL: config.baseUrl,
                });
                await strapi.plugins.email.services.email.send({
                    to: recipientEmail,
                    from: {
                        email: config.from,
                        name: config.fromName,
                    },
                    replyTo: config.replyTo,
                    subject: subjectMap[type],
                    html: htmlContent,
                    text: htmlContent.replace(/<[^>]*>/g, ""),
                });
                console.log(`Email de ${type} enviado exitosamente a ${recipientEmail}`);
                return;
            }
            catch (err) {
                lastError = err;
                console.error(`Error en envío de email (intento ${attempt}/${maxRetries}):`, err);
                if (attempt < maxRetries) {
                    await new Promise((res) => setTimeout(res, Math.min(1000 * 2 ** (attempt - 1), 10000)));
                }
            }
        }
        console.error("Todos los intentos fallaron:", lastError);
        const fallbackHtml = this.generateFallbackEmail(type, data);
        await strapi.plugins.email.services.email.send({
            to: recipientEmail,
            from: {
                email: config.from,
                name: config.fromName,
            },
            subject: subjectMap[type],
            html: fallbackHtml,
            text: fallbackHtml.replace(/<[^>]*>/g, ""),
        });
        console.log(`Fallback email enviado a ${recipientEmail}`);
    },
    generateFallbackEmail(type, data) {
        var _a, _b, _c, _d, _e;
        return `
      <h1>Notificación de Reservación</h1>
      <p>Se ha ${type === "created"
            ? "creado"
            : type === "approved"
                ? "aprobado"
                : "rechazado"} una reservación.</p>
      <p><strong>Área:</strong> ${data.area || data.socialArea || ((_a = data.reservation) === null || _a === void 0 ? void 0 : _a.area) || ""}</p>
      <p><strong>Fecha:</strong> ${data.date || ((_b = data.reservation) === null || _b === void 0 ? void 0 : _b.date) || ""}</p>
      <p><strong>Horario:</strong> ${data.startTime || ((_c = data.reservation) === null || _c === void 0 ? void 0 : _c.startTime) || ""} - ${data.endTime || ((_d = data.reservation) === null || _d === void 0 ? void 0 : _d.endTime) || ""}</p>
      ${type === "rejected"
            ? `<p><strong>Motivo de rechazo:</strong> ${data.rejectionReason || ((_e = data.reservation) === null || _e === void 0 ? void 0 : _e.rejectionReason) || ""}</p>`
            : ""}
      <hr>
      <p><small>Email de respaldo generado automáticamente.</small></p>
    `;
    },
};
