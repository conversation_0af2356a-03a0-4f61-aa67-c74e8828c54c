import { API_URL } from '../config/api';

export interface Message {
  id: number | string;
  description: string;
  title?: string;
  type?: string;
  read?: boolean;
  createdAt?: string;
  data?: any;
}

export interface Mention extends Message {
  userName: string;
  userIcon: string;
  place: string;
  href: string;
}

export type Notification = Mention | Message;

// Notificaciones de ejemplo para desarrollo
export const notifications = [
  {
    id: 2,
    title: 'Intento de inicio de sesión',
    description: 'Hubo un intento de inicio de sesión en tu cuenta',
    type: 'warning',
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 1,
    title: 'Pago exitoso',
    description: 'Tu pago ha sido procesado correctamente',
    type: 'success',
    read: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: 3,
    title: 'Error del servidor',
    description: 'Se ha producido un error en el servidor',
    type: 'error',
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 4,
    description: 'Te ha mencionado en un comentario',
    title: 'Nueva mención',
    type: 'info',
    read: false,
    createdAt: new Date().toISOString(),
    userName: '<PERSON>',
    userIcon:
      'https://res.cloudinary.com/lapkinthegod/image/upload/v1629187274/young-male-doctor-white-uniform_x7dcrs.jpg',
    place: 'dashboard.latestScreenings.title',
    href: `/#latest-screenings`,
  },
];

/**
 * Obtiene las notificaciones de un usuario
 * @param userId ID del usuario
 * @param token Token de autenticación
 * @returns Lista de notificaciones
 */
export const getUserNotifications = async (userId: string, token: string): Promise<Notification[]> => {
  try {
    const response = await fetch(`${API_URL}/api/notifications/user/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      return await response.json();
    }
    
    return [];
  } catch (error) {
    console.error('Error al obtener notificaciones:', error);
    return [];
  }
};

/**
 * Marca una notificación como leída
 * @param notificationId ID de la notificación
 * @param token Token de autenticación
 * @returns True si se marcó correctamente
 */
export const markNotificationAsRead = async (notificationId: string, token: string): Promise<boolean> => {
  try {
    const response = await fetch(`${API_URL}/api/notifications/${notificationId}/read`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error al marcar notificación como leída:', error);
    return false;
  }
};

/**
 * Marca todas las notificaciones de un usuario como leídas
 * @param userId ID del usuario
 * @param token Token de autenticación
 * @returns True si se marcaron correctamente
 */
export const markAllNotificationsAsRead = async (userId: string, token: string): Promise<boolean> => {
  try {
    const response = await fetch(`${API_URL}/api/notifications/user/${userId}/read-all`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error al marcar todas las notificaciones como leídas:', error);
    return false;
  }
};

/**
 * Envía una notificación a un usuario
 * @param userId ID del usuario destinatario
 * @param notification Datos de la notificación
 * @param token Token de autenticación
 * @returns True si se envió correctamente
 */
export const sendNotificationToUser = async (
  userId: string, 
  notification: { title: string; description: string; type: string; data?: any },
  token: string
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_URL}/api/notifications/user/${userId}/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(notification)
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error al enviar notificación:', error);
    return false;
  }
};
