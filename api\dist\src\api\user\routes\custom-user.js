"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: "PUT",
            path: "/users/:id/avatar",
            handler: "custom-user.updateImgUrl",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [], // Opcional: define middlewares
            },
        },
        {
            method: "GET",
            path: "/users/me/signature",
            handler: "custom-user.getSignature",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/users/me/signature",
            handler: "custom-user.updateSignature",
            config: {
                policies: ["global::isAuthenticated"],
                middlewares: [],
            },
        },
    ],
};
