/**
 * Estilos para el componente ChatMessageList
 */

import styled from 'styled-components';

export const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  
  /* Estilos para el scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#d9d9d9' : '#434343'};
    border-radius: 3px;
    
    &:hover {
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#bfbfbf' : '#595959'};
    }
  }

  /* Scroll suave */
  scroll-behavior: smooth;
`;

export const LoadingContainer = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
`;

export const EmptyContainer = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
`;

export const DateSeparator = styled.div`
  margin: 16px 0 8px 0;
  
  .ant-divider {
    margin: 0;
    
    .ant-divider-inner-text {
      padding: 0 12px;
      background-color: ${({ theme }) => theme.background};
      font-weight: 500;
      text-transform: capitalize;
    }
  }
`;

export const LoadingMore = styled.div`
  display: flex;
  justify-content: center;
  padding: 16px;
`;

export const MessageGroup = styled.div`
  margin-bottom: 8px;
`;

export const ScrollToBottomButton = styled.button`
  position: absolute;
  bottom: 80px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: ${({ theme }) => theme.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
`;

export const UnreadIndicator = styled.div`
  position: sticky;
  top: 0;
  z-index: 5;
  background-color: ${({ theme }) => theme.error};
  color: white;
  text-align: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  margin: 8px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const TypingIndicator = styled.div`
  padding: 8px 16px;
  color: ${({ theme }) => theme.textSecondary || '#8c8c8c'};
  font-size: 12px;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8px;

  .typing-dots {
    display: flex;
    gap: 2px;
    
    span {
      width: 4px;
      height: 4px;
      background-color: currentColor;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }

  @keyframes typing {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export const MessageSkeleton = styled.div`
  padding: 8px 16px;
  display: flex;
  gap: 8px;
  
  .avatar-skeleton {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  .content-skeleton {
    flex: 1;
    
    .header-skeleton {
      height: 16px;
      width: 120px;
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
      border-radius: 4px;
      margin-bottom: 4px;
      animation: pulse 1.5s ease-in-out infinite;
    }
    
    .text-skeleton {
      height: 14px;
      background-color: ${({ theme }) => theme.background === '#ffffff' ? '#f0f0f0' : '#2a2a2a'};
      border-radius: 4px;
      animation: pulse 1.5s ease-in-out infinite;
      
      &.short { width: 60%; }
      &.medium { width: 80%; }
      &.long { width: 95%; }
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
`;

export const JumpToMessage = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: ${({ theme }) => theme.primary};
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translate(-50%, -50%) scale(1.05);
  }
`;

export const MessageHighlight = styled.div`
  background-color: ${({ theme }) => theme.primary}20;
  border-left: 3px solid ${({ theme }) => theme.primary};
  animation: highlight 2s ease-out;

  @keyframes highlight {
    0% {
      background-color: ${({ theme }) => theme.primary}40;
    }
    100% {
      background-color: ${({ theme }) => theme.primary}20;
    }
  }
`;
