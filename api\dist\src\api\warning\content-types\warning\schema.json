{"kind": "collectionType", "collectionName": "warnings", "info": {"singularName": "warning", "pluralName": "warnings", "displayName": "Warning", "description": "Registro de advertencias y llamados de atención a residentes"}, "options": {"draftAndPublish": false}, "attributes": {"date": {"type": "date", "required": true, "description": "<PERSON>cha de la advertencia"}, "time": {"type": "time", "required": true, "description": "Hora de la advertencia"}, "type": {"type": "enumeration", "enum": ["attention_call_1", "attention_call_2", "sanction"], "required": true, "description": "Tipo de advertencia"}, "content": {"type": "text", "description": "Contenido detallado de la advertencia"}, "description": {"type": "text", "description": "Descripción adicional de la advertencia"}, "evidencePhotos": {"type": "media", "multiple": true, "allowedTypes": ["images", "videos"], "description": "Evidencias fotográficas o videos de la infracción"}, "rules": {"type": "relation", "relation": "manyToMany", "target": "api::warning-rule.warning-rule", "mappedBy": "warnings", "required": true, "description": "Reglas infringidas"}, "notifications": {"type": "component", "component": "warning.notification-config", "description": "Configuración de notificaciones"}, "resident": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "description": "Residente al que se le aplica la advertencia", "required": true}, "sanctionBy": {"type": "component", "component": "warning.sanction-by", "required": true, "description": "Usuario que crea la sanción"}}}