'use strict';
const templates = require('./content/templates.json');
module.exports = async ({ strapi }) => {
    const existingTemplates = await strapi.entityService.findMany('api::complaint-template.complaint-template', {
        filters: {
            title: {
                $in: templates.templates.map(t => t.title)
            }
        }
    });
    const existingTitles = existingTemplates.map(t => t.title);
    for (const template of templates.templates) {
        if (!existingTitles.includes(template.title)) {
            await strapi.entityService.create('api::complaint-template.complaint-template', {
                data: {
                    ...template,
                    isActive: true,
                    publishedAt: new Date().toISOString()
                }
            });
            console.log(`Created template: ${template.title}`);
        }
    }
};
