import { MouseEvent, useMemo } from "react";
import { Avatar, Space, Popconfirm } from "antd";
import {
  InfoCircleFilled,
  WarningFilled,
  CheckCircleFilled,
  CloseCircleFilled,
  DeleteOutlined,
} from "@ant-design/icons";
import * as S from "./BaseNotification.styles";

interface Icons {
  info: React.ReactNode;
  success: React.ReactNode;
  warning: React.ReactNode;
  error: React.ReactNode;
  mention: React.ReactNode;
}

export type NotificationType =
  | "info"
  | "mention"
  | "success"
  | "warning"
  | "error";

interface BaseNotificationProps {
  type: NotificationType;
  title: React.ReactNode;
  description?: React.ReactNode;
  mentionIconSrc?: React.ReactNode;
  onClick?: () => void;
  read?: boolean;
  onDelete?: () => void;
  timestamp?: string;
}

const BaseNotification = ({
  type,
  title,
  description,
  mentionIconSrc,
  onClick,
  read = false,
  onDelete,
  timestamp,
}: BaseNotificationProps) => {
  const notificationIcon = useMemo(() => {
    switch (type) {
      case "warning":
        return <WarningFilled />;
      case "success":
        return <CheckCircleFilled />;
      case "error":
        return <CloseCircleFilled />;
      case "info":
      default:
        return <InfoCircleFilled />;
    }
  }, [type]);

  const icons: Icons = {
    info: <InfoCircleFilled />,
    success: <CheckCircleFilled />,
    warning: <WarningFilled />,
    error: <CloseCircleFilled />,
    mention: mentionIconSrc,
  };

  const handleClick = (e?: MouseEvent) => {
    onClick && onClick();
  };

  const stopPropagation = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
  };

  return (
    <S.Wrapper $type={type} $read={read} onClick={handleClick}>
      <Space align="start">
        <Avatar icon={mentionIconSrc || notificationIcon} shape="square" />
        <S.NotificationContent>
          <S.Title $type={type}>{title}</S.Title>
          {description && <S.Description>{description}</S.Description>}
          {timestamp && <S.Timestamp>{timestamp}</S.Timestamp>}
        </S.NotificationContent>
        {onDelete && (
          <Popconfirm
            title="¿Eliminar notificación?"
            description="Esta acción no se puede deshacer"
            onConfirm={onDelete}
            okText="Sí"
            cancelText="No"
            placement="left"
          >
            <S.DeleteButton
              icon={<DeleteOutlined />}
              type="text"
              size="small"
              onClick={stopPropagation}
            />
          </Popconfirm>
        )}
      </Space>
    </S.Wrapper>
  );
};

export { BaseNotification };
export type { BaseNotificationProps };
