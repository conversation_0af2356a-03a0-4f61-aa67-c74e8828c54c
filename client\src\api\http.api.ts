import axios, { AxiosError } from "axios";
import { ApiError } from "@app/api/ApiError";
import { readToken } from "@app/services/localStorage.service";

export const httpApi = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
});

httpApi.interceptors.request.use((config) => {
  const headers = config.headers;
  headers.set("Authorization", `Bearer ${readToken()}`);
  return config;
});

httpApi.interceptors.response.use(undefined, (error: AxiosError) => {
  if (error.response) {
    const responseData = error.response.data as ApiErrorData;
    console.error("Detalles del error:", responseData);
    throw new ApiError<ApiErrorData>(
      responseData.message || error.message,
      responseData
    );
  } else {
    console.error("Error de red:", error.message);
    throw new ApiError<ApiErrorData>(error.message, undefined);
  }
});

export interface ApiErrorData {
  message: string;
}
