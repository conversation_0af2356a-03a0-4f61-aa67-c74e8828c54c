import { Http } from "@app/config/http";
import { useFeedback } from "@app/hooks/useFeedback";
import { useMutation } from "@tanstack/react-query";

export interface IPayload {
  email: string;
  password: string;
}

export const useSignIn = () => {
  const { notification } = useFeedback();
  const { success, error } = notification;

  const { mutate, ...rest } = useMutation({
    mutationKey: ["SIGNIN"],
    mutationFn: ({ email, password }: IPayload) =>
      Http.post("users/sign-in", { email, password }).then(({ data }) => data),
    onSuccess: () => {
      success({ message: "Éxi<PERSON>", description: "Bienvenido" });
    },
    onError: () => {
      return error({
        message: "Error",
        description: "Error al iniciar",
      });
    },
  });

  return { signIn: mutate, ...rest };
};
