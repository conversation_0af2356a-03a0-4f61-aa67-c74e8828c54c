"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Configuración de plugins para la aplicación
 * Versión consolidada que incluye todas las configuraciones necesarias
 */
exports.default = ({ env }) => ({
    upload: {
        config: {
            provider: "cloudinary",
            providerOptions: {
                cloud_name: env("CLOUDINARY_NAME"),
                api_key: env("CLOUDINARY_KEY"),
                api_secret: env("CLOUDINARY_SECRET"),
            },
            actionOptions: {
                upload: {},
                delete: {},
            },
        },
    },
    "users-permissions": {
        config: {
            register: {
                allowedFields: ["firstName", "lastName", "imgUrl"],
            },
            update: {
                allowedFields: ["firstName", "lastName", "imgUrl"],
            },
            rest: {
                defaultLimit: 25,
                maxLimit: 100,
            },
            jwt: {
                expiresIn: "7d",
            },
            email: {
                confirmation: {
                    url: env("CONFIRMATION_URL"),
                },
                resetPassword: {
                    url: env("RESET_PASSWORD_URL"),
                },
            },
        },
    },
    email: {
        config: {
            provider: "sendgrid",
            providerOptions: {
                apiKey: env("SENDGRID_API_KEY"),
            },
            settings: {
                defaultFrom: env("SENDGRID_EMAIL") || "<EMAIL>",
                defaultReplyTo: env("SENDGRID_REPLY_TO") || "<EMAIL>",
                defaultName: "Administración CCPALCAN",
                retryAttempts: parseInt(env("EMAIL_RETRY_ATTEMPTS") || "3", 10),
                retryDelay: parseInt(env("EMAIL_RETRY_DELAY") || "1000", 10),
                templates: {
                    reservation: {
                        defaultSubject: "Notificación de Reservación - CCPALCAN",
                    },
                    payment: {
                        defaultSubject: "Notificación de Pago - CCPALCAN",
                    },
                    announcement: {
                        defaultSubject: "Comunicado Importante - CCPALCAN",
                    },
                    security: {
                        defaultSubject: "Alerta de Seguridad - CCPALCAN",
                    },
                },
            },
        },
    },
});
