"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: "GET",
            path: "/notifications/user/:userId",
            handler: "notification.findByUser",
            config: {
                auth: {
                    scope: ["api::notification.notification.findByUser"],
                },
            },
        },
        {
            method: "POST",
            path: "/notifications/user/:userId/send",
            handler: "notification.sendToUser",
            config: {
                auth: {
                    scope: ["api::notification.notification.sendToUser"],
                },
            },
        },
        {
            method: "PUT",
            path: "/notifications/:id/read",
            handler: "notification.markAsRead",
            config: {
                auth: {
                    scope: ["api::notification.notification.markAsRead"],
                },
            },
        },
        {
            method: "PUT",
            path: "/notifications/user/:userId/read-all",
            handler: "notification.markAllAsRead",
            config: {
                auth: {
                    scope: ["api::notification.notification.markAllAsRead"],
                },
            },
        },
        {
            method: "DELETE",
            path: "/notifications/:id",
            handler: "notification.deleteNotification",
            config: {
                auth: {
                    scope: ["api::notification.notification.deleteNotification"],
                },
            },
        },
    ],
};
