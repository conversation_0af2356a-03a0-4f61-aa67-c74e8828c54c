import { Http } from "@app/config/http";
import { useMutation } from "@tanstack/react-query";

export interface IPayload {
  firstName: string;
  secondName?: string;
  lastName: string;
  secondLastName?: string;
  email: string;
  password: string;
  country: string;
}

export interface IHookSProps {
  onSuccess?: (values: any) => void;
}

export const useSignUp = ({ onSuccess }: IHookSProps) => {
  const { mutate, ...rest } = useMutation({
    mutationKey: ["SIGNUP"],
    mutationFn: (payload: IPayload) =>
      Http.post("users/sign-up", payload).then(({ data }) => data),
    onSuccess,
  });

  return { signUp: mutate, ...rest };
};
